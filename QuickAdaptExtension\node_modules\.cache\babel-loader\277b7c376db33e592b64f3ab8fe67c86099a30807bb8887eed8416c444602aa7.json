{"ast": null, "code": "import React,{useState,useEffect,useRef,useContext}from\"react\";import{<PERSON>,Typo<PERSON>,TextField,IconButton,Button,FormControl,Select,MenuItem,Tooltip}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{deleteicon,chkicn1,chkicn2,chkicn3,chkicn4,chkicn5,chkicn6,deletestep,redirect,warning}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import CloudUploadOutlinedIcon from'@mui/icons-material/CloudUploadOutlined';import{AccountContext}from\"../login/AccountContext\";import{getAllGuides}from\"../../services/GuideListServices\";import{useTranslation}from'react-i18next';import'../../styles/rtl_styles.scss';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckPointEditPopup=_ref=>{var _checklistGuideMetaDa,_checklistGuideMetaDa2,_checklistGuideMetaDa3,_checklistCheckpointP2;let{editInteractionName,checkpointsEditPopup}=_ref;const{t:translate}=useTranslation();const{setCheckPointsEditPopup,titlePopup,setTitlePopup,setDesignPopup,titleColor,setTitleColor,checkpointsPopup,setCheckPointsPopup,checkpointTitleColor,setCheckpointTitleColor,checkpointTitleDescription,setCheckpointTitleDescription,checkpointIconColor,setCheckpointIconColor,setUnlockCheckPointInOrder,unlockCheckPointInOrder,checkPointMessage,setCheckPointMessage,checklistGuideMetaData,updateChecklistCheckPointItem,setIsUnSavedChanges,isUnSavedChanges}=useDrawerStore(state=>state);const data=checklistGuideMetaData[0].checkpoints.checkpointsList.find(k=>k.id===editInteractionName);const encodeToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};const[icons,setIcons]=useState(()=>{return[{id:1,base64:encodeToBase64(chkicn1),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn1},style:{zoom:1,display:\"flex\"}}),selected:false},{id:2,base64:encodeToBase64(chkicn2),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn2},style:{zoom:1,display:\"flex\"}}),selected:false},{id:3,base64:encodeToBase64(chkicn3),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn3},style:{zoom:1,display:\"flex\"}}),selected:false},{id:4,base64:encodeToBase64(chkicn4),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn4},style:{zoom:1,display:\"flex\"}}),selected:false},{id:5,base64:encodeToBase64(chkicn5),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn5},style:{zoom:1,display:\"flex\"}}),selected:false},{id:6,base64:encodeToBase64(chkicn6),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn6},style:{zoom:1,display:\"flex\"}}),selected:false}];});const[checklistCheckpointProperties,setChecklistCheckpointProperties]=useState(()=>{const initialchecklistCheckpointProperties={interaction:data===null||data===void 0?void 0:data.interaction,title:data===null||data===void 0?void 0:data.title,description:data===null||data===void 0?void 0:data.description,redirectURL:data===null||data===void 0?void 0:data.redirectURL,icon:(data===null||data===void 0?void 0:data.icon)||icons[0].component,supportingMedia:(data===null||data===void 0?void 0:data.supportingMedia)||[],mediaTitle:data===null||data===void 0?void 0:data.mediaTitle,mediaDescription:data===null||data===void 0?void 0:data.mediaDescription,id:data===null||data===void 0?void 0:data.id};return initialchecklistCheckpointProperties;});const handleCheckPointIconColorChange=e=>setCheckpointIconColor(e.target.value);const handleCheckPointTitleColorChange=e=>setCheckpointTitleColor(e.target.value);const handleCheckPointDescriptionColorChange=e=>setCheckpointTitleColor(e.target.value);const[error,setError]=useState(null);useEffect(()=>{if(checkpointsEditPopup&&checklistCheckpointProperties.icon){setIcons(prevIcons=>prevIcons.map(icon=>({...icon,selected:icon.base64===checklistCheckpointProperties.icon// Compare Base64 strings directly\n})));}},[checkpointsEditPopup,checklistCheckpointProperties.icon]);const handleIconClick=id=>{setIcons(prevIcons=>prevIcons.map(icon=>({...icon,selected:icon.id===id})));const selectedIcon=icons.find(icon=>icon.id===id);if(selectedIcon){setChecklistCheckpointProperties(prev=>({...prev,icon:selectedIcon.base64// Save Base64 instead of component\n}));}};const handleFileUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(!file)return;const isIco=file.name.endsWith(\".ico\");// Validate the file type and size\nconst img=new Image();img.src=URL.createObjectURL(file);img.onload=()=>{if(!isIco||img.width>64||img.height>64){setError(\"Please upload an .ico file less than 64x64px\");}else{setError(null);setIcons(prevIcons=>[...prevIcons,{id:prevIcons.length+1,component:/*#__PURE__*/_jsx(\"img\",{src:img.src,alt:\"Custom Icon\",width:24}),selected:false}]);}};};const handleClose=()=>{setCheckPointsEditPopup(false);};const handledesignclose=()=>{setDesignPopup(false);};const handleSizeChange=value=>{const sizeInPx=16+(value-1)*4;onPropertyChange(\"Size\",sizeInPx);};const onReselectElement=()=>{};const onPropertyChange=(key,value)=>{setChecklistCheckpointProperties(prevState=>({...prevState,[key]:value}));};const handleApplyChanges=()=>{setFileError(null);updateChecklistCheckPointItem(checklistCheckpointProperties);handleClose();setIsUnSavedChanges(true);};const handleEditClick=()=>{setCheckPointsEditPopup(true);};const[interactions,setInteractions]=useState([]);const[skip,setSkip]=useState(0);const[loading,setLoading]=useState(false);const[hasMore,setHasMore]=useState(true);const dropdownRef=useRef(null);const{accountId}=useContext(AccountContext);// Set number of items to fetch per request to 10\nconst top=10;// Initial data fetch\nuseEffect(()=>{fetchData(0);// Load first 10 guides\n},[]);const fetchData=async newSkip=>{if(loading||!hasMore)return;// Prevent duplicate calls or if no more data\nsetLoading(true);const filters=[{FieldName:\"AccountId\",ElementType:\"string\",Condition:\"contains\",Value:accountId,IsCustomField:false}];try{const data=await getAllGuides(newSkip,top,filters,\"\");const newInteractions=(data===null||data===void 0?void 0:data.results)||[];if(newInteractions.length===0){setHasMore(false);}else{setInteractions(prev=>[...prev,...newInteractions]);setSkip(newSkip+top);}}catch(error){console.error(\"Error fetching guides:\",error);}finally{setLoading(false);}};// Handle scroll event for the dropdown menu\nconst handleMenuScroll=event=>{const{scrollTop,scrollHeight,clientHeight}=event.currentTarget;// If scrolled to bottom (with a small buffer)\nif(scrollHeight-scrollTop-clientHeight<50&&!loading&&hasMore){fetchData(skip);}};const[files,setFiles]=useState([]);const[gifFile,setGifFile]=useState(null);const[videoFile,setVideoFile]=useState(null);const[fileError,setFileError]=useState(null);const convertFileToBase64=file=>{return new Promise((resolve,reject)=>{const reader=new FileReader();reader.readAsDataURL(file);reader.onload=()=>resolve(reader.result);reader.onerror=error=>reject(error);});};const convertBase64ToFile=(base64,fileName,fileType)=>{if(!base64){console.error(\"Error: Base64 string is undefined or empty.\");return null;// Return null if invalid input\n}// If the base64 string doesn't contain a comma, it's probably missing the \"data:image/png;base64,\" prefix\nconst base64Data=base64.includes(\",\")?base64.split(\",\")[1]:base64;try{const byteCharacters=atob(base64Data);const byteNumbers=new Array(byteCharacters.length).fill(null).map((_,i)=>byteCharacters.charCodeAt(i));const byteArray=new Uint8Array(byteNumbers);return new File([byteArray],fileName,{type:fileType});}catch(error){console.error(\"Error converting Base64 to File:\",error);return null;}};useEffect(()=>{var _checklistCheckpointP;if(checkpointsEditPopup&&((_checklistCheckpointP=checklistCheckpointProperties.supportingMedia)===null||_checklistCheckpointP===void 0?void 0:_checklistCheckpointP.length)>0){const mediaFiles=checklistCheckpointProperties.supportingMedia.map(media=>{if(typeof media===\"string\"){return null;// Skip if media is a plain string (unexpected case)\n}else if(typeof media===\"object\"&&media.Base64){return convertBase64ToFile(media.Base64,media.Name,media.Type);// ✅ Use actual file name\n}return null;}).filter(file=>file!==null);// ✅ Remove null values\n// ✅ Separate files correctly\nconst imageFiles=mediaFiles.filter(file=>[\"image/jpeg\",\"image/png\",\"image/jpg\"].includes(file.type));const gif=mediaFiles.find(file=>file.type===\"image/gif\")||null;const video=mediaFiles.find(file=>file.type===\"video/mp4\")||null;setFiles(imageFiles);setGifFile(gif);setVideoFile(video);}},[checkpointsEditPopup,checklistCheckpointProperties.supportingMedia]);const handleFileChange=async event=>{setFileError(null);if(!event.target.files)return;const newFiles=Array.from(event.target.files);const base64Files=await Promise.all(newFiles.map(async file=>({Name:file.name,Type:file.type,Base64:await convertFileToBase64(file)})));const fileObjects=base64Files.map(fileData=>convertBase64ToFile(fileData.Base64,fileData.Name,fileData.Type)).filter(file=>file!==null);const newGifs=fileObjects.filter(file=>file.name.toLowerCase().endsWith(\".gif\"));const newVideos=fileObjects.filter(file=>file.name.toLowerCase().endsWith(\".mp4\"));const newImages=fileObjects.filter(file=>[\".png\",\".jpg\",\".jpeg\"].some(ext=>file.name.toLowerCase().endsWith(ext)));// Validate types\nconst allTypes=Array.from(new Set(fileObjects.map(file=>file.type)));if(allTypes.length>1){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}// Case 1: Check if a GIF already exists\nif(gifFile&&newGifs.length>0){setFileError(translate(\"Only one GIF allowed\"));return;}// Case 2: Check if a video already exists\nif(videoFile&&newVideos.length>0){setFileError(translate(\"Only one Video allowed\"));return;}// Case 3: If a GIF exists, prevent uploading any other type\nif(gifFile&&(newVideos.length>0||newImages.length>0)){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}// Case 4: If a video exists, prevent uploading any other type\nif(videoFile&&(newGifs.length>0||newImages.length>0)){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}// Case 5: If images exist, ensure new images are same type\nif(files.length>0&&newImages.length>0){const existingType=files[0].type;const newImageType=newImages[0].type;const isSameType=newImages.every(img=>img.type===existingType);if(!isSameType||newImageType!==existingType){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}}// Case 6: If images exist and GIF/MP4 is being added\nif(files.length>0&&(newGifs.length>0||newVideos.length>0)){setFileError(\"Mixed file formats are not allowed.\");return;}// Set accepted files\nif(newGifs.length>0){setGifFile(newGifs[0]);}if(newVideos.length>0){setVideoFile(newVideos[0]);}if(newImages.length>0){setFiles(prevFiles=>{const updated=[...prevFiles,...newImages];updated.sort((a,b)=>{const aNum=a.name.match(/\\d+/)?parseInt(a.name.match(/\\d+/)[0],10):0;const bNum=b.name.match(/\\d+/)?parseInt(b.name.match(/\\d+/)[0],10):0;return aNum-bNum;});return updated;});}// Update media\nsetChecklistCheckpointProperties(prevState=>{const updatedMedia=[...(prevState.supportingMedia||[]),...base64Files];updatedMedia.sort((a,b)=>{const aNum=a.Name.match(/\\d+/)?parseInt(a.Name.match(/\\d+/)[0],10):0;const bNum=b.Name.match(/\\d+/)?parseInt(b.Name.match(/\\d+/)[0],10):0;return aNum-bNum;});return{...prevState,supportingMedia:updatedMedia};});};const handleDeleteFile=index=>{setFileError(null);setFiles(prevFiles=>{const updated=prevFiles.filter((_,i)=>i!==index);setChecklistCheckpointProperties(prev=>{var _prev$supportingMedia;return{...prev,supportingMedia:((_prev$supportingMedia=prev.supportingMedia)===null||_prev$supportingMedia===void 0?void 0:_prev$supportingMedia.filter((_,i)=>i!==index))||[]};});return updated;});};const handleDeleteGif=()=>{setGifFile(null);setChecklistCheckpointProperties(prev=>{var _prev$supportingMedia2;return{...prev,supportingMedia:((_prev$supportingMedia2=prev.supportingMedia)===null||_prev$supportingMedia2===void 0?void 0:_prev$supportingMedia2.filter(file=>{var _file$Name;return!((_file$Name=file.Name)!==null&&_file$Name!==void 0&&_file$Name.toLowerCase().endsWith(\".gif\"));}))||[]};});};const handleDeleteVideo=()=>{setVideoFile(null);setChecklistCheckpointProperties(prev=>{var _prev$supportingMedia3;return{...prev,supportingMedia:((_prev$supportingMedia3=prev.supportingMedia)===null||_prev$supportingMedia3===void 0?void 0:_prev$supportingMedia3.filter(file=>{var _file$Name2;return!((_file$Name2=file.Name)!==null&&_file$Name2!==void 0&&_file$Name2.toLowerCase().endsWith(\".mp4\"));}))||[]};});};const index=(_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:(_checklistGuideMetaDa2=_checklistGuideMetaDa.checkpoints)===null||_checklistGuideMetaDa2===void 0?void 0:(_checklistGuideMetaDa3=_checklistGuideMetaDa2.checkpointsList)===null||_checklistGuideMetaDa3===void 0?void 0:_checklistGuideMetaDa3.findIndex(i=>i.id===editInteractionName);return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"back\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-title\",children:[translate(\"Step\"),\" \",index+1]}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Interaction\")}),/*#__PURE__*/_jsx(FormControl,{variant:\"outlined\",fullWidth:true,className:\"qadpt-control-input\",sx:{width:\"calc(100% - 13px) !important\",borderRadius:\"12px\",padding:\"0 8px 8px 8px\",margin:\"0 !important\"},children:/*#__PURE__*/_jsx(Select,{displayEmpty:true,disabled:true,value:checklistCheckpointProperties.interaction||\"\",sx:{width:\"100% !important\",borderRadius:\"12px\",backgroundColor:\"#f5f5f5\",padding:\"10px\",color:\"#333\",\".MuiSelect-icon\":{display:\"none\"// Hide the dropdown arrow to keep the read-only feel\n},\"& .MuiOutlinedInput-root\":{\"&:hover\":{borderColor:\"none !important\"},\"&.Mui-focused\":{borderColor:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.MuiInputBase-root\":{height:\"35px !important\"}},children:/*#__PURE__*/_jsx(MenuItem,{value:checklistCheckpointProperties.interaction,children:checklistCheckpointProperties.interaction||translate(\"No Interaction Selected\")})})})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Title\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Step Title\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointProperties.title,onChange:e=>onPropertyChange(\"title\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Description\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Step Desc\"),className:\"qadpt-control-input\",multiline:true,minRows:3,style:{width:\"100%\"},value:checklistCheckpointProperties.description,onChange:e=>onPropertyChange(\"description\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",style:{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",gap:\"5px\",padding:\"0\",marginBottom:\"10px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"#444444\",fontWeight:\"600\"},children:translate(\"Redirect URL\")}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:redirect},style:{display:\"flex\"}}),\" \"]}),/*#__PURE__*/_jsx(Typography,{style:{fontSize:\"11px\",color:\"#8d8d8d\",textAlign:\"left\",padding:\"0\",marginBottom:\"10px\"},children:translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Redirection URL\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointProperties===null||checklistCheckpointProperties===void 0?void 0:checklistCheckpointProperties.redirectURL,onChange:e=>onPropertyChange(\"redirectURL\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Icon\")}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",gap:1,alignItems:\"center\",width:\"-webkit-fill-available\",flexWrap:\"wrap\"},children:icons.map(icon=>/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"Select Icon\"),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIconClick(icon.id),sx:{border:icon.selected?\"2px solid var(--primarycolor)\":\"none\",borderRadius:\"8px\",padding:\"8px\",background:\"#F1ECEC\"},children:icon.component})},icon.id))})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Supporting Media\")}),/*#__PURE__*/_jsxs(\"div\",{style:{width:\"165px\",height:\"auto\",margin:\"0 8px 8px 8px\",display:\"flex\",flexDirection:\"column\",alignItems:\"center\",justifyContent:\"center\",border:\"1px dashed var(--primarycolor)\",borderRadius:\"12px\",padding:\"8px\",background:\"#F1ECEC\",textAlign:\"center\"},children:[/*#__PURE__*/_jsxs(Button,{className:\"qadpt-upload-button\",style:{height:\"auto\",padding:\"0\",width:\"100%\",display:\"flex\",flexDirection:\"row\",// Ensures icon & text are in one line\nalignItems:\"center\",justifyContent:\"center\",gap:\"6px\",color:\"#000\",backgroundColor:\"#F1ECEC\",textTransform:\"capitalize\",boxShadow:\"none\"},variant:\"contained\",component:\"label\",children:[/*#__PURE__*/_jsx(CloudUploadOutlinedIcon,{sx:{zoom:\"1.6\"}}),translate(\"Upload File\"),/*#__PURE__*/_jsx(\"input\",{id:\"file-input\",type:\"file\",multiple:true,accept:\".jpeg, .jpg, .png, .gif, .mp4\"// ✅ Added MP4 support\n,onChange:handleFileChange,style:{display:\"none\"}})]}),/*#__PURE__*/_jsx(Typography,{style:{fontSize:\"12px\",color:\"#A3A3A3\"},children:\".png, .jpg, .gif, .mp4\"})]}),fileError&&/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",color:\"#e6a957\",width:\"-webkit-fill-available\",padding:\"0 8px\",textAlign:\"left\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"4px\",display:\"flex\"},dangerouslySetInnerHTML:{__html:warning}}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"12px\"},children:fileError})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:\"-webkit-fill-available\"},children:[\" \",files.map((file,index)=>/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{borderRadius:\"12px\",padding:\"8px\",margin:\"8px\",backgroundColor:\"#e5dada\"},children:[/*#__PURE__*/_jsx(\"img\",{src:URL.createObjectURL(file),alt:`uploaded-${index}`,style:{width:\"20px\",height:\"20px\",borderRadius:\"5px\"}}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,ml:2,fontSize:\"14px\",wordBreak:\"break-word\"},children:file.name}),/*#__PURE__*/_jsxs(IconButton,{onClick:()=>handleDeleteFile(index),size:\"small\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:\"1\",display:\"flex\"}}),\" \"]})]},index)),gifFile&&/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{borderRadius:\"12px\",padding:\"8px\",margin:\"5px\",backgroundColor:\"#e5dada\"},children:[/*#__PURE__*/_jsx(\"img\",{src:URL.createObjectURL(gifFile),alt:\"uploaded-gif\",style:{width:\"20px\",height:\"20px\",borderRadius:\"5px\"}}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,fontSize:\"14px\",wordBreak:\"break-word\"},children:gifFile.name}),/*#__PURE__*/_jsxs(IconButton,{onClick:handleDeleteGif,size:\"small\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:\"1\",display:\"flex\"}}),\" \"]})]}),videoFile&&/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{border:\"1px solid #0a6\",borderRadius:\"5px\",padding:\"8px\",marginBottom:\"5px\",width:\"196px\",backgroundColor:\"#e6ffe6\"},children:[/*#__PURE__*/_jsxs(\"video\",{width:\"40\",height:\"40\",controls:true,children:[/*#__PURE__*/_jsx(\"source\",{src:URL.createObjectURL(videoFile),type:\"video/mp4\"}),\"Your browser does not support the video tag.\"]}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,ml:2,fontSize:\"14px\",wordBreak:\"break-word\"},children:videoFile.name}),/*#__PURE__*/_jsx(IconButton,{onClick:handleDeleteVideo,size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{zoom:0.7}})})]})]})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Media Title\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Media Title\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointProperties.mediaTitle,onChange:e=>onPropertyChange(\"mediaTitle\",e.target.value)//onChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\n,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",sx:{flexDirection:\"column\",height:\"auto !important\",padding:\"8px !important\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Media Description\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Media Desc\"),className:\"qadpt-control-input\",multiline:true,minRows:3,style:{width:\"100%\"},value:checklistCheckpointProperties.mediaDescription,onChange:e=>{let value=e.target.value;if(value.length>200){value=value.slice(0,200);}onPropertyChange(\"mediaDescription\",value);},helperText:`${((_checklistCheckpointP2=checklistCheckpointProperties.mediaDescription)===null||_checklistCheckpointP2===void 0?void 0:_checklistCheckpointP2.length)||0}/200`,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:\"qadpt-btn\",children:translate(\"Apply\")})})]})});};export default CheckPointEditPopup;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "<PERSON><PERSON><PERSON>", "CloseIcon", "useDrawerStore", "deleteicon", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "deletestep", "redirect", "warning", "ArrowBackIosNewOutlinedIcon", "CloudUploadOutlinedIcon", "AccountContext", "getAllGuides", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "CheckPointEditPopup", "_ref", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checklistCheckpointP2", "editInteractionName", "checkpointsEditPopup", "t", "translate", "setCheckPointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "checklistGuideMetaData", "updateChecklistCheckPointItem", "setIsUnSavedChanges", "isUnSavedChanges", "state", "data", "checkpoints", "checkpointsList", "find", "k", "id", "encodeToBase64", "svgString", "btoa", "icons", "setIcons", "base64", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "selected", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "initialchecklistCheckpointProperties", "interaction", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "handleCheckPointIconColorChange", "e", "target", "value", "handleCheckPointTitleColorChange", "handleCheckPointDescriptionColorChange", "error", "setError", "prevIcons", "map", "handleIconClick", "selectedIcon", "prev", "handleFileUpload", "event", "_event$target$files", "file", "files", "isIco", "name", "endsWith", "img", "Image", "src", "URL", "createObjectURL", "onload", "width", "height", "length", "alt", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "prevState", "handleApplyChanges", "setFileError", "handleEditClick", "interactions", "setInteractions", "skip", "setSkip", "loading", "setLoading", "hasMore", "setHasMore", "dropdownRef", "accountId", "top", "fetchData", "newSkip", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "newInteractions", "results", "console", "handleMenuScroll", "scrollTop", "scrollHeight", "clientHeight", "currentTarget", "setFiles", "gifFile", "setGifFile", "videoFile", "setVideoFile", "fileError", "convertFileToBase64", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "result", "onerror", "convertBase64ToFile", "fileName", "fileType", "base64Data", "includes", "split", "byteCharacters", "atob", "byteNumbers", "Array", "fill", "_", "i", "charCodeAt", "byteArray", "Uint8Array", "File", "type", "_checklistCheckpointP", "mediaFiles", "media", "Base64", "Name", "Type", "filter", "imageFiles", "gif", "video", "handleFileChange", "newFiles", "from", "base64Files", "all", "fileObjects", "fileData", "newGifs", "toLowerCase", "newVideos", "newImages", "some", "ext", "allTypes", "Set", "existingType", "newImageType", "isSameType", "every", "prevFiles", "updated", "sort", "a", "b", "aNum", "match", "parseInt", "bNum", "updatedMedia", "handleDeleteFile", "index", "_prev$supportingMedia", "handleDeleteGif", "_prev$supportingMedia2", "_file$Name", "handleDeleteVideo", "_prev$supportingMedia3", "_file$Name2", "findIndex", "className", "children", "onClick", "size", "sx", "padding", "variant", "fullWidth", "borderRadius", "margin", "displayEmpty", "disabled", "backgroundColor", "color", "borderColor", "border", "marginBottom", "placeholder", "onChange", "InputProps", "endAdornment", "textAlign", "paddingLeft", "multiline", "minRows", "flexDirection", "alignItems", "gap", "fontWeight", "fontSize", "flexWrap", "arrow", "background", "justifyContent", "textTransform", "boxShadow", "multiple", "accept", "marginRight", "flex", "ml", "wordBreak", "controls", "slice", "helperText"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/CheckpointEditPopup.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useRef, useContext } from \"react\";\r\nimport { Box, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n\tdeleteicon,\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tdeletestep,\r\n\tredirect,\r\n\twarning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\nconst CheckPointEditPopup = ({ editInteractionName, checkpointsEditPopup }: { editInteractionName: string; checkpointsEditPopup:any}) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\tsetCheckPointsEditPopup,\r\n\t\t\ttitlePopup,\r\n\t\t\tsetTitlePopup,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttitleColor,\r\n\t\t\tsetTitleColor,\r\n\t\t\tcheckpointsPopup,\r\n\t\t\tsetCheckPointsPopup,\r\n\t\t\tcheckpointTitleColor,\r\n\t\t\tsetCheckpointTitleColor,\r\n\t\t\tcheckpointTitleDescription,\r\n\t\t\tsetCheckpointTitleDescription,\r\n\t\t\tcheckpointIconColor,\r\n\t\t\tsetCheckpointIconColor,\r\n\t\t\tsetUnlockCheckPointInOrder,\r\n\t\t\tunlockCheckPointInOrder,\r\n\t\t\tcheckPointMessage,\r\n\t\t\tsetCheckPointMessage,\r\n\t\t\tchecklistGuideMetaData,\r\n\t\t\tupdateChecklistCheckPointItem,\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tisUnSavedChanges,\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst data = checklistGuideMetaData[0].checkpoints.checkpointsList.find((k: any) => k.id === editInteractionName);\r\n\t\tconst encodeToBase64 = (svgString: string) => {\r\n\t\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t\t};\r\n\r\n\t\tconst [icons, setIcons] = useState<any[]>(() => {\r\n\t\t\treturn [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn1),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn2),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn2 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn3),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn3 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn4),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn4 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 5,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn5),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn5 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 6,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn6),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn6 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t];\r\n\t\t});\r\n\r\n\t\tconst [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {\r\n\t\t\tconst initialchecklistCheckpointProperties = {\r\n\t\t\t\tinteraction: data?.interaction,\r\n\t\t\t\ttitle: data?.title,\r\n\t\t\t\tdescription: data?.description,\r\n\t\t\t\tredirectURL: data?.redirectURL,\r\n\t\t\t\ticon: data?.icon || icons[0].component,\r\n\t\t\t\tsupportingMedia: data?.supportingMedia || [],\r\n\t\t\t\tmediaTitle: data?.mediaTitle,\r\n\t\t\t\tmediaDescription: data?.mediaDescription,\r\n\t\t\t\tid: data?.id,\r\n\t\t\t};\r\n\t\t\treturn initialchecklistCheckpointProperties;\r\n\t\t});\r\n\r\n\t\tconst handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);\r\n\t\tconst handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\t\tconst handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\r\n\t\tconst [error, setError] = useState<string | null>(null);\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointsEditPopup && checklistCheckpointProperties.icon) {\r\n\t\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t\t...icon,\r\n\t\t\t\t\t\tselected: icon.base64 === checklistCheckpointProperties.icon, // Compare Base64 strings directly\r\n\t\t\t\t\t}))\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}, [checkpointsEditPopup, checklistCheckpointProperties.icon]);\r\n\r\n\t\tconst handleIconClick = (id: number) => {\r\n\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t...icon,\r\n\t\t\t\t\tselected: icon.id === id,\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\r\n\t\t\tconst selectedIcon = icons.find((icon) => icon.id === id);\r\n\t\t\tif (selectedIcon) {\r\n\t\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t\t...prev,\r\n\t\t\t\t\ticon: selectedIcon.base64, // Save Base64 instead of component\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\t\tconst file = event.target.files?.[0];\r\n\t\t\tif (!file) return;\r\n\r\n\t\t\tconst isIco = file.name.endsWith(\".ico\");\r\n\r\n\t\t\t// Validate the file type and size\r\n\t\t\tconst img = new Image();\r\n\t\t\timg.src = URL.createObjectURL(file);\r\n\t\t\timg.onload = () => {\r\n\t\t\t\tif (!isIco || img.width > 64 || img.height > 64) {\r\n\t\t\t\t\tsetError(\"Please upload an .ico file less than 64x64px\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetError(null);\r\n\t\t\t\t\tsetIcons((prevIcons) => [\r\n\t\t\t\t\t\t...prevIcons,\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: prevIcons.length + 1,\r\n\t\t\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={img.src}\r\n\t\t\t\t\t\t\t\t\talt=\"Custom Icon\"\r\n\t\t\t\t\t\t\t\t\twidth={24}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\tselected: false,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t]);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetCheckPointsEditPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tsetFileError(null);\r\n\r\n\t\t\tupdateChecklistCheckPointItem(checklistCheckpointProperties);\r\n\t\t\thandleClose();\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst handleEditClick = () => {\r\n\t\t\tsetCheckPointsEditPopup(true);\r\n\t\t};\r\n\r\n\t\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\r\n\t\tconst [skip, setSkip] = useState(0);\r\n\t\tconst [loading, setLoading] = useState(false);\r\n\t\tconst [hasMore, setHasMore] = useState(true);\r\n\t\tconst dropdownRef = useRef<HTMLDivElement>(null);\r\n\t\tconst { accountId } = useContext(AccountContext);\r\n\r\n\t\t// Set number of items to fetch per request to 10\r\n\t\tconst top = 10;\r\n\r\n\t\t// Initial data fetch\r\n\t\tuseEffect(() => {\r\n\t\t\tfetchData(0); // Load first 10 guides\r\n\t\t}, []);\r\n\r\n\t\tconst fetchData = async (newSkip: number) => {\r\n\t\t\tif (loading || !hasMore) return; // Prevent duplicate calls or if no more data\r\n\r\n\t\t\tsetLoading(true);\r\n\r\n\t\t\tconst filters = [\r\n\t\t\t\t{\r\n\t\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\t\tElementType: \"string\",\r\n\t\t\t\t\tCondition: \"contains\",\r\n\t\t\t\t\tValue: accountId,\r\n\t\t\t\t\tIsCustomField: false,\r\n\t\t\t\t},\r\n\t\t\t];\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst data = await getAllGuides(newSkip, top, filters, \"\");\r\n\t\t\t\tconst newInteractions = data?.results || [];\r\n\r\n\t\t\t\tif (newInteractions.length === 0) {\r\n\t\t\t\t\tsetHasMore(false);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetInteractions((prev) => [...prev, ...newInteractions]);\r\n\t\t\t\t\tsetSkip(newSkip + top);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error fetching guides:\", error);\r\n\t\t\t} finally {\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Handle scroll event for the dropdown menu\r\n\t\tconst handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {\r\n\t\t\tconst { scrollTop, scrollHeight, clientHeight } = event.currentTarget;\r\n\r\n\t\t\t// If scrolled to bottom (with a small buffer)\r\n\t\t\tif (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {\r\n\t\t\t\tfetchData(skip);\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst [files, setFiles] = useState<File[]>([]);\r\n\t\tconst [gifFile, setGifFile] = useState<File | null>(null);\r\n\t\tconst [videoFile, setVideoFile] = useState<File | null>(null);\r\n\t\tconst [fileError, setFileError] = useState<string | null>(null);\r\n\r\n\t\tconst convertFileToBase64 = (file: File): Promise<string> => {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tconst reader = new FileReader();\r\n\t\t\t\treader.readAsDataURL(file);\r\n\t\t\t\treader.onload = () => resolve(reader.result as string);\r\n\t\t\t\treader.onerror = (error) => reject(error);\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst convertBase64ToFile = (base64: string, fileName: string, fileType: string) => {\r\n\t\t\tif (!base64) {\r\n\t\t\t\tconsole.error(\"Error: Base64 string is undefined or empty.\");\r\n\t\t\t\treturn null; // Return null if invalid input\r\n\t\t\t}\r\n\r\n\t\t\t// If the base64 string doesn't contain a comma, it's probably missing the \"data:image/png;base64,\" prefix\r\n\t\t\tconst base64Data = base64.includes(\",\") ? base64.split(\",\")[1] : base64;\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst byteCharacters = atob(base64Data);\r\n\t\t\t\tconst byteNumbers = new Array(byteCharacters.length).fill(null).map((_, i) => byteCharacters.charCodeAt(i));\r\n\t\t\t\tconst byteArray = new Uint8Array(byteNumbers);\r\n\r\n\t\t\t\treturn new File([byteArray], fileName, { type: fileType });\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error converting Base64 to File:\", error);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t};\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointsEditPopup && checklistCheckpointProperties.supportingMedia?.length > 0) {\r\n\t\t\t\tconst mediaFiles = checklistCheckpointProperties.supportingMedia\r\n\t\t\t\t\t.map((media: any) => {\r\n\t\t\t\t\t\tif (typeof media === \"string\") {\r\n\t\t\t\t\t\t\treturn null; // Skip if media is a plain string (unexpected case)\r\n\t\t\t\t\t\t} else if (typeof media === \"object\" && media.Base64) {\r\n\t\t\t\t\t\t\treturn convertBase64ToFile(media.Base64, media.Name, media.Type); // ✅ Use actual file name\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn null;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.filter((file: any): file is File => file !== null); // ✅ Remove null values\r\n\r\n\t\t\t\t// ✅ Separate files correctly\r\n\t\t\t\tconst imageFiles = mediaFiles.filter((file: any) =>\r\n\t\t\t\t\t[\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)\r\n\t\t\t\t);\r\n\t\t\t\tconst gif = mediaFiles.find((file: any) => file.type === \"image/gif\") || null;\r\n\t\t\t\tconst video = mediaFiles.find((file: any) => file.type === \"video/mp4\") || null;\r\n\r\n\t\t\t\tsetFiles(imageFiles);\r\n\t\t\t\tsetGifFile(gif);\r\n\t\t\t\tsetVideoFile(video);\r\n\t\t\t}\r\n\t\t}, [checkpointsEditPopup, checklistCheckpointProperties.supportingMedia]);\r\n\r\n\t\tconst handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\t\tsetFileError(null);\r\n\t\t\tif (!event.target.files) return;\r\n\r\n\t\t\tconst newFiles = Array.from(event.target.files);\r\n\r\n\t\t\tconst base64Files = await Promise.all(\r\n\t\t\t\tnewFiles.map(async (file) => ({\r\n\t\t\t\t\tName: file.name,\r\n\t\t\t\t\tType: file.type,\r\n\t\t\t\t\tBase64: await convertFileToBase64(file),\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\r\n\t\t\tconst fileObjects = base64Files\r\n\t\t\t\t.map((fileData) => convertBase64ToFile(fileData.Base64, fileData.Name, fileData.Type))\r\n\t\t\t\t.filter((file): file is File => file !== null);\r\n\r\n\t\t\tconst newGifs = fileObjects.filter((file) => file.name.toLowerCase().endsWith(\".gif\"));\r\n\t\t\tconst newVideos = fileObjects.filter((file) => file.name.toLowerCase().endsWith(\".mp4\"));\r\n\t\t\tconst newImages = fileObjects.filter((file) =>\r\n\t\t\t\t[\".png\", \".jpg\", \".jpeg\"].some((ext) => file.name.toLowerCase().endsWith(ext))\r\n\t\t\t);\r\n\r\n\t\t\t// Validate types\r\n\t\t\tconst allTypes = Array.from(new Set(fileObjects.map((file) => file.type)));\r\n\t\t\tif (allTypes.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 1: Check if a GIF already exists\r\n\t\t\tif (gifFile && newGifs.length > 0) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF allowed\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 2: Check if a video already exists\r\n\t\t\tif (videoFile && newVideos.length > 0) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video allowed\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 3: If a GIF exists, prevent uploading any other type\r\n\t\t\tif (gifFile && (newVideos.length > 0 || newImages.length > 0)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 4: If a video exists, prevent uploading any other type\r\n\t\t\tif (videoFile && (newGifs.length > 0 || newImages.length > 0)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 5: If images exist, ensure new images are same type\r\n\t\t\tif (files.length > 0 && newImages.length > 0) {\r\n\t\t\t\tconst existingType = files[0].type;\r\n\t\t\t\tconst newImageType = newImages[0].type;\r\n\r\n\t\t\t\tconst isSameType = newImages.every((img) => img.type === existingType);\r\n\t\t\t\tif (!isSameType || newImageType !== existingType) {\r\n\t\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Case 6: If images exist and GIF/MP4 is being added\r\n\t\t\tif (files.length > 0 && (newGifs.length > 0 || newVideos.length > 0)) {\r\n\t\t\t\tsetFileError(\"Mixed file formats are not allowed.\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Set accepted files\r\n\t\t\tif (newGifs.length > 0) {\r\n\t\t\t\tsetGifFile(newGifs[0]);\r\n\t\t\t}\r\n\t\t\tif (newVideos.length > 0) {\r\n\t\t\t\tsetVideoFile(newVideos[0]);\r\n\t\t\t}\r\n\t\t\tif (newImages.length > 0) {\r\n\t\t\t\tsetFiles((prevFiles) => {\r\n\t\t\t\t\tconst updated = [...prevFiles, ...newImages];\r\n\t\t\t\t\tupdated.sort((a, b) => {\r\n\t\t\t\t\t\tconst aNum = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\t\tconst bNum = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\t\treturn aNum - bNum;\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn updated;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// Update media\r\n\t\t\tsetChecklistCheckpointProperties((prevState: any) => {\r\n\t\t\t\tconst updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\r\n\t\t\t\tupdatedMedia.sort((a, b) => {\r\n\t\t\t\t\tconst aNum = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\tconst bNum = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\treturn aNum - bNum;\r\n\t\t\t\t});\r\n\t\t\t\treturn { ...prevState, supportingMedia: updatedMedia };\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleDeleteFile = (index: number) => {\r\n\t\t\tsetFileError(null);\r\n\t\t\tsetFiles((prevFiles) => {\r\n\t\t\t\tconst updated = prevFiles.filter((_, i) => i !== index);\r\n\t\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t\t...prev,\r\n\t\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((_: any, i: any) => i !== index) || [],\r\n\t\t\t\t}));\r\n\t\t\t\treturn updated;\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleDeleteGif = () => {\r\n\t\t\tsetGifFile(null);\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(\".gif\")) || [],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleDeleteVideo = () => {\r\n\t\t\tsetVideoFile(null);\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(\".mp4\")) || [],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst index = checklistGuideMetaData[0]?.checkpoints?.checkpointsList?.findIndex(\r\n\t\t\t(i: any) => i.id === editInteractionName\r\n\t\t);\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Step\")} {index + 1}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t <Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interaction\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t{/* Disabled Select-Like Display */}\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 13px) !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 8px 8px 8px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.interaction || \"\"}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\".MuiSelect-icon\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"none\", // Hide the dropdown arrow to keep the read-only feel\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder : \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"35px !important\" }\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem value={checklistCheckpointProperties.interaction}>\r\n\t\t\t\t\t\t\t\t\t\t\t{checklistCheckpointProperties.interaction || translate(\"No Interaction Selected\")}\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Title\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Title\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.title}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"title\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Description\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.description}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"description\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0\",\r\n\t\t\t\t\t\t\t\t\t\tmarginBottom: \"10px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Redirect URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: redirect }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"11px\", color: \"#8d8d8d\", textAlign: \"left\", padding: \"0\", marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Redirection URL\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.redirectURL}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"redirectURL\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Icon\")}</Typography>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tgap: 1,\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"-webkit-fill-available\",\r\n\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{icons.map((icon) => (\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\t\t\t\t\t\tkey={icon.id}\r\n\t\t\t\t\t\t\t\t\t\t\ttitle={translate(\"Select Icon\")}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t{/* <Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Icon Background Color</Typography>\r\n\t\t\t\t\t\t\t\t<Box sx={{ padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={\r\n\t\t\t\t\t\t\t\t\t\t\tchecklistCheckpointProperties.iconBackgroundColor ||\r\n\t\t\t\t\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsIcons ||\r\n\t\t\t\t\t\t\t\t\t\t\t\"#333\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"iconBackgroundColor\", e.target.value);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", height: \"30px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Supporting Media\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"165px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 8px 8px 8px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tborder: \"1px dashed var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tbackground: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-upload-button\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\", // Ensures icon & text are in one line\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\tcomponent=\"label\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloudUploadOutlinedIcon sx={{ zoom: \"1.6\" }} />\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Upload File\")}\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"file-input\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\tmultiple\r\n\t\t\t\t\t\t\t\t\t\t\taccept=\".jpeg, .jpg, .png, .gif, .mp4\" // ✅ Added MP4 support\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleFileChange}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t<Typography style={{ fontSize: \"12px\", color: \"#A3A3A3\" }}>.png, .jpg, .gif, .mp4</Typography>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t{fileError && (\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#e6a957\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"-webkit-fill-available\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ fontSize: \"12px\" }}>{fileError}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t{/* Display uploaded images */}\r\n\t\t\t\t\t\t\t\t<Box sx={{ width: \"-webkit-fill-available\" }}>\r\n\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t{files.map((file, index) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(file)}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt={`uploaded-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{file.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDeleteFile(index)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deletestep }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: \"1\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t{/* Display uploaded GIF separately */}\r\n\t\t\t\t\t\t\t\t\t{gifFile && (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(gifFile)}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"uploaded-gif\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{gifFile.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleDeleteGif}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deletestep }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: \"1\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{videoFile && (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #0a6\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"196px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e6ffe6\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<video\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth=\"40\"\r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"40\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<source\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(videoFile)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"video/mp4\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\tYour browser does not support the video tag.\r\n\t\t\t\t\t\t\t\t\t\t\t</video>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{videoFile.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleDeleteVideo}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Media Title\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Title\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.mediaTitle}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"mediaTitle\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t//onChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Media Description\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.mediaDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\tif (value.length > 200) {\r\n\t\t\t\t\t\t\t\t\t\t\tvalue = value.slice(0, 200);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"mediaDescription\", value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\thelperText={`${checklistCheckpointProperties.mediaDescription?.length || 0}/200`}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default CheckPointEditPopup;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,CAACC,SAAS,CAAEC,MAAM,CAAEC,UAAU,KAAQ,OAAO,CACjF,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,CAAkBC,WAAW,CAAcC,MAAM,CAAEC,QAAQ,CAA+CC,OAAO,KAA0B,eAAe,CACvN,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,OAMCC,UAAU,CACVC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,OAAO,KACD,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,uBAAuB,KAAM,yCAAyC,CAC7E,OAASC,cAAc,KAAQ,yBAAyB,CACxD,OAASC,YAAY,KAAQ,kCAAkC,CAK/D,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtC,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAA6G,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,IAA5G,CAAEC,mBAAmB,CAAEC,oBAA+E,CAAC,CAAAN,IAAA,CACnI,KAAM,CAAEO,CAAC,CAAEC,SAAU,CAAC,CAAGd,cAAc,CAAC,CAAC,CACtC,KAAM,CACPe,uBAAuB,CACvBC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,UAAU,CACVC,aAAa,CACbC,gBAAgB,CAChBC,mBAAmB,CACnBC,oBAAoB,CACpBC,uBAAuB,CACvBC,0BAA0B,CAC1BC,6BAA6B,CAC7BC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC1BC,uBAAuB,CACvBC,iBAAiB,CACjBC,oBAAoB,CACpBC,sBAAsB,CACtBC,6BAA6B,CAC7BC,mBAAmB,CACnBC,gBACD,CAAC,CAAGnD,cAAc,CAAEoD,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAAAC,IAAI,CAAGL,sBAAsB,CAAC,CAAC,CAAC,CAACM,WAAW,CAACC,eAAe,CAACC,IAAI,CAAEC,CAAM,EAAKA,CAAC,CAACC,EAAE,GAAKhC,mBAAmB,CAAC,CACjH,KAAM,CAAAiC,cAAc,CAAIC,SAAiB,EAAK,CAC7C,MAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE,CACtD,CAAC,CAED,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAG7E,QAAQ,CAAQ,IAAM,CAC/C,MAAO,CACN,CACCwE,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACzD,OAAO,CAAC,CAC/B+D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAEjE,OAAQ,CAAE,CAC7CkE,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACCb,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACxD,OAAO,CAAC,CAC/B8D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAEhE,OAAQ,CAAE,CAC7CiE,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACCb,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACvD,OAAO,CAAC,CAC/B6D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE/D,OAAQ,CAAE,CAC7CgE,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACCb,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACtD,OAAO,CAAC,CAC/B4D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE9D,OAAQ,CAAE,CAC7C+D,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACCb,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACrD,OAAO,CAAC,CAC/B2D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE7D,OAAQ,CAAE,CAC7C8D,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACCb,EAAE,CAAE,CAAC,CACLM,MAAM,CAAEL,cAAc,CAACpD,OAAO,CAAC,CAC/B0D,SAAS,cACRhD,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE5D,OAAQ,CAAE,CAC7C6D,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,KACX,CAAC,CACD,CACF,CAAC,CAAC,CAEF,KAAM,CAACC,6BAA6B,CAAEC,gCAAgC,CAAC,CAAGvF,QAAQ,CAAM,IAAM,CAC7F,KAAM,CAAAwF,oCAAoC,CAAG,CAC5CC,WAAW,CAAEtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsB,WAAW,CAC9BC,KAAK,CAAEvB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuB,KAAK,CAClBC,WAAW,CAAExB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,WAAW,CAC9BC,WAAW,CAAEzB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEyB,WAAW,CAC9BC,IAAI,CAAE,CAAA1B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE0B,IAAI,GAAIjB,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,CACtCe,eAAe,CAAE,CAAA3B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2B,eAAe,GAAI,EAAE,CAC5CC,UAAU,CAAE5B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE4B,UAAU,CAC5BC,gBAAgB,CAAE7B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6B,gBAAgB,CACxCxB,EAAE,CAAEL,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,EACX,CAAC,CACD,MAAO,CAAAgB,oCAAoC,CAC5C,CAAC,CAAC,CAEF,KAAM,CAAAS,+BAA+B,CAAIC,CAAM,EAAKzC,sBAAsB,CAACyC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC1F,KAAM,CAAAC,gCAAgC,CAAIH,CAAM,EAAK7C,uBAAuB,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC5F,KAAM,CAAAE,sCAAsC,CAAIJ,CAAM,EAAK7C,uBAAuB,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAElG,KAAM,CAACG,KAAK,CAAEC,QAAQ,CAAC,CAAGxG,QAAQ,CAAgB,IAAI,CAAC,CACvDC,SAAS,CAAC,IAAM,CACf,GAAIwC,oBAAoB,EAAI6C,6BAA6B,CAACO,IAAI,CAAE,CAC/DhB,QAAQ,CAAE4B,SAAS,EAClBA,SAAS,CAACC,GAAG,CAAEb,IAAI,GAAM,CACxB,GAAGA,IAAI,CACPR,QAAQ,CAAEQ,IAAI,CAACf,MAAM,GAAKQ,6BAA6B,CAACO,IAAM;AAC/D,CAAC,CAAC,CACH,CAAC,CACF,CACD,CAAC,CAAE,CAACpD,oBAAoB,CAAE6C,6BAA6B,CAACO,IAAI,CAAC,CAAC,CAE9D,KAAM,CAAAc,eAAe,CAAInC,EAAU,EAAK,CACvCK,QAAQ,CAAE4B,SAAS,EAClBA,SAAS,CAACC,GAAG,CAAEb,IAAI,GAAM,CACxB,GAAGA,IAAI,CACPR,QAAQ,CAAEQ,IAAI,CAACrB,EAAE,GAAKA,EACvB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAoC,YAAY,CAAGhC,KAAK,CAACN,IAAI,CAAEuB,IAAI,EAAKA,IAAI,CAACrB,EAAE,GAAKA,EAAE,CAAC,CACzD,GAAIoC,YAAY,CAAE,CACjBrB,gCAAgC,CAAEsB,IAAS,GAAM,CAChD,GAAGA,IAAI,CACPhB,IAAI,CAAEe,YAAY,CAAC9B,MAAQ;AAC5B,CAAC,CAAC,CAAC,CACJ,CACD,CAAC,CAED,KAAM,CAAAgC,gBAAgB,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACxE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACZ,MAAM,CAACe,KAAK,UAAAF,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAI,CAACC,IAAI,CAAE,OAEX,KAAM,CAAAE,KAAK,CAAGF,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAExC;AACA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACvBD,GAAG,CAACE,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC,CACnCK,GAAG,CAACK,MAAM,CAAG,IAAM,CAClB,GAAI,CAACR,KAAK,EAAIG,GAAG,CAACM,KAAK,CAAG,EAAE,EAAIN,GAAG,CAACO,MAAM,CAAG,EAAE,CAAE,CAChDrB,QAAQ,CAAC,8CAA8C,CAAC,CACzD,CAAC,IAAM,CACNA,QAAQ,CAAC,IAAI,CAAC,CACd3B,QAAQ,CAAE4B,SAAS,EAAK,CACvB,GAAGA,SAAS,CACZ,CACCjC,EAAE,CAAEiC,SAAS,CAACqB,MAAM,CAAG,CAAC,CACxB/C,SAAS,cACRhD,IAAA,QACCyF,GAAG,CAAEF,GAAG,CAACE,GAAI,CACbO,GAAG,CAAC,aAAa,CACjBH,KAAK,CAAE,EAAG,CACV,CACD,CACDvC,QAAQ,CAAE,KACX,CAAC,CACD,CAAC,CACH,CACD,CAAC,CACF,CAAC,CAED,KAAM,CAAA2C,WAAW,CAAGA,CAAA,GAAM,CACzBpF,uBAAuB,CAAC,KAAK,CAAC,CAC/B,CAAC,CACD,KAAM,CAAAqF,iBAAiB,CAAGA,CAAA,GAAM,CAC/BlF,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CACD,KAAM,CAAAmF,gBAAgB,CAAI9B,KAAa,EAAK,CAC3C,KAAM,CAAA+B,QAAQ,CAAG,EAAE,CAAG,CAAC/B,KAAK,CAAG,CAAC,EAAI,CAAC,CACrCgC,gBAAgB,CAAC,MAAM,CAAED,QAAQ,CAAC,CACnC,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAAC,CAAC,CAElC,KAAM,CAAAD,gBAAgB,CAAGA,CAACE,GAAQ,CAAElC,KAAU,GAAK,CAClDb,gCAAgC,CAAEgD,SAAc,GAAM,CACrD,GAAGA,SAAS,CACZ,CAACD,GAAG,EAAGlC,KACR,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAoC,kBAAkB,CAAGA,CAAA,GAAM,CAChCC,YAAY,CAAC,IAAI,CAAC,CAElB1E,6BAA6B,CAACuB,6BAA6B,CAAC,CAC5D0C,WAAW,CAAC,CAAC,CACbhE,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA0E,eAAe,CAAGA,CAAA,GAAM,CAC7B9F,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED,KAAM,CAAC+F,YAAY,CAAEC,eAAe,CAAC,CAAG5I,QAAQ,CAAQ,EAAE,CAAC,CAE3D,KAAM,CAAC6I,IAAI,CAAEC,OAAO,CAAC,CAAG9I,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAAC+I,OAAO,CAAEC,UAAU,CAAC,CAAGhJ,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACiJ,OAAO,CAAEC,UAAU,CAAC,CAAGlJ,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAAmJ,WAAW,CAAGjJ,MAAM,CAAiB,IAAI,CAAC,CAChD,KAAM,CAAEkJ,SAAU,CAAC,CAAGjJ,UAAU,CAACwB,cAAc,CAAC,CAEhD;AACA,KAAM,CAAA0H,GAAG,CAAG,EAAE,CAEd;AACApJ,SAAS,CAAC,IAAM,CACfqJ,SAAS,CAAC,CAAC,CAAC,CAAE;AACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,SAAS,CAAG,KAAO,CAAAC,OAAe,EAAK,CAC5C,GAAIR,OAAO,EAAI,CAACE,OAAO,CAAE,OAAQ;AAEjCD,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAAAQ,OAAO,CAAG,CACf,CACCC,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAER,SAAS,CAChBS,aAAa,CAAE,KAChB,CAAC,CACD,CAED,GAAI,CACH,KAAM,CAAA1F,IAAI,CAAG,KAAM,CAAAvC,YAAY,CAAC2H,OAAO,CAAEF,GAAG,CAAEG,OAAO,CAAE,EAAE,CAAC,CAC1D,KAAM,CAAAM,eAAe,CAAG,CAAA3F,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE4F,OAAO,GAAI,EAAE,CAE3C,GAAID,eAAe,CAAChC,MAAM,GAAK,CAAC,CAAE,CACjCoB,UAAU,CAAC,KAAK,CAAC,CAClB,CAAC,IAAM,CACNN,eAAe,CAAE/B,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAE,GAAGiD,eAAe,CAAC,CAAC,CACxDhB,OAAO,CAACS,OAAO,CAAGF,GAAG,CAAC,CACvB,CACD,CAAE,MAAO9C,KAAK,CAAE,CACfyD,OAAO,CAACzD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC/C,CAAC,OAAS,CACTyC,UAAU,CAAC,KAAK,CAAC,CAClB,CACD,CAAC,CAED;AACA,KAAM,CAAAiB,gBAAgB,CAAIlD,KAAoC,EAAK,CAClE,KAAM,CAAEmD,SAAS,CAAEC,YAAY,CAAEC,YAAa,CAAC,CAAGrD,KAAK,CAACsD,aAAa,CAErE;AACA,GAAIF,YAAY,CAAGD,SAAS,CAAGE,YAAY,CAAG,EAAE,EAAI,CAACrB,OAAO,EAAIE,OAAO,CAAE,CACxEK,SAAS,CAACT,IAAI,CAAC,CAChB,CACD,CAAC,CACD,KAAM,CAAC3B,KAAK,CAAEoD,QAAQ,CAAC,CAAGtK,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACuK,OAAO,CAAEC,UAAU,CAAC,CAAGxK,QAAQ,CAAc,IAAI,CAAC,CACzD,KAAM,CAACyK,SAAS,CAAEC,YAAY,CAAC,CAAG1K,QAAQ,CAAc,IAAI,CAAC,CAC7D,KAAM,CAAC2K,SAAS,CAAElC,YAAY,CAAC,CAAGzI,QAAQ,CAAgB,IAAI,CAAC,CAE/D,KAAM,CAAA4K,mBAAmB,CAAI3D,IAAU,EAAsB,CAC5D,MAAO,IAAI,CAAA4D,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACvC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,aAAa,CAACjE,IAAI,CAAC,CAC1B+D,MAAM,CAACrD,MAAM,CAAG,IAAMmD,OAAO,CAACE,MAAM,CAACG,MAAgB,CAAC,CACtDH,MAAM,CAACI,OAAO,CAAI7E,KAAK,EAAKwE,MAAM,CAACxE,KAAK,CAAC,CAC1C,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAA8E,mBAAmB,CAAGA,CAACvG,MAAc,CAAEwG,QAAgB,CAAEC,QAAgB,GAAK,CACnF,GAAI,CAACzG,MAAM,CAAE,CACZkF,OAAO,CAACzD,KAAK,CAAC,6CAA6C,CAAC,CAC5D,MAAO,KAAI,CAAE;AACd,CAEA;AACA,KAAM,CAAAiF,UAAU,CAAG1G,MAAM,CAAC2G,QAAQ,CAAC,GAAG,CAAC,CAAG3G,MAAM,CAAC4G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAG5G,MAAM,CAEvE,GAAI,CACH,KAAM,CAAA6G,cAAc,CAAGC,IAAI,CAACJ,UAAU,CAAC,CACvC,KAAM,CAAAK,WAAW,CAAG,GAAI,CAAAC,KAAK,CAACH,cAAc,CAAC7D,MAAM,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,CAACrF,GAAG,CAAC,CAACsF,CAAC,CAAEC,CAAC,GAAKN,cAAc,CAACO,UAAU,CAACD,CAAC,CAAC,CAAC,CAC3G,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAC,UAAU,CAACP,WAAW,CAAC,CAE7C,MAAO,IAAI,CAAAQ,IAAI,CAAC,CAACF,SAAS,CAAC,CAAEb,QAAQ,CAAE,CAAEgB,IAAI,CAAEf,QAAS,CAAC,CAAC,CAC3D,CAAE,MAAOhF,KAAK,CAAE,CACfyD,OAAO,CAACzD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,MAAO,KAAI,CACZ,CACD,CAAC,CACDtG,SAAS,CAAC,IAAM,KAAAsM,qBAAA,CACf,GAAI9J,oBAAoB,EAAI,EAAA8J,qBAAA,CAAAjH,6BAA6B,CAACQ,eAAe,UAAAyG,qBAAA,iBAA7CA,qBAAA,CAA+CzE,MAAM,EAAG,CAAC,CAAE,CACtF,KAAM,CAAA0E,UAAU,CAAGlH,6BAA6B,CAACQ,eAAe,CAC9DY,GAAG,CAAE+F,KAAU,EAAK,CACpB,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC9B,MAAO,KAAI,CAAE;AACd,CAAC,IAAM,IAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAACC,MAAM,CAAE,CACrD,MAAO,CAAArB,mBAAmB,CAACoB,KAAK,CAACC,MAAM,CAAED,KAAK,CAACE,IAAI,CAAEF,KAAK,CAACG,IAAI,CAAC,CAAE;AACnE,CACA,MAAO,KAAI,CACZ,CAAC,CAAC,CACDC,MAAM,CAAE5F,IAAS,EAAmBA,IAAI,GAAK,IAAI,CAAC,CAAE;AAEtD;AACA,KAAM,CAAA6F,UAAU,CAAGN,UAAU,CAACK,MAAM,CAAE5F,IAAS,EAC9C,CAAC,YAAY,CAAE,WAAW,CAAE,WAAW,CAAC,CAACwE,QAAQ,CAACxE,IAAI,CAACqF,IAAI,CAC5D,CAAC,CACD,KAAM,CAAAS,GAAG,CAAGP,UAAU,CAAClI,IAAI,CAAE2C,IAAS,EAAKA,IAAI,CAACqF,IAAI,GAAK,WAAW,CAAC,EAAI,IAAI,CAC7E,KAAM,CAAAU,KAAK,CAAGR,UAAU,CAAClI,IAAI,CAAE2C,IAAS,EAAKA,IAAI,CAACqF,IAAI,GAAK,WAAW,CAAC,EAAI,IAAI,CAE/EhC,QAAQ,CAACwC,UAAU,CAAC,CACpBtC,UAAU,CAACuC,GAAG,CAAC,CACfrC,YAAY,CAACsC,KAAK,CAAC,CACpB,CACD,CAAC,CAAE,CAACvK,oBAAoB,CAAE6C,6BAA6B,CAACQ,eAAe,CAAC,CAAC,CAEzE,KAAM,CAAAmH,gBAAgB,CAAG,KAAO,CAAAlG,KAA0C,EAAK,CAC9E0B,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CAAC1B,KAAK,CAACZ,MAAM,CAACe,KAAK,CAAE,OAEzB,KAAM,CAAAgG,QAAQ,CAAGpB,KAAK,CAACqB,IAAI,CAACpG,KAAK,CAACZ,MAAM,CAACe,KAAK,CAAC,CAE/C,KAAM,CAAAkG,WAAW,CAAG,KAAM,CAAAvC,OAAO,CAACwC,GAAG,CACpCH,QAAQ,CAACxG,GAAG,CAAC,KAAO,CAAAO,IAAI,GAAM,CAC7B0F,IAAI,CAAE1F,IAAI,CAACG,IAAI,CACfwF,IAAI,CAAE3F,IAAI,CAACqF,IAAI,CACfI,MAAM,CAAE,KAAM,CAAA9B,mBAAmB,CAAC3D,IAAI,CACvC,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAqG,WAAW,CAAGF,WAAW,CAC7B1G,GAAG,CAAE6G,QAAQ,EAAKlC,mBAAmB,CAACkC,QAAQ,CAACb,MAAM,CAAEa,QAAQ,CAACZ,IAAI,CAAEY,QAAQ,CAACX,IAAI,CAAC,CAAC,CACrFC,MAAM,CAAE5F,IAAI,EAAmBA,IAAI,GAAK,IAAI,CAAC,CAE/C,KAAM,CAAAuG,OAAO,CAAGF,WAAW,CAACT,MAAM,CAAE5F,IAAI,EAAKA,IAAI,CAACG,IAAI,CAACqG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,MAAM,CAAC,CAAC,CACtF,KAAM,CAAAqG,SAAS,CAAGJ,WAAW,CAACT,MAAM,CAAE5F,IAAI,EAAKA,IAAI,CAACG,IAAI,CAACqG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,MAAM,CAAC,CAAC,CACxF,KAAM,CAAAsG,SAAS,CAAGL,WAAW,CAACT,MAAM,CAAE5F,IAAI,EACzC,CAAC,MAAM,CAAE,MAAM,CAAE,OAAO,CAAC,CAAC2G,IAAI,CAAEC,GAAG,EAAK5G,IAAI,CAACG,IAAI,CAACqG,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAACwG,GAAG,CAAC,CAC9E,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGhC,KAAK,CAACqB,IAAI,CAAC,GAAI,CAAAY,GAAG,CAACT,WAAW,CAAC5G,GAAG,CAAEO,IAAI,EAAKA,IAAI,CAACqF,IAAI,CAAC,CAAC,CAAC,CAC1E,GAAIwB,QAAQ,CAAChG,MAAM,CAAG,CAAC,CAAE,CACxBW,YAAY,CAAC9F,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CAEA;AACA,GAAI4H,OAAO,EAAIiD,OAAO,CAAC1F,MAAM,CAAG,CAAC,CAAE,CAClCW,YAAY,CAAC9F,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAC/C,OACD,CAEA;AACA,GAAI8H,SAAS,EAAIiD,SAAS,CAAC5F,MAAM,CAAG,CAAC,CAAE,CACtCW,YAAY,CAAC9F,SAAS,CAAC,wBAAwB,CAAC,CAAC,CACjD,OACD,CAEA;AACA,GAAI4H,OAAO,GAAKmD,SAAS,CAAC5F,MAAM,CAAG,CAAC,EAAI6F,SAAS,CAAC7F,MAAM,CAAG,CAAC,CAAC,CAAE,CAC9DW,YAAY,CAAC9F,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CAEA;AACA,GAAI8H,SAAS,GAAK+C,OAAO,CAAC1F,MAAM,CAAG,CAAC,EAAI6F,SAAS,CAAC7F,MAAM,CAAG,CAAC,CAAC,CAAE,CAC9DW,YAAY,CAAC9F,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CAEA;AACA,GAAIuE,KAAK,CAACY,MAAM,CAAG,CAAC,EAAI6F,SAAS,CAAC7F,MAAM,CAAG,CAAC,CAAE,CAC7C,KAAM,CAAAkG,YAAY,CAAG9G,KAAK,CAAC,CAAC,CAAC,CAACoF,IAAI,CAClC,KAAM,CAAA2B,YAAY,CAAGN,SAAS,CAAC,CAAC,CAAC,CAACrB,IAAI,CAEtC,KAAM,CAAA4B,UAAU,CAAGP,SAAS,CAACQ,KAAK,CAAE7G,GAAG,EAAKA,GAAG,CAACgF,IAAI,GAAK0B,YAAY,CAAC,CACtE,GAAI,CAACE,UAAU,EAAID,YAAY,GAAKD,YAAY,CAAE,CACjDvF,YAAY,CAAC9F,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CACD,CAEA;AACA,GAAIuE,KAAK,CAACY,MAAM,CAAG,CAAC,GAAK0F,OAAO,CAAC1F,MAAM,CAAG,CAAC,EAAI4F,SAAS,CAAC5F,MAAM,CAAG,CAAC,CAAC,CAAE,CACrEW,YAAY,CAAC,qCAAqC,CAAC,CACnD,OACD,CAEA;AACA,GAAI+E,OAAO,CAAC1F,MAAM,CAAG,CAAC,CAAE,CACvB0C,UAAU,CAACgD,OAAO,CAAC,CAAC,CAAC,CAAC,CACvB,CACA,GAAIE,SAAS,CAAC5F,MAAM,CAAG,CAAC,CAAE,CACzB4C,YAAY,CAACgD,SAAS,CAAC,CAAC,CAAC,CAAC,CAC3B,CACA,GAAIC,SAAS,CAAC7F,MAAM,CAAG,CAAC,CAAE,CACzBwC,QAAQ,CAAE8D,SAAS,EAAK,CACvB,KAAM,CAAAC,OAAO,CAAG,CAAC,GAAGD,SAAS,CAAE,GAAGT,SAAS,CAAC,CAC5CU,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACtB,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAACnH,IAAI,CAACsH,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACJ,CAAC,CAACnH,IAAI,CAACsH,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAE,IAAI,CAAGJ,CAAC,CAACpH,IAAI,CAACsH,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACH,CAAC,CAACpH,IAAI,CAACsH,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC5E,MAAO,CAAAD,IAAI,CAAGG,IAAI,CACnB,CAAC,CAAC,CACF,MAAO,CAAAP,OAAO,CACf,CAAC,CAAC,CACH,CAEA;AACA9I,gCAAgC,CAAEgD,SAAc,EAAK,CACpD,KAAM,CAAAsG,YAAY,CAAG,CAAC,IAAItG,SAAS,CAACzC,eAAe,EAAI,EAAE,CAAC,CAAE,GAAGsH,WAAW,CAAC,CAC3EyB,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC3B,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAAC5B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACJ,CAAC,CAAC5B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAE,IAAI,CAAGJ,CAAC,CAAC7B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACH,CAAC,CAAC7B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC5E,MAAO,CAAAD,IAAI,CAAGG,IAAI,CACnB,CAAC,CAAC,CACF,MAAO,CAAE,GAAGrG,SAAS,CAAEzC,eAAe,CAAE+I,YAAa,CAAC,CACvD,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,KAAa,EAAK,CAC3CtG,YAAY,CAAC,IAAI,CAAC,CAClB6B,QAAQ,CAAE8D,SAAS,EAAK,CACvB,KAAM,CAAAC,OAAO,CAAGD,SAAS,CAACvB,MAAM,CAAC,CAACb,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAK8C,KAAK,CAAC,CACvDxJ,gCAAgC,CAAEsB,IAAS,OAAAmI,qBAAA,OAAM,CAChD,GAAGnI,IAAI,CACPf,eAAe,CAAE,EAAAkJ,qBAAA,CAAAnI,IAAI,CAACf,eAAe,UAAAkJ,qBAAA,iBAApBA,qBAAA,CAAsBnC,MAAM,CAAC,CAACb,CAAM,CAAEC,CAAM,GAAKA,CAAC,GAAK8C,KAAK,CAAC,GAAI,EACnF,CAAC,EAAC,CAAC,CACH,MAAO,CAAAV,OAAO,CACf,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAY,eAAe,CAAGA,CAAA,GAAM,CAC7BzE,UAAU,CAAC,IAAI,CAAC,CAChBjF,gCAAgC,CAAEsB,IAAS,OAAAqI,sBAAA,OAAM,CAChD,GAAGrI,IAAI,CACPf,eAAe,CAAE,EAAAoJ,sBAAA,CAAArI,IAAI,CAACf,eAAe,UAAAoJ,sBAAA,iBAApBA,sBAAA,CAAsBrC,MAAM,CAAE5F,IAAS,OAAAkI,UAAA,OAAK,GAAAA,UAAA,CAAClI,IAAI,CAAC0F,IAAI,UAAAwC,UAAA,WAATA,UAAA,CAAW1B,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,MAAM,CAAC,IAAC,GAAI,EAC7G,CAAC,EAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA+H,iBAAiB,CAAGA,CAAA,GAAM,CAC/B1E,YAAY,CAAC,IAAI,CAAC,CAClBnF,gCAAgC,CAAEsB,IAAS,OAAAwI,sBAAA,OAAM,CAChD,GAAGxI,IAAI,CACPf,eAAe,CAAE,EAAAuJ,sBAAA,CAAAxI,IAAI,CAACf,eAAe,UAAAuJ,sBAAA,iBAApBA,sBAAA,CAAsBxC,MAAM,CAAE5F,IAAS,OAAAqI,WAAA,OAAK,GAAAA,WAAA,CAACrI,IAAI,CAAC0F,IAAI,UAAA2C,WAAA,WAATA,WAAA,CAAW7B,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,MAAM,CAAC,IAAC,GAAI,EAC7G,CAAC,EAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA0H,KAAK,EAAA3M,qBAAA,CAAG0B,sBAAsB,CAAC,CAAC,CAAC,UAAA1B,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA2BgC,WAAW,UAAA/B,sBAAA,kBAAAC,sBAAA,CAAtCD,sBAAA,CAAwCgC,eAAe,UAAA/B,sBAAA,iBAAvDA,sBAAA,CAAyDiN,SAAS,CAC9EtD,CAAM,EAAKA,CAAC,CAACzH,EAAE,GAAKhC,mBACtB,CAAC,CAED,mBACCT,IAAA,QACCyC,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BxN,KAAA,QAAKuN,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BxN,KAAA,QAAKuN,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC1N,IAAA,CAACxB,UAAU,EACV,aAAW,MAAM,CACjBmP,OAAO,CAAE1H,WAAY,CAAAyH,QAAA,cAErB1N,IAAA,CAACN,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbQ,KAAA,QAAKuN,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAE9M,SAAS,CAAC,MAAM,CAAC,CAAC,GAAC,CAACoM,KAAK,CAAG,CAAC,EAAM,CAAC,cAClEhN,IAAA,CAACxB,UAAU,EACVoP,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAE1H,WAAY,CAAAyH,QAAA,cAErB1N,IAAA,CAAClB,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAENkB,IAAA,QAAKyN,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9BxN,KAAA,QAAKuN,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC9BxN,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAEjD1N,IAAA,CAAC1B,UAAU,EAACmP,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAJ,QAAA,CACnF9M,SAAS,CAAC,aAAa,CAAC,CACb,CAAC,cAGdZ,IAAA,CAACtB,WAAW,EACXqP,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CACHhI,KAAK,CAAE,8BAA8B,CACrCoI,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,eAAe,CACxBI,MAAM,CAAE,cACT,CAAE,CAAAR,QAAA,cAEF1N,IAAA,CAACrB,MAAM,EACNwP,YAAY,MACZC,QAAQ,MACR/J,KAAK,CAAEd,6BAA6B,CAACG,WAAW,EAAI,EAAG,CACvDmK,EAAE,CAAE,CACHhI,KAAK,CAAE,iBAAiB,CACxBoI,YAAY,CAAE,MAAM,CACpBI,eAAe,CAAE,SAAS,CAC1BP,OAAO,CAAE,MAAM,CACfQ,KAAK,CAAE,MAAM,CACb,iBAAiB,CAAE,CAClBjL,OAAO,CAAE,MAAQ;AAClB,CAAC,CACD,0BAA0B,CAAE,CAC3B,SAAS,CAAE,CACVkL,WAAW,CAAE,iBACd,CAAC,CACD,eAAe,CAAE,CAChBA,WAAW,CAAE,iBACd,CACD,CAAC,CACD,oCAAoC,CAAE,CACrCC,MAAM,CAAG,iBACV,CAAC,CACD,qBAAqB,CAAE,CAAE1I,MAAM,CAAE,iBAAkB,CACpD,CAAE,CAAA4H,QAAA,cAEF1N,IAAA,CAACpB,QAAQ,EAACyF,KAAK,CAAEd,6BAA6B,CAACG,WAAY,CAAAgK,QAAA,CACzDnK,6BAA6B,CAACG,WAAW,EAAI9C,SAAS,CAAC,yBAAyB,CAAC,CACzE,CAAC,CACJ,CAAC,CACG,CAAC,EACV,CAAC,cAENV,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElD1N,IAAA,CAAC1B,UAAU,EACVmP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEW,YAAY,CAAE,gBAAiB,CAAE,CAAAf,QAAA,CAE/D9M,SAAS,CAAC,OAAO,CAAC,CACR,CAAC,cAEbZ,IAAA,CAACzB,SAAS,EACTwP,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZc,WAAW,CAAE9N,SAAS,CAAC,YAAY,CAAE,CACrC6M,SAAS,CAAC,qBAAqB,CAC/BtK,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAO,CAAE,CACzBxB,KAAK,CAAEd,6BAA6B,CAACI,KAAM,CAC3CgL,QAAQ,CAAGxK,CAAC,EAAKkC,gBAAgB,CAAC,OAAO,CAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC3DuK,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEM,SAAS,CAAE,iBAAiB,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjJ,MAAM,CAAE,iBAAkB,CACpD,CACD,CAAE,CACF,CAAC,EACE,CAAC,cAEN5F,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElD1N,IAAA,CAAC1B,UAAU,EACVmP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEW,YAAY,CAAE,gBAAiB,CAAE,CAAAf,QAAA,CAE/D9M,SAAS,CAAC,aAAa,CAAC,CACd,CAAC,cAEbZ,IAAA,CAACzB,SAAS,EACTwP,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZc,WAAW,CAAE9N,SAAS,CAAC,WAAW,CAAE,CACpC6M,SAAS,CAAC,qBAAqB,CAC/BuB,SAAS,MACTC,OAAO,CAAE,CAAE,CACX9L,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAO,CAAE,CACzBxB,KAAK,CAAEd,6BAA6B,CAACK,WAAY,CACjD+K,QAAQ,CAAGxK,CAAC,EAAKkC,gBAAgB,CAAC,aAAa,CAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjEuK,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEM,SAAS,CAAE,iBAAiB,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjJ,MAAM,CAAE,iBAAkB,CACpD,CACD,CAAE,CACF,CAAC,EACE,CAAC,cAEN5F,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElDxN,KAAA,QACCuN,SAAS,CAAC,qBAAqB,CAC/BtK,KAAK,CAAE,CACNE,OAAO,CAAE,MAAM,CACf6L,aAAa,CAAE,KAAK,CACpBC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVtB,OAAO,CAAE,GAAG,CACZW,YAAY,CAAE,MACf,CAAE,CAAAf,QAAA,eAEF1N,IAAA,CAAC1B,UAAU,EAACuP,EAAE,CAAE,CAAES,KAAK,CAAE,SAAS,CAAEe,UAAU,CAAE,KAAM,CAAE,CAAA3B,QAAA,CAAE9M,SAAS,CAAC,cAAc,CAAC,CAAa,CAAC,cACjGZ,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE1D,QAAS,CAAE,CAC9C2D,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAO,CAAE,CAC3B,CAAC,CAAC,GAAG,EACF,CAAC,cAENrD,IAAA,CAAC1B,UAAU,EACV6E,KAAK,CAAE,CAAEmM,QAAQ,CAAE,MAAM,CAAEhB,KAAK,CAAE,SAAS,CAAEQ,SAAS,CAAE,MAAM,CAAEhB,OAAO,CAAE,GAAG,CAAEW,YAAY,CAAE,MAAO,CAAE,CAAAf,QAAA,CAEpG9M,SAAS,CAAC,+FAA+F,CAAC,CAChG,CAAC,cACbZ,IAAA,CAACzB,SAAS,EACTwP,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZc,WAAW,CAAE9N,SAAS,CAAC,iBAAiB,CAAE,CAC1C6M,SAAS,CAAC,qBAAqB,CAC/BtK,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAO,CAAE,CACzBxB,KAAK,CAAEd,6BAA6B,SAA7BA,6BAA6B,iBAA7BA,6BAA6B,CAAEM,WAAY,CAClD8K,QAAQ,CAAGxK,CAAC,EAAKkC,gBAAgB,CAAC,aAAa,CAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjEuK,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEM,SAAS,CAAE,iBAAiB,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjJ,MAAM,CAAE,iBAAkB,CACpD,CACD,CAAE,CACF,CAAC,EACE,CAAC,cAEN5F,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElD1N,IAAA,CAAC1B,UAAU,EAACmP,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAJ,QAAA,CAAE9M,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,cACrHZ,IAAA,CAAC3B,GAAG,EACHwP,EAAE,CAAE,CACHxK,OAAO,CAAE,MAAM,CACf+L,GAAG,CAAE,CAAC,CACND,UAAU,CAAE,QAAQ,CACpBtJ,KAAK,CAAE,wBAAwB,CAC/B0J,QAAQ,CAAE,MACX,CAAE,CAAA7B,QAAA,CAED7K,KAAK,CAAC8B,GAAG,CAAEb,IAAI,eACf9D,IAAA,CAACnB,OAAO,EACP2Q,KAAK,MAEL7L,KAAK,CAAE/C,SAAS,CAAC,aAAa,CAAE,CAAA8M,QAAA,cAEhC1N,IAAA,CAACxB,UAAU,EACVmP,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAACd,IAAI,CAACrB,EAAE,CAAE,CACxCoL,EAAE,CAAE,CACHW,MAAM,CAAE1K,IAAI,CAACR,QAAQ,CAAG,+BAA+B,CAAG,MAAM,CAChE2K,YAAY,CAAE,KAAK,CACnBH,OAAO,CAAE,KAAK,CACd2B,UAAU,CAAE,SACb,CAAE,CAAA/B,QAAA,CAED5J,IAAI,CAACd,SAAS,CACJ,CAAC,EAbRc,IAAI,CAACrB,EAcF,CACT,CAAC,CACE,CAAC,EACF,CAAC,cAyBNvC,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElD1N,IAAA,CAAC1B,UAAU,EAACmP,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAJ,QAAA,CAAE9M,SAAS,CAAC,kBAAkB,CAAC,CAAa,CAAC,cAEjIV,KAAA,QACCiD,KAAK,CAAE,CACN0C,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,MAAM,CACdoI,MAAM,CAAE,eAAe,CACvB7K,OAAO,CAAE,MAAM,CACf6L,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBO,cAAc,CAAE,QAAQ,CACxBlB,MAAM,CAAE,gCAAgC,CACxCP,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACd2B,UAAU,CAAE,SAAS,CACrBX,SAAS,CAAE,QACZ,CAAE,CAAApB,QAAA,eAEFxN,KAAA,CAACzB,MAAM,EACNgP,SAAS,CAAC,qBAAqB,CAC/BtK,KAAK,CAAE,CACN2C,MAAM,CAAE,MAAM,CACdgI,OAAO,CAAE,GAAG,CACZjI,KAAK,CAAE,MAAM,CACbxC,OAAO,CAAE,MAAM,CACf6L,aAAa,CAAE,KAAK,CAAE;AACtBC,UAAU,CAAE,QAAQ,CACpBO,cAAc,CAAE,QAAQ,CACxBN,GAAG,CAAE,KAAK,CACVd,KAAK,CAAE,MAAM,CACbD,eAAe,CAAE,SAAS,CAC1BsB,aAAa,CAAE,YAAY,CAC3BC,SAAS,CAAE,MACZ,CAAE,CACF7B,OAAO,CAAC,WAAW,CACnB/K,SAAS,CAAC,OAAO,CAAA0K,QAAA,eAEjB1N,IAAA,CAACL,uBAAuB,EAACkO,EAAE,CAAE,CAAEzK,IAAI,CAAE,KAAM,CAAE,CAAE,CAAC,CAC/CxC,SAAS,CAAC,aAAa,CAAC,cACzBZ,IAAA,UACCyC,EAAE,CAAC,YAAY,CACf8H,IAAI,CAAC,MAAM,CACXsF,QAAQ,MACRC,MAAM,CAAC,+BAAgC;AAAA,CACvCnB,QAAQ,CAAEzD,gBAAiB,CAC3B/H,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAO,CAAE,CAC3B,CAAC,EACK,CAAC,cACTrD,IAAA,CAAC1B,UAAU,EAAC6E,KAAK,CAAE,CAAEmM,QAAQ,CAAE,MAAM,CAAEhB,KAAK,CAAE,SAAU,CAAE,CAAAZ,QAAA,CAAC,wBAAsB,CAAY,CAAC,EAC1F,CAAC,CACL9E,SAAS,eACT1I,KAAA,QACCiD,KAAK,CAAE,CACNE,OAAO,CAAE,MAAM,CACf8L,UAAU,CAAE,QAAQ,CACpBb,KAAK,CAAE,SAAS,CAChBzI,KAAK,CAAE,wBAAwB,CAC/BiI,OAAO,CAAE,OAAO,CAChBgB,SAAS,CAAE,MACZ,CAAE,CAAApB,QAAA,eAEF1N,IAAA,SACCmD,KAAK,CAAE,CAAE4M,WAAW,CAAE,KAAK,CAAE1M,OAAO,CAAE,MAAO,CAAE,CAC/CJ,uBAAuB,CAAE,CAAEC,MAAM,CAAEzD,OAAQ,CAAE,CAC7C,CAAC,cACFO,IAAA,QAAKmD,KAAK,CAAE,CAAEmM,QAAQ,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAAE9E,SAAS,CAAM,CAAC,EAC/C,CACL,cAGD1I,KAAA,CAAC7B,GAAG,EAACwP,EAAE,CAAE,CAAEhI,KAAK,CAAE,wBAAyB,CAAE,CAAA6H,QAAA,EAC3C,GAAG,CACHvI,KAAK,CAACR,GAAG,CAAC,CAACO,IAAI,CAAE8H,KAAK,gBACtB9M,KAAA,CAAC7B,GAAG,EAEHgF,OAAO,CAAC,MAAM,CACd8L,UAAU,CAAC,QAAQ,CACnBO,cAAc,CAAC,eAAe,CAC9B7B,EAAE,CAAE,CACHI,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACdI,MAAM,CAAE,KAAK,CACbG,eAAe,CAAE,SAClB,CAAE,CAAAX,QAAA,eAEF1N,IAAA,QACCyF,GAAG,CAAEC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAE,CAC/Bc,GAAG,CAAE,YAAYgH,KAAK,EAAG,CACzB7J,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEmI,YAAY,CAAE,KAAM,CAAE,CAC9D,CAAC,cAEFjO,IAAA,CAAC1B,UAAU,EAACuP,EAAE,CAAE,CAAEmC,IAAI,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEX,QAAQ,CAAE,MAAM,CAAEY,SAAS,CAAE,YAAa,CAAE,CAAAxC,QAAA,CAC5ExI,IAAI,CAACG,IAAI,CACC,CAAC,cACbnF,KAAA,CAAC1B,UAAU,EACVmP,OAAO,CAAEA,CAAA,GAAMZ,gBAAgB,CAACC,KAAK,CAAE,CACvCY,IAAI,CAAC,OAAO,CAAAF,QAAA,eAEZ1N,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE3D,UAAW,CAAE,CAChD4D,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,OAAO,CAAE,MAAO,CAAE,CACtC,CAAC,CAAC,GAAG,EACK,CAAC,GA5BR2J,KA6BD,CACL,CAAC,CAEDxE,OAAO,eACPtI,KAAA,CAAC7B,GAAG,EACHgF,OAAO,CAAC,MAAM,CACd8L,UAAU,CAAC,QAAQ,CACnBO,cAAc,CAAC,eAAe,CAC9B7B,EAAE,CAAE,CACHI,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACdI,MAAM,CAAE,KAAK,CACbG,eAAe,CAAE,SAClB,CAAE,CAAAX,QAAA,eAEF1N,IAAA,QACCyF,GAAG,CAAEC,GAAG,CAACC,eAAe,CAAC6C,OAAO,CAAE,CAClCxC,GAAG,CAAC,cAAc,CAClB7C,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEmI,YAAY,CAAE,KAAM,CAAE,CAC9D,CAAC,cACFjO,IAAA,CAAC1B,UAAU,EAACuP,EAAE,CAAE,CAAEmC,IAAI,CAAE,CAAC,CAAEV,QAAQ,CAAE,MAAM,CAAEY,SAAS,CAAE,YAAa,CAAE,CAAAxC,QAAA,CACrElF,OAAO,CAACnD,IAAI,CACF,CAAC,cACbnF,KAAA,CAAC1B,UAAU,EACVmP,OAAO,CAAET,eAAgB,CACzBU,IAAI,CAAC,OAAO,CAAAF,QAAA,eAEZ1N,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAE3D,UAAW,CAAE,CAChD4D,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,OAAO,CAAE,MAAO,CAAE,CACtC,CAAC,CAAC,GAAG,EACK,CAAC,EACT,CACL,CACAqF,SAAS,eACTxI,KAAA,CAAC7B,GAAG,EACHgF,OAAO,CAAC,MAAM,CACd8L,UAAU,CAAC,QAAQ,CACnBO,cAAc,CAAC,eAAe,CAC9B7B,EAAE,CAAE,CACHW,MAAM,CAAE,gBAAgB,CACxBP,YAAY,CAAE,KAAK,CACnBH,OAAO,CAAE,KAAK,CACdW,YAAY,CAAE,KAAK,CACnB5I,KAAK,CAAE,OAAO,CACdwI,eAAe,CAAE,SAClB,CAAE,CAAAX,QAAA,eAEFxN,KAAA,UACC2F,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXqK,QAAQ,MAAAzC,QAAA,eAER1N,IAAA,WACCyF,GAAG,CAAEC,GAAG,CAACC,eAAe,CAAC+C,SAAS,CAAE,CACpC6B,IAAI,CAAC,WAAW,CAChB,CAAC,+CAEH,EAAO,CAAC,cACRvK,IAAA,CAAC1B,UAAU,EAACuP,EAAE,CAAE,CAAEmC,IAAI,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEX,QAAQ,CAAE,MAAM,CAAEY,SAAS,CAAE,YAAa,CAAE,CAAAxC,QAAA,CAC5EhF,SAAS,CAACrD,IAAI,CACJ,CAAC,cACbrF,IAAA,CAACxB,UAAU,EACVmP,OAAO,CAAEN,iBAAkB,CAC3BO,IAAI,CAAC,OAAO,CAAAF,QAAA,cAEZ1N,IAAA,SACCiD,uBAAuB,CAAE,CAAEC,MAAM,CAAElE,UAAW,CAAE,CAChDmE,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,EACT,CACL,EACG,CAAC,EACF,CAAC,cAENlD,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAElD1N,IAAA,CAAC1B,UAAU,EACVmP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEW,YAAY,CAAE,gBAAiB,CAAE,CAAAf,QAAA,CAE/D9M,SAAS,CAAC,aAAa,CAAC,CACd,CAAC,cAEbZ,IAAA,CAACzB,SAAS,EACTwP,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZc,WAAW,CAAE9N,SAAS,CAAC,aAAa,CAAE,CACtC6M,SAAS,CAAC,qBAAqB,CAC/BtK,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAO,CAAE,CACzBxB,KAAK,CAAEd,6BAA6B,CAACS,UAAW,CAChD2K,QAAQ,CAAGxK,CAAC,EAAKkC,gBAAgB,CAAC,YAAY,CAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAC9D;AAAA,CACAuK,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEM,SAAS,CAAE,iBAAiB,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjJ,MAAM,CAAE,iBAAkB,CACpD,CACD,CAAE,CACF,CAAC,EACE,CAAC,cAEN5F,KAAA,CAAC7B,GAAG,EACHoE,EAAE,CAAC,mBAAmB,CACtBgL,SAAS,CAAC,wCAAwC,CAClDI,EAAE,CAAE,CAAEqB,aAAa,CAAE,QAAQ,CAAEpJ,MAAM,CAAE,iBAAiB,CAAEgI,OAAO,CAAE,gBAAiB,CAAE,CAAAJ,QAAA,eAEtF1N,IAAA,CAAC1B,UAAU,EACVmP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEW,YAAY,CAAE,gBAAiB,CAAE,CAAAf,QAAA,CAE/D9M,SAAS,CAAC,mBAAmB,CAAC,CACpB,CAAC,cAEbZ,IAAA,CAACzB,SAAS,EACTwP,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZc,WAAW,CAAE9N,SAAS,CAAC,YAAY,CAAE,CACrC6M,SAAS,CAAC,qBAAqB,CAC/BuB,SAAS,MACTC,OAAO,CAAE,CAAE,CACX9L,KAAK,CAAE,CAAE0C,KAAK,CAAE,MAAO,CAAE,CACzBxB,KAAK,CAAEd,6BAA6B,CAACU,gBAAiB,CACtD0K,QAAQ,CAAGxK,CAAC,EAAK,CAChB,GAAI,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC1B,GAAIA,KAAK,CAAC0B,MAAM,CAAG,GAAG,CAAE,CACvB1B,KAAK,CAAGA,KAAK,CAAC+L,KAAK,CAAC,CAAC,CAAE,GAAG,CAAC,CAC5B,CACA/J,gBAAgB,CAAC,kBAAkB,CAAEhC,KAAK,CAAC,CAC5C,CAAE,CACFgM,UAAU,CAAE,GAAG,EAAA7P,sBAAA,CAAA+C,6BAA6B,CAACU,gBAAgB,UAAAzD,sBAAA,iBAA9CA,sBAAA,CAAgDuF,MAAM,GAAI,CAAC,MAAO,CACjF6I,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEM,SAAS,CAAE,iBAAiB,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjJ,MAAM,CAAE,iBAAkB,CACpD,CACD,CAAE,CACF,CAAC,EACE,CAAC,EACF,CAAC,CACF,CAAC,cAEN9F,IAAA,QAAKyN,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC1N,IAAA,CAACvB,MAAM,EACNsP,OAAO,CAAC,WAAW,CACnBJ,OAAO,CAAElH,kBAAmB,CAC5BgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAEpB9M,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CAET,CAAC,CAED,cAAe,CAAAT,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}