# PopupLoader Component

## Overview

The PopupLoader component is a small loading indicator that appears in the top-right corner of web pages when the QuickAdapt browser extension is first activated. It provides visual feedback to users that the extension is initializing before the main interface (login page) becomes available.

## Features

- **Top-right positioning**: Positioned fixed in the top-right corner of the webpage
- **Non-intrusive**: Uses `pointer-events: none` to avoid interfering with page interactions
- **Responsive design**: Adapts to different screen sizes with mobile-friendly adjustments
- **RTL support**: Automatically adjusts positioning for right-to-left languages
- **Smooth animations**: Includes fade-in and fade-out animations for better UX
- **Extension branding**: Displays "QuickAdapt" text with the extension's color scheme

## Usage

### React Component

```tsx
import PopupLoader from './components/common/PopupLoader';

function MyComponent() {
  const [isLoading, setIsLoading] = useState(true);
  
  return (
    <PopupLoader isVisible={isLoading} />
  );
}
```

### Content Script Integration

The loader is automatically integrated into the content script and will:

1. **Show immediately** when the extension is activated (icon click, context menu, or URL parameter)
2. **Hide automatically** when the React app signals it's ready via the `quickadapt-app-ready` event
3. **Fallback timeout** of 3 seconds in case the ready event doesn't fire

### Manual Control

You can also manually hide the loader using the global function:

```javascript
// Hide the loader manually
window.hideQuickAdaptLoader();
```

## Styling

The component follows the extension's design system:

- **Colors**: Uses CSS custom properties (`--primarycolor`, `--white-color`, etc.)
- **Typography**: Uses the extension's font family (`--qadptfont-family`)
- **Border radius**: Consistent with extension's button border radius
- **Z-index**: High z-index (9999999) to ensure visibility above page content

## CSS Classes

- `.qadpt-popup-loader`: Main container with fixed positioning
- `.qadpt-loader-container`: Inner container with styling and layout
- `.qadpt-loader-spinner`: Animated spinning indicator
- `.qadpt-loader-text`: Text label with extension branding

## Animations

- **Fade-in**: Smooth entrance animation when loader appears
- **Spin**: Continuous rotation animation for the spinner
- **Fade-out**: Smooth exit animation when loader disappears

## Browser Compatibility

The loader is designed to work across all modern browsers and uses:

- CSS custom properties with fallbacks
- Standard CSS animations
- Flexbox for layout
- Fixed positioning for placement

## Testing

Run the component tests with:

```bash
npm test PopupLoader.test.tsx
```

The tests verify:
- Conditional rendering based on `isVisible` prop
- Correct CSS classes are applied
- Text content is displayed properly
