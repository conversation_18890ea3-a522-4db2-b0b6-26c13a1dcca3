{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\drawer\\\\AlertPopup.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Dialog, DialogActions, Button, DialogTitle, DialogContent, DialogContentText } from \"@mui/material\";\nimport WarningIcon from \"@mui/icons-material/Warning\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AlertPopup = ({\n  openWarning,\n  setopenWarning,\n  handleLeave\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    sx: {\n      zIndex: 9999999\n    },\n    open: openWarning,\n    onClose: () => setopenWarning(false),\n    PaperProps: {\n      style: {\n        borderRadius: \"4px\",\n        maxWidth: \"400px\",\n        textAlign: \"center\",\n        maxHeight: \"300px\",\n        boxShadow: \"none\"\n      }\n    },\n    className: \"qadpt-alertpopup\",\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        padding: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          padding: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: \"#e4b6b0\",\n            borderRadius: \"50%\",\n            width: \"40px\",\n            height: \"40px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n            sx: {\n              color: \"#F44336\",\n              height: \"20px\",\n              width: \"20px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        padding: \"20px !important\"\n      },\n      children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n        style: {\n          fontSize: \"14px\",\n          color: \"#000\"\n        },\n        children: translate(\"You Will Loose Changes, If you Leave With Out Saving\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        justifyContent: \"space-between\",\n        borderTop: \"1px solid var(--border-color)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setopenWarning(false),\n        sx: {\n          color: \"#9E9E9E\",\n          border: \"1px solid #9E9E9E\",\n          borderRadius: \"8px\",\n          textTransform: \"capitalize\",\n          padding: \"var(--button-padding)\",\n          lineHeight: \"var(--button-lineheight)\"\n        },\n        children: translate(\"Go Back\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLeave,\n        sx: {\n          backgroundColor: \"var(--error-color)\",\n          color: \"#FFF\",\n          borderRadius: \"8px\",\n          textTransform: \"capitalize\",\n          padding: \"var(--button-padding)\",\n          lineHeight: \"var(--button-lineheight)\"\n          // \"&:hover\": {\n          // \tbackgroundColor: \"#D32F2F\",\n          // },\n        },\n        children: translate(\"Close\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 4\n  }, this);\n};\n_s(AlertPopup, \"pr/MAE+x5KkRn8BZcdsayxNnBiM=\", false, function () {\n  return [useTranslation];\n});\n_c = AlertPopup;\nexport default AlertPopup;\nvar _c;\n$RefreshReg$(_c, \"AlertPopup\");", "map": {"version": 3, "names": ["React", "Dialog", "DialogActions", "<PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "WarningIcon", "useTranslation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openWarning", "setopenWarning", "handleLeave", "_s", "t", "translate", "sx", "zIndex", "open", "onClose", "PaperProps", "style", "borderRadius", "max<PERSON><PERSON><PERSON>", "textAlign", "maxHeight", "boxShadow", "className", "children", "padding", "display", "justifyContent", "backgroundColor", "width", "height", "alignItems", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "borderTop", "onClick", "border", "textTransform", "lineHeight", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/drawer/AlertPopup.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Box, Typography, Dialog, DialogActions, IconButton, Button, DialogTitle, DialogContent,DialogContentText } from \"@mui/material\";\r\nimport WarningIcon from \"@mui/icons-material/Warning\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst AlertPopup = ({\r\n    openWarning,\r\n    setopenWarning,\r\n    handleLeave,\r\n}: {\r\n  openWarning: boolean; setopenWarning: (params: boolean) => void; handleLeave: () => void;\r\n    }) => { \r\n\tconst { t: translate } = useTranslation();\r\n    return (\r\n\t\t\t<Dialog\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tzIndex: 9999999,\t\t\t\t}}\r\n\t\t\t\topen={openWarning}\r\n\t\t\t\tonClose={() => setopenWarning(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t}}\r\n\t\t\tclassName=\"qadpt-alertpopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<WarningIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t{/* <Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n                Delete {GuideTypetoDelete}\r\n            </Typography> */}\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t{translate(\"You Will Loose Changes, If you Leave With Out Saving\")}\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setopenWarning(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t{translate(\"Go Back\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleLeave}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t{translate(\"Close\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t);\r\n}\r\nexport default AlertPopup;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAA0BC,MAAM,EAAEC,aAAa,EAAcC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAACC,iBAAiB,QAAQ,eAAe;AACxI,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAMC,UAAU,GAAGA,CAAC;EAChBC,WAAW;EACXC,cAAc;EACdC;AAGA,CAAC,KAAK;EAAAC,EAAA;EACT,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGT,cAAc,CAAC,CAAC;EACtC,oBACDE,OAAA,CAACT,MAAM;IACNiB,EAAE,EAAE;MACHC,MAAM,EAAE;IAAY,CAAE;IACvBC,IAAI,EAAER,WAAY;IAClBS,OAAO,EAAEA,CAAA,KAAMR,cAAc,CAAC,KAAK,CAAE;IACrCS,UAAU,EAAE;MACXC,KAAK,EAAE;QACNC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE;MACZ;IACF,CAAE;IACFC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE3BpB,OAAA,CAACN,WAAW;MAACc,EAAE,EAAE;QAAEa,OAAO,EAAE;MAAE,CAAE;MAAAD,QAAA,eAC/BpB,OAAA;QAAKa,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEF,OAAO,EAAE;QAAO,CAAE;QAAAD,QAAA,eAC1EpB,OAAA;UACCa,KAAK,EAAE;YACNW,eAAe,EAAE,SAAS;YAC1BV,YAAY,EAAE,KAAK;YACnBW,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdJ,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBJ,cAAc,EAAE;UACjB,CAAE;UAAAH,QAAA,eAEFpB,OAAA,CAACH,WAAW;YAACW,EAAE,EAAE;cAAEoB,KAAK,EAAE,SAAS;cAAEF,MAAM,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIM,CAAC,eAEdhC,OAAA,CAACL,aAAa;MAACa,EAAE,EAAE;QAAEa,OAAO,EAAE;MAAkB,CAAE;MAAAD,QAAA,eACjDpB,OAAA,CAACJ,iBAAiB;QAACiB,KAAK,EAAE;UAAEoB,QAAQ,EAAE,MAAM;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAC7Db,SAAS,CAAC,sDAAsD;MAAC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEhBhC,OAAA,CAACR,aAAa;MAACgB,EAAE,EAAE;QAAEe,cAAc,EAAE,eAAe;QAAEW,SAAS,EAAE;MAAgC,CAAE;MAAAd,QAAA,gBAClGpB,OAAA,CAACP,MAAM;QACN0C,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAAC,KAAK,CAAE;QACrCK,EAAE,EAAE;UACHoB,KAAK,EAAE,SAAS;UAChBQ,MAAM,EAAE,mBAAmB;UAC3BtB,YAAY,EAAE,KAAK;UACnBuB,aAAa,EAAE,YAAY;UAC3BhB,OAAO,EAAE,uBAAuB;UAChCiB,UAAU,EAAE;QACb,CAAE;QAAAlB,QAAA,EAEFb,SAAS,CAAC,SAAS;MAAC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACThC,OAAA,CAACP,MAAM;QACN0C,OAAO,EAAE/B,WAAY;QACrBI,EAAE,EAAE;UACHgB,eAAe,EAAE,oBAAoB;UACrCI,KAAK,EAAE,MAAM;UACbd,YAAY,EAAE,KAAK;UACnBuB,aAAa,EAAE,YAAY;UAC3BhB,OAAO,EAAE,uBAAuB;UAChCiB,UAAU,EAAE;UACZ;UACA;UACA;QACD,CAAE;QAAAlB,QAAA,EAEFb,SAAS,CAAC,OAAO;MAAC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEZ,CAAC;AAAA3B,EAAA,CArFKJ,UAAU;EAAA,QAOUH,cAAc;AAAA;AAAAyC,EAAA,GAPlCtC,UAAU;AAsFhB,eAAeA,UAAU;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}