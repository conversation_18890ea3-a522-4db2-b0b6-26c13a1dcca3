{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\login\\\\AccountContext.tsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState, useEffect, useRef } from 'react';\n\n// Create the context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AccountContext = /*#__PURE__*/createContext({\n  accountId: '',\n  // Default value\n  setAccountId: () => {},\n  // Empty function as a placeholder\n  roles: [],\n  setRoles: () => []\n});\n\n// Provider component\nexport const AccountProvider = ({\n  children\n}) => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  // Get the default accountId from localStorage\n  const getDefaultAccountId = () => localStorage.getItem(\"accountId\") || \"\";\n  const [accountId, setAccountId] = useState(getDefaultAccountId());\n  const originalAccountIdRef = useRef(null);\n  useEffect(() => {\n    // On mount, check for qa_account_id in the URL\n\n    const params = new URLSearchParams(window.location.search);\n    const overrideAccountId = params.get('qa_account_id');\n    const defaultAccountId = getDefaultAccountId();\n    if (overrideAccountId && overrideAccountId !== defaultAccountId) {\n      // Cache the original accountId (if not already cached)\n      if (originalAccountIdRef.current === null) {\n        originalAccountIdRef.current = defaultAccountId;\n      }\n      setAccountId(overrideAccountId);\n      // Store override in localStorage for later use\n      localStorage.setItem('qa_account_id_override', overrideAccountId);\n      // Remove qa_account_id from the URL after use\n      params.delete('qa_account_id');\n      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;\n      window.history.replaceState({}, '', newUrl);\n    } else {\n      setAccountId(defaultAccountId);\n      // Remove any previous override if not using it\n      localStorage.removeItem('qa_account_id_override');\n    }\n    // On unload, restore the original accountId and clean up override\n    const handleUnload = () => {\n      if (originalAccountIdRef.current !== null) {\n        setAccountId(originalAccountIdRef.current);\n        originalAccountIdRef.current = null;\n      }\n      localStorage.removeItem('qa_account_id_override');\n    };\n    window.addEventListener('beforeunload', handleUnload);\n    return () => {\n      window.removeEventListener('beforeunload', handleUnload);\n      // Also restore on unmount\n      handleUnload();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsxDEV(AccountContext.Provider, {\n    value: {\n      accountId,\n      setAccountId,\n      roles,\n      setRoles\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountProvider, \"LbE589WhA1941ikebVgeZBou9L8=\");\n_c = AccountProvider;\nvar _c;\n$RefreshReg$(_c, \"AccountProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "AccountContext", "accountId", "setAccountId", "roles", "setRoles", "Account<PERSON><PERSON><PERSON>", "children", "_s", "getDefaultAccountId", "localStorage", "getItem", "originalAccountIdRef", "params", "URLSearchParams", "window", "location", "search", "overrideAccountId", "get", "defaultAccountId", "current", "setItem", "delete", "newUrl", "pathname", "toString", "hash", "history", "replaceState", "removeItem", "handleUnload", "addEventListener", "removeEventListener", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/login/AccountContext.tsx"], "sourcesContent": ["import React, { createContext, useState, ReactNode, useEffect, useRef } from 'react';\r\n\r\n// Create the context\r\nexport const AccountContext = createContext<{\r\n  accountId: string;\r\n  setAccountId: (id: string) => void;\r\n  roles: string[];\r\n  setRoles: (roles: [])=>void;\r\n}>({\r\n  accountId: '',      // Default value\r\n  setAccountId: () => { },  // Empty function as a placeholder\r\n  roles: [],\r\n  setRoles: () =>[],\r\n});\r\n\r\n// Provider component\r\nexport const AccountProvider = ({ children }: { children: ReactNode }) => {\r\n  const [roles, setRoles] = useState<string[]>([]); \r\n  // Get the default accountId from localStorage\r\n  const getDefaultAccountId = () => localStorage.getItem(\"accountId\") || \"\";\r\n  const [accountId, setAccountId] = useState<string>(getDefaultAccountId());\r\n  const originalAccountIdRef = useRef<string | null>(null);\r\n\r\n  useEffect(() => {\r\n\r\n    // On mount, check for qa_account_id in the URL\r\n\r\n    const params = new URLSearchParams(window.location.search);\r\n    \r\n    const overrideAccountId = params.get('qa_account_id');\r\n    const defaultAccountId = getDefaultAccountId();\r\n    if (overrideAccountId && overrideAccountId !== defaultAccountId) {\r\n      // Cache the original accountId (if not already cached)\r\n      if (originalAccountIdRef.current === null) {\r\n        originalAccountIdRef.current = defaultAccountId;\r\n      }\r\n      setAccountId(overrideAccountId);\r\n      // Store override in localStorage for later use\r\n      localStorage.setItem('qa_account_id_override', overrideAccountId);\r\n      // Remove qa_account_id from the URL after use\r\n      params.delete('qa_account_id');\r\n      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;\r\n      window.history.replaceState({}, '', newUrl);\r\n    } else {\r\n      setAccountId(defaultAccountId);\r\n      // Remove any previous override if not using it\r\n      localStorage.removeItem('qa_account_id_override');\r\n    }\r\n    // On unload, restore the original accountId and clean up override\r\n    const handleUnload = () => {\r\n      if (originalAccountIdRef.current !== null) {\r\n        setAccountId(originalAccountIdRef.current);\r\n        originalAccountIdRef.current = null;\r\n      }\r\n      localStorage.removeItem('qa_account_id_override');\r\n    };\r\n    window.addEventListener('beforeunload', handleUnload);\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleUnload);\r\n      // Also restore on unmount\r\n      handleUnload();\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n\r\n  return (\r\n    <AccountContext.Provider value={{ accountId, setAccountId,roles,setRoles }}>\r\n      {children}\r\n    </AccountContext.Provider>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAaC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEpF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,cAAc,gBAAGN,aAAa,CAKxC;EACDO,SAAS,EAAE,EAAE;EAAO;EACpBC,YAAY,EAAEA,CAAA,KAAM,CAAE,CAAC;EAAG;EAC1BC,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAEA,CAAA,KAAK;AACjB,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM,CAACJ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAW,EAAE,CAAC;EAChD;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE;EACzE,MAAM,CAACT,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAASa,mBAAmB,CAAC,CAAC,CAAC;EACzE,MAAMG,oBAAoB,GAAGd,MAAM,CAAgB,IAAI,CAAC;EAExDD,SAAS,CAAC,MAAM;IAEd;;IAEA,MAAMgB,MAAM,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAE1D,MAAMC,iBAAiB,GAAGL,MAAM,CAACM,GAAG,CAAC,eAAe,CAAC;IACrD,MAAMC,gBAAgB,GAAGX,mBAAmB,CAAC,CAAC;IAC9C,IAAIS,iBAAiB,IAAIA,iBAAiB,KAAKE,gBAAgB,EAAE;MAC/D;MACA,IAAIR,oBAAoB,CAACS,OAAO,KAAK,IAAI,EAAE;QACzCT,oBAAoB,CAACS,OAAO,GAAGD,gBAAgB;MACjD;MACAjB,YAAY,CAACe,iBAAiB,CAAC;MAC/B;MACAR,YAAY,CAACY,OAAO,CAAC,wBAAwB,EAAEJ,iBAAiB,CAAC;MACjE;MACAL,MAAM,CAACU,MAAM,CAAC,eAAe,CAAC;MAC9B,MAAMC,MAAM,GAAG,GAAGT,MAAM,CAACC,QAAQ,CAACS,QAAQ,GAAGZ,MAAM,CAACa,QAAQ,CAAC,CAAC,GAAG,IAAIb,MAAM,CAACa,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,GAAGX,MAAM,CAACC,QAAQ,CAACW,IAAI,EAAE;MACtHZ,MAAM,CAACa,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEL,MAAM,CAAC;IAC7C,CAAC,MAAM;MACLrB,YAAY,CAACiB,gBAAgB,CAAC;MAC9B;MACAV,YAAY,CAACoB,UAAU,CAAC,wBAAwB,CAAC;IACnD;IACA;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAInB,oBAAoB,CAACS,OAAO,KAAK,IAAI,EAAE;QACzClB,YAAY,CAACS,oBAAoB,CAACS,OAAO,CAAC;QAC1CT,oBAAoB,CAACS,OAAO,GAAG,IAAI;MACrC;MACAX,YAAY,CAACoB,UAAU,CAAC,wBAAwB,CAAC;IACnD,CAAC;IACDf,MAAM,CAACiB,gBAAgB,CAAC,cAAc,EAAED,YAAY,CAAC;IACrD,OAAO,MAAM;MACXhB,MAAM,CAACkB,mBAAmB,CAAC,cAAc,EAAEF,YAAY,CAAC;MACxD;MACAA,YAAY,CAAC,CAAC;IAChB,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EAGN,oBACE/B,OAAA,CAACC,cAAc,CAACiC,QAAQ;IAACC,KAAK,EAAE;MAAEjC,SAAS;MAAEC,YAAY;MAACC,KAAK;MAACC;IAAS,CAAE;IAAAE,QAAA,EACxEA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAAC/B,EAAA,CAvDWF,eAAe;AAAAkC,EAAA,GAAflC,eAAe;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}