{"ast": null, "code": "import React,{useState,useEffect,useContext}from\"react\";import{Box,Typography,TextField,IconButton,Button,InputAdornment,FormControl,Select,MenuItem,Tooltip,CircularProgress}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import{useTranslation}from'react-i18next';import useDrawerStore from\"../../store/drawerStore\";import SearchIcon from\"@mui/icons-material/Search\";import{deletestep,chkicn1,chkicn2,chkicn3,chkicn4,chkicn5,chkicn6,redirect,warning}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import CloudUploadOutlinedIcon from'@mui/icons-material/CloudUploadOutlined';import{getAllGuides}from\"../../services/GuideListServices\";import{AccountContext}from\"../login/AccountContext\";import'../../styles/rtl_styles.scss';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CheckPointAddPopup=_ref=>{var _checklistCheckpointL;let{checklistCheckpointProperties,setChecklistCheckpointProperties}=_ref;const{t:translate}=useTranslation();const[duplicateError,setDuplicateError]=useState(null);const{checklistGuideMetaData,setCheckPointsEditPopup,checkpointsEditPopup,titlePopup,setTitlePopup,setDesignPopup,titleColor,setTitleColor,checkpointsPopup,setCheckPointsPopup,checkpointTitleColor,setCheckpointTitleColor,checkpointTitleDescription,setCheckpointTitleDescription,checkpointIconColor,setCheckpointIconColor,setUnlockCheckPointInOrder,unlockCheckPointInOrder,checkPointMessage,setCheckPointMessage,setCheckPointsAddPopup,updateChecklistCheckPoints,updateChecklistCheckPointItem,setIsUnSavedChanges,isUnSavedChanges}=useDrawerStore(state=>state);const[selectedInteraction,setSelectedInteraction]=useState('');const[searchTerm,setSearchTerm]=useState('');const[checklistCheckpointListProperties,setChecklistCheckpointListProperties]=useState({id:'',interaction:'',title:'',description:'',redirectURL:'',icon:'',supportingMedia:'',mediaTitle:'',mediaDescription:''});const handleCheckPointIconColorChange=e=>setCheckpointIconColor(e.target.value);const handleCheckPointTitleColorChange=e=>setCheckpointTitleColor(e.target.value);const handleCheckPointDescriptionColorChange=e=>setCheckpointTitleColor(e.target.value);const[interactions,setInteractions]=useState([]);useEffect(()=>{if(selectedInteraction){const selectedInteractionData=filteredInteractions.find(interaction=>interaction.Name===selectedInteraction);if(selectedInteractionData){setChecklistCheckpointListProperties({interaction:selectedInteraction,title:selectedInteractionData.Name||'',description:selectedInteractionData.Description||'',redirectURL:selectedInteractionData.TargetUrl||'',icon:selectedInteractionData.targetUrl,supportingMedia:'',mediaTitle:selectedInteractionData.Name||'',mediaDescription:selectedInteractionData.description||'',id:selectedInteractionData.GuideId||''});}}},[selectedInteraction,interactions,searchTerm]);const handleClose=()=>{setCheckPointsAddPopup(false);};const handledesignclose=()=>{setDesignPopup(false);};const handleSizeChange=value=>{const sizeInPx=16+(value-1)*4;onPropertyChange(\"Size\",sizeInPx);};const onReselectElement=()=>{};const onPropertyChange=(key,value)=>{setChecklistCheckpointListProperties(prevState=>({...prevState,[key]:value}));};const[applyclicked,setapplyClisked]=useState(false);const handleApplyChanges=()=>{var _checklistCheckpointP;setFileError(null);setDuplicateError(null);checklistCheckpointListProperties.icon=icon;// Check if interaction is selected\nif(!checklistCheckpointListProperties.interaction||checklistCheckpointListProperties.interaction.trim()===\"\"){setDuplicateError(translate(\"Please select an interaction\"));return;}// Check for duplicate interaction\nconst isDuplicate=(_checklistCheckpointP=checklistCheckpointProperties.checkpointsList)===null||_checklistCheckpointP===void 0?void 0:_checklistCheckpointP.some(cp=>cp.interaction===checklistCheckpointListProperties.interaction);if(isDuplicate){setDuplicateError(translate(\"Interaction already used\"));return;}const updatedCheckpoint={...checklistCheckpointListProperties};updateChecklistCheckPointItem(updatedCheckpoint);setapplyClisked(true);setIsUnSavedChanges(true);setChecklistCheckpointProperties(prev=>({...prev,checkpointsList:[...prev.checkpointsList,// Keep existing checkpoints\nupdatedCheckpoint// Add the new one at the end\n]}));handleClose();};const handleEditClick=()=>{setCheckPointsEditPopup(true);};const[skip,setSkip]=useState(0);const[loading,setLoading]=useState(false);const[hasMore,setHasMore]=useState(true);const[filteredInteractions,setFilteredInteractions]=useState([]);const{accountId}=useContext(AccountContext);const[isSearching,setIsSearching]=useState(false);const top=20;useEffect(()=>{fetchData(0);// Fetch initial data to enable scrolling\n},[]);const fetchData=async function(newSkip){let newsearchTerm=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"\";if(newsearchTerm==\"\"&&loading)return;// Prevent duplicate calls\nif(newsearchTerm==\"\"&&searchTerm!=\"\")newsearchTerm=searchTerm;setLoading(true);const filters=[{FieldName:\"AccountId\",ElementType:\"string\",Condition:\"contains\",Value:accountId,IsCustomField:false},{FieldName:\"GuideType\",ElementType:\"string\",Condition:\"not equal\",Value:\"Checklist\",IsCustomField:false}];if(newsearchTerm!=\"\"){filters.push({FieldName:\"Name\",ElementType:\"string\",Condition:\"contains\",Value:newsearchTerm,IsCustomField:false});}try{const data=await getAllGuides(newSkip,top,filters,\"\");const newInteractions=data===null||data===void 0?void 0:data.results;if(newsearchTerm==\"\"){if(newSkip===0){setInteractions(newInteractions);setFilteredInteractions(newInteractions);}else{setInteractions(prev=>[...prev,...newInteractions]);setFilteredInteractions(prev=>[...prev,...newInteractions]);}}else{if(newSkip===0)setFilteredInteractions(newInteractions);else setFilteredInteractions(prev=>[...prev,...newInteractions]);}setSkip(newSkip+top);setHasMore(newInteractions.length>0);}catch(error){console.error(\"Error fetching guides:\",error);}finally{setLoading(false);}};const handleMenuScroll=event=>{const target=event.currentTarget;const{scrollTop,scrollHeight,clientHeight}=target;if(scrollHeight-scrollTop-clientHeight<50&&!loading&&hasMore){fetchData(skip);// no need to pass searchTerm\n}};const[isInteractionDropdownOpen,setIsInteractionDropdownOpen]=useState(false);const handleInteractionChange=newValue=>{setSelectedInteraction(newValue);onPropertyChange(\"interaction\",newValue);setIsInteractionDropdownOpen(false);setSearchTerm('');setDuplicateError(null);// Clear duplicate error when selecting a new interaction\n};const handleInteractionDropdownOpen=()=>{setIsInteractionDropdownOpen(true);if(searchTerm.trim()){fetchData(0,searchTerm);// Or your logic to update `filteredInteractions`\n}else{fetchData(0,\"\");// Optional: load default or all interactions if search is empty\n}};const handleInteractionDropdownClose=()=>{setIsInteractionDropdownOpen(false);setSearchTerm('');};const handleSearch=event=>{const term=event.target.value;setSearchTerm(term);if(!term.trim()){// If search is cleared\nsetFilteredInteractions(interactions);setIsSearching(false);}else{//   const filtered = interactions.filter((interaction) =>\n// \tinteraction.Name.toLowerCase().includes(term.toLowerCase())\n//   );\n//   setFilteredInteractions(filtered);\nsetIsSearching(true);setFilteredInteractions([]);fetchData(0,term);}};const[icons,setIcons]=useState([{id:1,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn1},style:{zoom:1,display:\"flex\"}}),selected:true},{id:2,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn2},style:{zoom:1,display:\"flex\"}}),selected:false},{id:3,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn3},style:{zoom:1,display:\"flex\"}}),selected:false},{id:4,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn4},style:{zoom:1,display:\"flex\"}}),selected:false},{id:5,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn5},style:{zoom:1,display:\"flex\"}}),selected:false},{id:6,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn6},style:{zoom:1,display:\"flex\"}}),selected:false}]);const[error,setError]=useState(null);const handleIconClick=async id=>{setIcons(prevIcons=>prevIcons.map(icon=>({...icon,selected:icon.id===id})));const selectedIcon=icons.find(icon=>icon.id===id);if(selectedIcon){var _selectedIcon$compone;const svgElement=(_selectedIcon$compone=selectedIcon.component.props.dangerouslySetInnerHTML)===null||_selectedIcon$compone===void 0?void 0:_selectedIcon$compone.__html;if(svgElement){const base64Icon=svgToBase64(svgElement);setIcon(base64Icon);checklistCheckpointListProperties.icon=base64Icon;}}};// Helper function to convert SVG to Base64\nconst svgToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};const handleFileUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(!file)return;const isIco=file.name.endsWith(\".ico\");// Validate the file type and size\nconst img=new Image();img.src=URL.createObjectURL(file);img.onload=()=>{if(!isIco||img.width>64||img.height>64){setError(translate(\"Please upload an .ico file less than 64x64px\"));}else{setError(null);setIcons(prevIcons=>[...prevIcons,{id:prevIcons.length+1,component:/*#__PURE__*/_jsx(\"img\",{src:img.src,alt:\"Custom Icon\",width:24}),selected:false}]);}};};const[icon,setIcon]=useState();useEffect(()=>{const initialSelectedIcon=icons.find(icon=>icon.selected);if(initialSelectedIcon&&!checklistCheckpointListProperties.icon){var _initialSelectedIcon$;const svgElement=(_initialSelectedIcon$=initialSelectedIcon.component.props.dangerouslySetInnerHTML)===null||_initialSelectedIcon$===void 0?void 0:_initialSelectedIcon$.__html;if(svgElement){const base64Icon=svgToBase64(svgElement);setIcon(base64Icon);checklistCheckpointListProperties.icon=base64Icon;}}},[]);const[files,setFiles]=useState([]);const[gifFile,setGifFile]=useState(null);const[videoFile,setVideoFile]=useState(null);const[imageType,setImageType]=useState(null);// Track locked image type\nconst[fileError,setFileError]=useState(null);const handleFileChange=async event=>{setFileError(null);if(!event.target.files)return;const newFiles=Array.from(event.target.files);const isAllGif=newFiles.every(file=>file.type===\"image/gif\");const isAllMp4=newFiles.every(file=>file.type===\"video/mp4\");const isAllImages=newFiles.every(file=>[\"image/jpeg\",\"image/png\",\"image/jpg\"].includes(file.type));const isMixedType=!(isAllGif||isAllMp4||isAllImages);if(isMixedType){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}if(gifFile){if(isAllGif){setFileError(translate(\"Only one GIF is allowed.\"));return;}else{setFileError(translate(\"Mixed file formats are not allowed.\"));return;}}if(videoFile){if(isAllMp4){setFileError(translate(\"Only one Video is allowed.\"));return;}else{setFileError(translate(\"Mixed file formats are not allowed.\"));return;}}if(files.length>0){if(!isAllImages){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}if(imageType&&!newFiles.every(file=>file.type===imageType)){setFileError(translate(\"Mixed file formats are not allowed.\"));return;}}if(isAllGif){if(newFiles.length>1){setFileError(translate(\"Only one GIF is allowed.\"));return;}setGifFile(newFiles[0]);}else if(isAllMp4){if(newFiles.length>1){setFileError(translate(\"Only one Video is allowed.\"));return;}setVideoFile(newFiles[0]);}else if(isAllImages){const newImageType=newFiles[0].type;if(!imageType){setImageType(newImageType);// Lock the image type\n}setFiles(prevFiles=>{const updatedFiles=[...prevFiles,...newFiles];updatedFiles.sort((a,b)=>{const nameA=a.name.match(/\\d+/)?parseInt(a.name.match(/\\d+/)[0],10):0;const nameB=b.name.match(/\\d+/)?parseInt(b.name.match(/\\d+/)[0],10):0;return nameA-nameB;});return updatedFiles;});}const base64Files=await Promise.all(newFiles.map(async file=>({Name:file.name,Type:file.type,Base64:await fileToBase64(file)})));setChecklistCheckpointListProperties(prevState=>{const updatedMedia=[...(prevState.supportingMedia||[]),...base64Files];updatedMedia.sort((a,b)=>{const nameA=a.Name.match(/\\d+/)?parseInt(a.Name.match(/\\d+/)[0],10):0;const nameB=b.Name.match(/\\d+/)?parseInt(b.Name.match(/\\d+/)[0],10):0;return nameA-nameB;});return{...prevState,supportingMedia:updatedMedia};});};const fileToBase64=file=>{return new Promise((resolve,reject)=>{const reader=new FileReader();reader.readAsDataURL(file);reader.onload=()=>resolve(reader.result);reader.onerror=error=>reject(error);});};const handleDeleteFile=index=>{setFileError(null);setFiles(prevFiles=>{const newFiles=prevFiles.filter((_,i)=>i!==index);if(newFiles.length===0)setImageType(null);// Reset image lock\nsetChecklistCheckpointListProperties(prevProperties=>({...prevProperties,supportingMedia:prevProperties.supportingMedia.filter((_,i)=>i!==index)}));return newFiles;});};const handleDeleteGif=()=>{setGifFile(null);setChecklistCheckpointListProperties(prevProperties=>({...prevProperties,supportingMedia:prevProperties.supportingMedia.filter(file=>{var _file$Name;return!((_file$Name=file.Name)!==null&&_file$Name!==void 0&&_file$Name.toLowerCase().endsWith(\".gif\"));})}));};const handleDeleteVideo=()=>{setVideoFile(null);setChecklistCheckpointListProperties(prevProperties=>({...prevProperties,supportingMedia:prevProperties.supportingMedia.filter(file=>{var _file$Name2;return!((_file$Name2=file.Name)!==null&&_file$Name2!==void 0&&_file$Name2.toLowerCase().endsWith(\".mp4\"));})}));};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"back\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-title\",children:[translate(\"Step\"),\" \",checklistGuideMetaData[0].checkpoints.checkpointsList.length+1]}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\"// sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0px !important\" }}\n,children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Interaction\")}),/*#__PURE__*/_jsx(FormControl,{variant:\"outlined\",fullWidth:true,className:\"qadpt-control-input\",sx:{width:\"100% !important\",borderRadius:\"12px\",padding:\"0\",margin:\"0 !important\"},children:/*#__PURE__*/_jsxs(Select,{open:isInteractionDropdownOpen,onOpen:handleInteractionDropdownOpen,onClose:handleInteractionDropdownClose,value:selectedInteraction,onChange:e=>{const newValue=e.target.value;setSelectedInteraction(newValue);onPropertyChange(\"interaction\",newValue);},name:\"ShowUpon\",displayEmpty:true,sx:{width:\"100% !important\",textAlign:\"left\",\"& .MuiOutlinedInput-root\":{\"&:hover\":{borderColor:\"none !important\"},\"&.Mui-focused\":{borderColor:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.MuiInputBase-root\":{height:\"35px !important\"}},MenuProps:{PaperProps:{sx:{maxHeight:430,overflowY:\"auto\",maxWidth:\"220px\",\"& ul li\":{whiteSpace:\"normal\",wordBreak:\"break-word\",overflowWrap:\"break-word\",maxWidth:\"100%\"},\"& .MuiFormControl-root .MuiInputBase-root\":{borderRadius:\"12px\",padding:\"1px !important\"},\"& .MuiOutlinedInput-root\":{\"&:hover\":{borderColor:\"#ccc !important\"},\"&.Mui-focused\":{borderColor:\"#ccc !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{borderColor:\"#ccc !important\",borderWidth:\"1px !important\"}}},onClose:handleInteractionDropdownClose},renderValue:selected=>selected||translate(\"Select Interaction\"),children:[/*#__PURE__*/_jsx(Box,{sx:{position:\"sticky\",top:0,left:0,right:0,zIndex:1,backgroundColor:\"white\",padding:\"8px\",borderBottom:\"1px solid #eee\"}// onClick={(e) => e.stopPropagation()} // Prevent click from closing dropdown\n,onKeyDown:e=>e.stopPropagation()// Prevent dropdown from closing on typing\n,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:translate(\"Search interactions...\"),variant:\"outlined\",size:\"small\",value:searchTerm,onClick:e=>{e.stopPropagation();// Prevent TextField click from closing dropdown\nsetIsInteractionDropdownOpen(true);},onKeyDown:e=>{e.stopPropagation();// Prevent keypress from closing dropdown\nif(e.key===\"Escape\"){handleInteractionDropdownClose();// Allow Escape to close dropdown\n}if(e.key===\"Enter\"){searchTerm.trim()?fetchData(0,searchTerm):fetchData(0,\"\");// 🔁 Same logic as icon button\n}},onChange:e=>setSearchTerm(e.target.value)// Call search handler directly\n,autoFocus:true,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"search\",onClick:e=>{e.stopPropagation();if(searchTerm.trim()){// Only search if there's a non-empty term\nfetchData(0,searchTerm);}else{fetchData(0,\"\");}},children:/*#__PURE__*/_jsx(SearchIcon,{})})})},sx:{\"& .MuiOutlinedInput-root\":{borderRadius:\"4px\"},\"& .MuiInputAdornment-root\":{margin:\"0 !important\"}}})}),/*#__PURE__*/_jsxs(Box,{sx:{maxHeight:\"352px\",overflowY:\"auto\"},onScroll:handleMenuScroll// Attach the event directly here\n,children:[filteredInteractions&&filteredInteractions.length===0&&!loading&&/*#__PURE__*/_jsx(MenuItem,{disabled:true,children:translate(\"No interactions found\")}),filteredInteractions&&filteredInteractions.map(interaction=>/*#__PURE__*/_jsx(MenuItem,{value:interaction.Name,onClick:()=>{handleInteractionChange(interaction.Name);// onPropertyChange(\"interaction\", interaction.Name);\n},children:interaction.Name},interaction.GuideId)),loading&&/*#__PURE__*/_jsx(MenuItem,{disabled:true,sx:{display:\"flex\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(CircularProgress,{size:20})})]})]})}),duplicateError&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#e53935',padding:10,marginBottom:8,fontSize:13,textAlign:'left'},children:duplicateError})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Title\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Step Title\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointListProperties.title,onChange:e=>onPropertyChange(\"title\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 !important\",marginBottom:\"8px !important\"},children:translate(\"Description\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Step Desc\"),className:\"qadpt-control-input\",multiline:true,minRows:3,value:checklistCheckpointListProperties.description,onChange:e=>onPropertyChange(\"description\",e.target.value),style:{width:\"100%\"},InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",style:{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",gap:\"5px\",padding:\"0\",marginBottom:\"10px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"#444444\",fontWeight:\"600\"},children:translate(\"Redirect URL\")}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:redirect},style:{display:\"flex\"}}),\" \"]}),/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"11px\",color:\"#8d8d8d\",textAlign:\"left\",padding:\"0\",marginBottom:\"10px\"},children:[\"         \",translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")]}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Redirection URL\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointListProperties.redirectURL,onChange:e=>onPropertyChange(\"redirectURL\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\"// sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\n,children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Icon\")}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",gap:1,alignItems:\"center\",width:\"-webkit-fill-available\",flexWrap:\"wrap\"},children:icons.map(icon=>/*#__PURE__*/_jsx(Tooltip,{title:\"Select Icon\",arrow:true,children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIconClick(icon.id),sx:{border:icon.selected?\"2px solid var(--primarycolor)\":\"none\",borderRadius:\"8px\",padding:\"8px\",background:\"#F1ECEC\"},children:icon.component})},icon.id))})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\"//   sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\"}}\n,children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 8px 0 !important\"},children:translate(\"Supporting Media\")}),/*#__PURE__*/_jsxs(\"div\",{style:{width:\"165px\",height:\"auto\",margin:\"0 8px 8px 8px\",display:\"flex\",flexDirection:\"column\",alignItems:\"center\",justifyContent:\"center\",border:\"1px dashed var(--primarycolor)\",borderRadius:\"12px\",padding:\"8px\",background:\"#F1ECEC\",textAlign:\"center\"},children:[/*#__PURE__*/_jsxs(Button,{className:\"qadpt-upload-button\",style:{height:\"auto\",padding:\"0\",width:\"100%\",display:\"flex\",flexDirection:\"row\",// Ensures icon & text are in one line\nalignItems:\"center\",justifyContent:\"center\",gap:\"6px\",color:\"#000\",backgroundColor:\"#F1ECEC\",textTransform:\"capitalize\",boxShadow:\"none\"},component:\"label\",children:[/*#__PURE__*/_jsx(CloudUploadOutlinedIcon,{sx:{zoom:\"1.6\"}}),\"Upload file\",/*#__PURE__*/_jsx(\"input\",{id:\"file-input\",type:\"file\",multiple:true,accept:\".jpeg, .jpg, .png, .gif, .mp4\",onChange:handleFileChange,style:{display:\"none\"}})]}),/*#__PURE__*/_jsx(Typography,{style:{fontSize:\"12px\",color:\"#A3A3A3\"},children:\".png, .jpg, .gif, .mp4\"})]}),fileError&&/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",color:\"#e6a957\",padding:\"0 8px\",textAlign:\"left\",width:\"-webkit-fill-available\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"4px\",display:\"flex\"},dangerouslySetInnerHTML:{__html:warning}}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"12px\"},children:fileError})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:\"-webkit-fill-available\"},children:[files.map((file,index)=>/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{borderRadius:\"12px\",padding:\"8px\",margin:\"8px\",backgroundColor:\"#e5dada\"},children:[/*#__PURE__*/_jsx(\"img\",{src:URL.createObjectURL(file),alt:`uploaded-${index}`,style:{width:\"20px\",height:\"20px\",borderRadius:\"5px\"}}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,fontSize:\"14px\",wordBreak:\"break-word\"},children:file.name}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleDeleteFile(index),size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:\"1\",display:\"flex\"}})})]},index)),gifFile&&/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{borderRadius:\"12px\",padding:\"8px\",margin:\"5px\",backgroundColor:\"#e5dada\"},children:[/*#__PURE__*/_jsx(\"img\",{src:URL.createObjectURL(gifFile),alt:\"uploaded-gif\",style:{width:\"20px\",height:\"20px\",borderRadius:\"5px\"}}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,fontSize:\"14px\",wordBreak:\"break-word\"},children:gifFile.name}),/*#__PURE__*/_jsx(IconButton,{onClick:handleDeleteGif,size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:\"1\",display:\"flex\"}})})]}),videoFile&&/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",sx:{border:\"1px solid #0a6\",borderRadius:\"5px\",padding:\"8px\",marginBottom:\"5px\",width:\"196px\",backgroundColor:\"#e6ffe6\"},children:[/*#__PURE__*/_jsxs(\"video\",{width:\"40\",height:\"40\",controls:true,children:[/*#__PURE__*/_jsx(\"source\",{src:URL.createObjectURL(videoFile),type:\"video/mp4\"}),\"Your browser does not support the video tag.\"]}),/*#__PURE__*/_jsx(Typography,{sx:{flex:1,ml:2,fontSize:\"14px\",wordBreak:\"break-word\"},children:videoFile.name}),/*#__PURE__*/_jsx(IconButton,{onClick:handleDeleteVideo,size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:0.7}})})]})]})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{paddingBottom:\"8 !important\"},children:translate(\"Media Title\")}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Media Title\"),className:\"qadpt-control-input\",style:{width:\"100%\"},value:checklistCheckpointListProperties.mediaTitle,onChange:e=>onPropertyChange(\"mediaTitle\",e.target.value),InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box qadpt-chkcontrol-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{paddingBottom:\"8px !important\"},children:translate(\"Media Description\")}),/*#__PURE__*/_jsx(TextField,{value:checklistCheckpointListProperties.mediaDescription,onChange:e=>{let value=e.target.value;if(value.length>200){value=value.slice(0,200);}onPropertyChange(\"mediaDescription\",value);},variant:\"outlined\",size:\"small\",placeholder:translate(\"Media Desc\"),className:\"qadpt-control-input\",multiline:true,minRows:3,style:{width:\"100%\"},helperText:`${((_checklistCheckpointL=checklistCheckpointListProperties.mediaDescription)===null||_checklistCheckpointL===void 0?void 0:_checklistCheckpointL.length)||0}/200`,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:\"qadpt-btn\",children:\"Apply\"})})]})})});};export default CheckPointAddPopup;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "InputAdornment", "FormControl", "Select", "MenuItem", "<PERSON><PERSON><PERSON>", "CircularProgress", "CloseIcon", "useTranslation", "useDrawerStore", "SearchIcon", "deletestep", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "redirect", "warning", "ArrowBackIosNewOutlinedIcon", "CloudUploadOutlinedIcon", "getAllGuides", "AccountContext", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CheckPointAddPopup", "_ref", "_checklistCheckpointL", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "t", "translate", "duplicateError", "setDuplicateError", "checklistGuideMetaData", "setCheckPointsEditPopup", "checkpointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "setCheckPointsAddPopup", "updateChecklistCheckPoints", "updateChecklistCheckPointItem", "setIsUnSavedChanges", "isUnSavedChanges", "state", "selectedInteraction", "setSelectedInteraction", "searchTerm", "setSearchTerm", "checklistCheckpointListProperties", "setChecklistCheckpointListProperties", "id", "interaction", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "handleCheckPointIconColorChange", "e", "target", "value", "handleCheckPointTitleColorChange", "handleCheckPointDescriptionColorChange", "interactions", "setInteractions", "selectedInteractionData", "filteredInteractions", "find", "Name", "Description", "TargetUrl", "targetUrl", "GuideId", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "prevState", "applyclicked", "setapplyClisked", "handleApplyChanges", "_checklistCheckpointP", "setFileError", "trim", "isDuplicate", "checkpointsList", "some", "cp", "updatedCheckpoint", "prev", "handleEditClick", "skip", "setSkip", "loading", "setLoading", "hasMore", "setHasMore", "setFilteredInteractions", "accountId", "isSearching", "setIsSearching", "top", "fetchData", "newSkip", "newsearchTerm", "arguments", "length", "undefined", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "push", "data", "newInteractions", "results", "error", "console", "handleMenuScroll", "event", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "isInteractionDropdownOpen", "setIsInteractionDropdownOpen", "handleInteractionChange", "newValue", "handleInteractionDropdownOpen", "handleInteractionDropdownClose", "handleSearch", "term", "icons", "setIcons", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "selected", "setError", "handleIconClick", "prevIcons", "map", "selectedIcon", "_selectedIcon$compone", "svgElement", "props", "base64Icon", "svgToBase64", "setIcon", "svgString", "btoa", "handleFileUpload", "_event$target$files", "file", "files", "isIco", "name", "endsWith", "img", "Image", "src", "URL", "createObjectURL", "onload", "width", "height", "alt", "initialSelectedIcon", "_initialSelectedIcon$", "setFiles", "gifFile", "setGifFile", "videoFile", "setVideoFile", "imageType", "setImageType", "fileError", "handleFileChange", "newFiles", "Array", "from", "isAllGif", "every", "type", "isAllMp4", "isAllImages", "includes", "isMixedType", "newImageType", "prevFiles", "updatedFiles", "sort", "a", "b", "nameA", "match", "parseInt", "nameB", "base64Files", "Promise", "all", "Type", "Base64", "fileToBase64", "updatedMedia", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "result", "onerror", "handleDeleteFile", "index", "filter", "_", "i", "prevProperties", "handleDeleteGif", "_file$Name", "toLowerCase", "handleDeleteVideo", "_file$Name2", "children", "className", "onClick", "checkpoints", "size", "sx", "padding", "variant", "fullWidth", "borderRadius", "margin", "open", "onOpen", "onClose", "onChange", "displayEmpty", "textAlign", "borderColor", "border", "MenuProps", "PaperProps", "maxHeight", "overflowY", "max<PERSON><PERSON><PERSON>", "whiteSpace", "wordBreak", "overflowWrap", "borderWidth", "renderValue", "position", "left", "right", "zIndex", "backgroundColor", "borderBottom", "onKeyDown", "stopPropagation", "placeholder", "autoFocus", "InputProps", "startAdornment", "onScroll", "disabled", "justifyContent", "color", "marginBottom", "fontSize", "endAdornment", "paddingLeft", "multiline", "minRows", "flexDirection", "alignItems", "gap", "fontWeight", "flexWrap", "arrow", "background", "textTransform", "boxShadow", "multiple", "accept", "marginRight", "flex", "controls", "ml", "paddingBottom", "slice", "helperText"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/CheckpointAddPopup.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useRef, useContext, useCallback } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress, ClickAwayListener } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n    deletestep,\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tredirect,\r\n\twarning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport '../../styles/rtl_styles.scss';\r\n\r\n\r\nconst CheckPointAddPopup = ({ checklistCheckpointProperties, setChecklistCheckpointProperties }: { checklistCheckpointProperties: any; setChecklistCheckpointProperties:any}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [duplicateError, setDuplicateError] = useState<string | null>(null);\r\n\tconst {\r\n\t\tchecklistGuideMetaData,\r\n        setCheckPointsEditPopup,\r\n        checkpointsEditPopup,\r\n        titlePopup,\r\n        setTitlePopup,\r\n        setDesignPopup,\r\n        titleColor,\r\n        setTitleColor,\r\n        checkpointsPopup,\r\n        setCheckPointsPopup,\r\n        checkpointTitleColor,\r\n        setCheckpointTitleColor,\r\n        checkpointTitleDescription,\r\n        setCheckpointTitleDescription,\r\n        checkpointIconColor,\r\n        setCheckpointIconColor,\r\n        setUnlockCheckPointInOrder,\r\n\t    unlockCheckPointInOrder,\r\n        checkPointMessage,\r\n        setCheckPointMessage,\r\n\t\tsetCheckPointsAddPopup,\r\n\t\tupdateChecklistCheckPoints,\r\n\t\tupdateChecklistCheckPointItem,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges\r\n\t\r\n      \r\n\t\r\n    } = useDrawerStore((state: any) => state);\r\n    \r\n\r\n\tconst [selectedInteraction, setSelectedInteraction] = useState<string>('');\r\n\tconst [searchTerm, setSearchTerm] = useState('');\r\n\r\n\tconst [checklistCheckpointListProperties, setChecklistCheckpointListProperties] = useState<any>({\r\n\t\tid:'',\r\n\t\tinteraction: '',\r\n\t\ttitle: '',\r\n\t\tdescription: '',\r\n\t\tredirectURL: '',\r\n\t\ticon: '',\r\n\t\tsupportingMedia: '',\r\n\t\tmediaTitle: '',\r\n\t\tmediaDescription: '',\r\n\t});\r\n\t\r\n\r\n    const handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);\r\n    const handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\tconst handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (selectedInteraction) {\r\n\t\t\tconst selectedInteractionData = filteredInteractions.find(interaction => interaction.Name === selectedInteraction);\r\n\t\t\t\r\n\t\t\tif (selectedInteractionData) {\r\n\t\t\t\tsetChecklistCheckpointListProperties({\r\n\t\t\t\t\tinteraction: selectedInteraction,\r\n\t\t\t\t\ttitle: selectedInteractionData.Name || '',\r\n\t\t\t\t\tdescription: selectedInteractionData.Description || '',\r\n\t\t\t\t\tredirectURL: selectedInteractionData.TargetUrl || '',\r\n\t\t\t\t\ticon: selectedInteractionData.targetUrl,\r\n\t\t\t\t\tsupportingMedia: '',\r\n\t\t\t\t\tmediaTitle: selectedInteractionData.Name || '',\r\n\t\t\t\t\tmediaDescription: selectedInteractionData.description || '',\r\n\t\t\t\t\tid:selectedInteractionData.GuideId||'',\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}, [selectedInteraction, interactions, searchTerm]);\r\n\t\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetCheckPointsAddPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {\r\n\t\t\t\r\n\t\t};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetChecklistCheckpointListProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t};\r\n\tconst [applyclicked, setapplyClisked] = useState(false);\r\n\tconst handleApplyChanges = () => {\r\n\t\tsetFileError(null);\r\n\t\tsetDuplicateError(null);\r\n\t\t\tchecklistCheckpointListProperties.icon = icon;\r\n\r\n\t\t// Check if interaction is selected\r\n\t\tif (!checklistCheckpointListProperties.interaction || checklistCheckpointListProperties.interaction.trim() === \"\") {\r\n\t\t\tsetDuplicateError(translate(\"Please select an interaction\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Check for duplicate interaction\r\n\t\tconst isDuplicate = checklistCheckpointProperties.checkpointsList?.some(\r\n\t\t\t(cp: any) => cp.interaction === checklistCheckpointListProperties.interaction\r\n\t\t);\r\n\t\tif (isDuplicate) {\r\n\t\t\tsetDuplicateError(translate(\"Interaction already used\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t\tconst updatedCheckpoint = {\r\n\t\t\t\t...checklistCheckpointListProperties,\r\n\t\t\t};\r\n\t\t\tupdateChecklistCheckPointItem(updatedCheckpoint);\r\n\t\t\tsetapplyClisked(true);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tcheckpointsList: [\r\n\t\t\t\t\t...prev.checkpointsList, // Keep existing checkpoints\r\n\t\t\t\t\tupdatedCheckpoint,       // Add the new one at the end\r\n\t\t\t\t],\r\n\t\t\t}));\r\n\r\n\t\t\thandleClose();\r\n\t\t};\r\n\t\t\r\n\r\n\tconst handleEditClick = () => {\r\n        setCheckPointsEditPopup(true);\r\n    }\r\n\tconst [skip, setSkip] = useState(0);\r\nconst [loading, setLoading] = useState(false);\r\nconst [hasMore, setHasMore] = useState(true);\r\nconst [filteredInteractions, setFilteredInteractions] = useState<any[]>([]);\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst [isSearching, setIsSearching] = useState(false);\r\nconst top = 20;\r\n\r\nuseEffect(() => {\r\n  fetchData(0);  // Fetch initial data to enable scrolling\r\n}, []);\r\n\r\n\r\n\r\nconst fetchData = async (newSkip: number,newsearchTerm:string=\"\") => {\r\n\tif (newsearchTerm==\"\" && loading) return;  // Prevent duplicate calls\r\n\tif(newsearchTerm == \"\" && searchTerm != \"\") newsearchTerm = searchTerm;\r\n\tsetLoading(true);\r\n  \r\n\tconst filters = [\r\n\t  {\r\n\t\tFieldName: \"AccountId\",\r\n\t\tElementType: \"string\",\r\n\t\tCondition: \"contains\",\r\n\t\tValue: accountId,\r\n\t\tIsCustomField: false,\r\n\t  },\r\n\t  {\r\n\t\tFieldName: \"GuideType\",\r\n\t\tElementType: \"string\",\r\n\t\tCondition: \"not equal\",\r\n\t\tValue: \"Checklist\",\r\n\t\tIsCustomField: false,\r\n\t  }\r\n\t];\r\n\tif(newsearchTerm != \"\"){\r\n\t\tfilters.push({\r\n\t\t\tFieldName:\"Name\",\r\n\t\t\tElementType:\"string\",\r\n\t\t\tCondition:\"contains\",\r\n\t\t\tValue:newsearchTerm,\r\n\t\t\tIsCustomField:false\r\n\t\t});\r\n\t}\r\n  \r\n\ttry {\r\n\t  const data = await getAllGuides(newSkip, top, filters, \"\");\r\n\t const newInteractions = data?.results;\r\n   if(newsearchTerm==\"\") {\r\n\t  if (newSkip === 0) {\r\n\t\tsetInteractions(newInteractions);  \r\n\t\tsetFilteredInteractions(newInteractions);\r\n\t  } else {\r\n\t\tsetInteractions((prev) => [...prev, ...newInteractions]); \r\n\t\tsetFilteredInteractions((prev) => [...prev, ...newInteractions]);\r\n\t  }\r\n\t}\r\n\telse {\r\n       if(newSkip === 0) setFilteredInteractions(newInteractions);\r\n\t   else setFilteredInteractions((prev) => [...prev, ...newInteractions]);\r\n\t}\r\n\t  setSkip(newSkip + top);\r\n\t  setHasMore(newInteractions.length > 0);\r\n\t} catch (error) {\r\n\t  console.error(\"Error fetching guides:\", error);\r\n\t} finally {\r\n\t  setLoading(false);\r\n\t}\r\n  };\r\n  \r\nconst handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {\r\n\tconst target = event.currentTarget;\r\n\tconst { scrollTop, scrollHeight, clientHeight } = target;\r\n  \r\n\tif (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore ) {\r\n\t   fetchData(skip); // no need to pass searchTerm\r\n\t}\r\n  };\r\n  \r\n  const [isInteractionDropdownOpen, setIsInteractionDropdownOpen] = useState(false);\r\n  const handleInteractionChange = (newValue: string) => {\r\n\t  setSelectedInteraction(newValue);\r\n\t  onPropertyChange(\"interaction\", newValue);\r\n\t  setIsInteractionDropdownOpen(false);\r\n\t  setSearchTerm('');\r\n\t\tsetDuplicateError(null); // Clear duplicate error when selecting a new interaction\r\n  };\r\n\r\n  const handleInteractionDropdownOpen = () => {\r\n\t  setIsInteractionDropdownOpen(true);\r\n\t  if (searchTerm.trim()) {\r\n\t\tfetchData(0, searchTerm); // Or your logic to update `filteredInteractions`\r\n\t  } else {\r\n\t\tfetchData(0, \"\"); // Optional: load default or all interactions if search is empty\r\n\t  }\r\n  };\r\n\r\n  const handleInteractionDropdownClose = () => {\r\n\t  setIsInteractionDropdownOpen(false);\r\n\t  setSearchTerm('');\r\n  };\r\nconst handleSearch =  (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\tconst term = event.target.value;\r\n\tsetSearchTerm(term);\r\n  \r\n\tif (!term.trim()) {\r\n\t  // If search is cleared\r\n\t  setFilteredInteractions(interactions);\r\n\t  setIsSearching(false);\r\n\t} else {\r\n\t//   const filtered = interactions.filter((interaction) =>\r\n\t// \tinteraction.Name.toLowerCase().includes(term.toLowerCase())\r\n\t//   );\r\n\t//   setFilteredInteractions(filtered);\r\n\tsetIsSearching(true);\r\n\tsetFilteredInteractions([]);\r\n\t fetchData(0,term);\r\n\t}\r\n  };\r\n  \r\n\t  \r\n  const [icons, setIcons] = useState<any[]>([\r\n\t{ id: 1, component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: true },\r\n\t{ id: 2, component:  <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t{ id: 3, component:  <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1,display:\"flex\" }} />, selected: false },\r\n\t  { id: 4, component:  <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1,display:\"flex\" }} /> , selected: false },\r\n\t  { id: 5, component:  <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t  { id: 6, component:  <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t]);\r\nconst [error, setError] = useState<string | null>(null);\r\n\r\nconst handleIconClick = async (id: number) => {\r\n    setIcons(prevIcons =>\r\n        prevIcons.map(icon => ({\r\n            ...icon,\r\n            selected: icon.id === id,\r\n        }))\r\n    );\r\n\r\n    const selectedIcon = icons.find(icon => icon.id === id);\r\n    if (selectedIcon) {\r\n        const svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n        if (svgElement) {\r\n            const base64Icon = svgToBase64(svgElement);\r\n            setIcon(base64Icon);\r\n            checklistCheckpointListProperties.icon=base64Icon\r\n        }\r\n    }\r\n};\r\n\r\n// Helper function to convert SVG to Base64\r\nconst svgToBase64 = (svgString: string): string => {\r\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n};\r\n\r\n\r\nconst handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\tconst file = event.target.files?.[0];\r\n\tif (!file) return;\r\n\r\n\tconst isIco = file.name.endsWith(\".ico\");\r\n\r\n\t// Validate the file type and size\r\n\tconst img = new Image();\r\n\timg.src = URL.createObjectURL(file);\r\n\timg.onload = () => {\r\n\t\tif (!isIco || img.width > 64 || img.height > 64) {\r\n\t\t\tsetError(translate(\"Please upload an .ico file less than 64x64px\"));\r\n\t\t} else {\r\n\t\t\tsetError(null);\r\n\t\t\tsetIcons(prevIcons => [\r\n\t\t\t\t...prevIcons,\r\n\t\t\t\t{ id: prevIcons.length + 1, component: <img src={img.src} alt=\"Custom Icon\" width={24} />, selected: false },\r\n\t\t\t]);\r\n\t\t}\r\n\t};\r\n\t};\r\n\t\r\n\r\n\t\r\n\tconst [icon, setIcon] = useState<any>();\r\n\r\nuseEffect(() => {\r\n  const initialSelectedIcon = icons.find(icon => icon.selected);\r\n  if (initialSelectedIcon && !checklistCheckpointListProperties.icon) {\r\n        const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n        if (svgElement) {\r\n            const base64Icon = svgToBase64(svgElement);\r\n            setIcon(base64Icon);\r\n\t\t\tchecklistCheckpointListProperties.icon=base64Icon\r\n\r\n    }\r\n  }\r\n}, []); \r\n\r\n\tconst [files, setFiles] = useState<File[]>([]);\r\n\tconst [gifFile, setGifFile] = useState<File | null>(null);\r\n\tconst [videoFile, setVideoFile] = useState<File | null>(null);\r\n\tconst [imageType, setImageType] = useState<string | null>(null); // Track locked image type\r\n\tconst [fileError, setFileError] = useState<string | null>(null);\r\n\r\n\tconst handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetFileError(null);\r\n\t\tif (!event.target.files) return;\r\n\t\r\n\t\tconst newFiles = Array.from(event.target.files);\r\n\t\r\n\t\tconst isAllGif = newFiles.every(file => file.type === \"image/gif\");\r\n\t\tconst isAllMp4 = newFiles.every(file => file.type === \"video/mp4\");\r\n\t\tconst isAllImages = newFiles.every(file =>\r\n\t\t\t[\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)\r\n\t\t);\r\n\t\tconst isMixedType = !(isAllGif || isAllMp4 || isAllImages);\r\n\t\r\n\t\tif (isMixedType) {\r\n\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\t\r\n\t\tif (gifFile) {\r\n\t\t\tif (isAllGif) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t} else {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (videoFile) {\r\n\t\t\tif (isAllMp4) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t} else {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (files.length > 0) {\r\n\t\t\tif (!isAllImages) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (imageType && !newFiles.every(file => file.type === imageType)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (isAllGif) {\r\n\t\t\tif (newFiles.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tsetGifFile(newFiles[0]);\r\n\t\t} else if (isAllMp4) {\r\n\t\t\tif (newFiles.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tsetVideoFile(newFiles[0]);\r\n\t\t} else if (isAllImages) {\r\n\t\t\tconst newImageType = newFiles[0].type;\r\n\t\t\tif (!imageType) {\r\n\t\t\t\tsetImageType(newImageType); // Lock the image type\r\n\t\t\t}\r\n\t\t\tsetFiles(prevFiles => {\r\n\t\t\t\tconst updatedFiles = [...prevFiles, ...newFiles];\r\n\t\t\t\tupdatedFiles.sort((a, b) => {\r\n\t\t\t\t\tconst nameA = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\tconst nameB = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\treturn nameA - nameB;\r\n\t\t\t\t});\r\n\t\t\t\treturn updatedFiles;\r\n\t\t\t});\r\n\t\t}\r\n\t\r\n\t\tconst base64Files = await Promise.all(\r\n\t\t\tnewFiles.map(async (file) => ({\r\n\t\t\t\tName: file.name,\r\n\t\t\t\tType: file.type,\r\n\t\t\t\tBase64: await fileToBase64(file),\r\n\t\t\t}))\r\n\t\t);\r\n\t\r\n\t\tsetChecklistCheckpointListProperties((prevState: any) => {\r\n\t\t\tconst updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\r\n\t\t\tupdatedMedia.sort((a, b) => {\r\n\t\t\t\tconst nameA = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\tconst nameB = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\treturn nameA - nameB;\r\n\t\t\t});\r\n\t\t\treturn {\r\n\t\t\t\t...prevState,\r\n\t\t\t\tsupportingMedia: updatedMedia,\r\n\t\t\t};\r\n\t\t});\r\n\t};\r\n\t\r\n\t\r\n\t  \r\n\tconst fileToBase64 = (file: File): Promise<string> => {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t\treader.onload = () => resolve(reader.result as string);\r\n\t\t\treader.onerror = error => reject(error);\r\n\t\t});\r\n\t};\r\n\t\r\n\t\r\n\t\r\n\tconst handleDeleteFile = (index: number) => {\r\n\t\tsetFileError(null);\r\n\t\tsetFiles((prevFiles) => {\r\n\t\t\tconst newFiles = prevFiles.filter((_, i) => i !== index);\r\n\t\t\tif (newFiles.length === 0) setImageType(null); // Reset image lock\r\n\t\r\n\t\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t\t...prevProperties,\r\n\t\t\t\tsupportingMedia: prevProperties.supportingMedia.filter((_: any, i: any) => i !== index),\r\n\t\t\t}));\r\n\t\r\n\t\t\treturn newFiles;\r\n\t\t});\r\n\t};\r\n\t\r\n\tconst handleDeleteGif = () => {\r\n\t\tsetGifFile(null);\r\n\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t...prevProperties,\r\n\t\t\tsupportingMedia: prevProperties.supportingMedia.filter(\r\n\t\t\t\t(file: any) => !file.Name?.toLowerCase().endsWith(\".gif\")\r\n\t\t\t),\r\n\t\t}));\r\n\t};\r\n\t\r\n\tconst handleDeleteVideo = () => {\r\n\t\tsetVideoFile(null);\r\n\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t...prevProperties,\r\n\t\t\tsupportingMedia: prevProperties.supportingMedia.filter(\r\n\t\t\t\t(file: any) => !file.Name?.toLowerCase().endsWith(\".mp4\")\r\n\t\t\t),\r\n\t\t}));\r\n\t};\r\n\t\r\n\t\r\n\t\t\t\t  \r\n\t  \r\n\t\r\n\treturn (\r\n\t\t\t\r\n\t\t<>\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Step\")} {checklistGuideMetaData[0].checkpoints.checkpointsList.length + 1}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box\r\n    id=\"qadpt-designpopup\"\r\n    className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n    // sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0px !important\" }}\r\n  >\r\n    <Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interaction\")}\r\n    </Typography>\r\n\r\n    <FormControl\r\n      variant=\"outlined\"\r\n      fullWidth\r\n      className=\"qadpt-control-input\"\r\n      sx={{\r\n        width: \"100% !important\",\r\n        borderRadius: \"12px\",\r\n        padding: \"0\",\r\n        margin: \"0 !important\",\r\n      }}\r\n    >\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\topen={isInteractionDropdownOpen}\r\n\t\t\t\t\t\t\t\t\t\tonOpen={handleInteractionDropdownOpen}\r\n\t\t\t\t\t\t\t\t\t\tonClose={handleInteractionDropdownClose}\r\n        value={selectedInteraction}\r\n        onChange={(e) => {\r\n          const newValue = e.target.value as string;\r\n          setSelectedInteraction(newValue);\r\n          onPropertyChange(\"interaction\", newValue);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n        name=\"ShowUpon\"\r\n        displayEmpty\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\", \r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder : \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t  \"&.MuiInputBase-root\":{height:\"35px !important\"}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n        MenuProps={{\r\n          PaperProps: {\r\n            sx: {\r\n              maxHeight: 430,\r\n\t\t\t\t  overflowY: \"auto\",\r\n\t\t\t\t  maxWidth: \"220px\",\r\n\t\t\t\t  \"& ul li\": {\r\n\t\t\t\t\twhiteSpace: \"normal\",\r\n\t\t\t\t\twordBreak: \"break-word\", \r\n\t\t\t\t\toverflowWrap: \"break-word\", \r\n\t\t\t\t\tmaxWidth: \"100%\",\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiFormControl-root .MuiInputBase-root\": {\r\n\t\t\t\t\t  borderRadius: \"12px\",\r\n\t\t\t\t\t  padding:\"1px !important\"\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\", \r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\", \r\n\t\t\t\t\t},\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\",\r\n\t\t\t\t\tborderWidth:\"1px !important\"  \r\n\t\t\t\t  },\r\n            },\r\n\t\t\t},\r\n\t\t\tonClose: handleInteractionDropdownClose,\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\trenderValue={(selected) => selected || translate(\"Select Interaction\")}\r\n      >\r\n\r\n        <Box\r\n          sx={{\r\n            position: \"sticky\",\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            zIndex: 1,\r\n            backgroundColor: \"white\",\r\n            padding: \"8px\",\r\n            borderBottom: \"1px solid #eee\",\r\n          }}\r\n         // onClick={(e) => e.stopPropagation()} // Prevent click from closing dropdown\r\n          onKeyDown={(e) => e.stopPropagation()} // Prevent dropdown from closing on typing\r\n        >\r\n          <TextField\r\n            fullWidth\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Search interactions...\")}\r\n            variant=\"outlined\"\r\n            size=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={searchTerm}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent TextField click from closing dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetIsInteractionDropdownOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t  onKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent keypress from closing dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (e.key === \"Escape\") {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  handleInteractionDropdownClose(); // Allow Escape to close dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\" ) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsearchTerm.trim()?fetchData(0, searchTerm): fetchData(0, \"\"); // 🔁 Same logic as icon button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t  onChange={(e) => setSearchTerm(e.target.value)}// Call search handler directly\r\n\t\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  <InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  aria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  onClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (searchTerm.trim()) {  // Only search if there's a non-empty term\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  fetchData(0, searchTerm);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\telse{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfetchData(0, \"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  <SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  </InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": { borderRadius: \"4px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiInputAdornment-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmargin:\"0 !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n          />\r\n        </Box>\r\n\r\n        {/* Scrollable Content */}\r\n        <Box\r\n          sx={{\r\n            maxHeight: \"352px\",\r\n            overflowY: \"auto\",\r\n          }}\r\n          onScroll={handleMenuScroll} // Attach the event directly here\r\n        >\r\n          {filteredInteractions && filteredInteractions.length === 0 && !loading && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem disabled>{translate(\"No interactions found\")}</MenuItem>\r\n          )}\r\n          {filteredInteractions && filteredInteractions.map((interaction) => (\r\n            <MenuItem\r\n              key={interaction.GuideId}\r\n              value={interaction.Name}\r\n              onClick={() => {\r\n\t\t\t\thandleInteractionChange(interaction.Name);\r\n\t\t\t\t // onPropertyChange(\"interaction\", interaction.Name);\r\n              }}\r\n            >\r\n              {interaction.Name}\r\n            </MenuItem>\r\n          ))}\r\n          {loading && (\r\n            <MenuItem disabled sx={{ display: \"flex\", justifyContent: \"center\" }}>\r\n              <CircularProgress size={20} />\r\n            </MenuItem>\r\n          )}\r\n        </Box>\r\n      </Select>\r\n    </FormControl>\r\n\t\t\t\t\t\t\t\t{duplicateError && (\r\n\t\t\t\t\t\t\t\t\t<div style={{ color: '#e53935', padding: 10, marginBottom: 8, fontSize: 13, textAlign: 'left' }}>\r\n\t\t\t\t\t\t\t\t\t\t{duplicateError}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n  </Box>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}>{translate(\"Title\")}</Typography>\r\n\t\t  \t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Title\")}\r\n                                            className=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.title}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"title\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }} >{translate(\"Description\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.description}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"description\", e.target.value)}\r\n\r\n                                            style={{width:\"100%\"}}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" style={{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",gap:\"5px\",padding:\"0\" ,marginBottom:\"10px\"}}>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Redirect URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: redirect }} style={{ display: \"flex\" }} /> </div>\r\n\t\t\t\t\t\t\t\t<Typography style={{ fontSize: \"11px\", color: \"#8d8d8d\", textAlign: \"left\", padding: \"0\", marginBottom: \"10px\" }}>\t\t\t\t\t\t\t\t\t{translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Redirection URL\")}\r\n                                            className=\"qadpt-control-input\"\r\n                                            style={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.redirectURL}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"redirectURL\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t// sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}\r\n\t\t\t\t\t\t\t\t>{translate(\"Icon\")}</Typography>\r\n            <Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\" ,width:\"-webkit-fill-available\",flexWrap:\"wrap\"}}>\r\n                {icons.map(icon => (\r\n                    <Tooltip key={icon.id} title=\"Select Icon\" arrow>\r\n                        <IconButton\r\n                            onClick={() => handleIconClick(icon.id)}\r\n                            sx={{\r\n                                border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n                                borderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n                            }}\r\n                        >\r\n                            {icon.component}\r\n                        </IconButton>\r\n                    </Tooltip>\r\n                ))}\r\n\r\n            \r\n            </Box>\r\n\r\n          \r\n        </Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t<Box\r\n      id=\"qadpt-designpopup\"\r\n      className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n    //   sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\"}}\r\n    >\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Supporting Media\")}</Typography>\r\n\r\n\t  <div\r\n  style={{\r\n    width: \"165px\",\r\n    height: \"auto\",\r\n    margin: \"0 8px 8px 8px\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\", \r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    border: \"1px dashed var(--primarycolor)\",\r\n    borderRadius: \"12px\",\r\n    padding: \"8px\",\r\n    background: \"#F1ECEC\",\r\n    textAlign: \"center\",\r\n  }}\r\n>\r\n  <Button\r\n    className=\"qadpt-upload-button\"\r\n    style={{\r\n\t\theight: \"auto\",\r\n\t\tpadding: \"0\",\r\n      width: \"100%\",\r\n      display: \"flex\",\r\n      flexDirection: \"row\", // Ensures icon & text are in one line\r\n      alignItems: \"center\",\r\n      justifyContent: \"center\",\r\n      gap: \"6px\",\r\n      color: \"#000\",\r\n      backgroundColor: \"#F1ECEC\",\r\n      textTransform: \"capitalize\",\r\n      boxShadow: \"none\",\r\n    }}\r\n    component=\"label\"\r\n  >\r\n    <CloudUploadOutlinedIcon sx={{zoom:\"1.6\"}} />\r\n    Upload file\r\n    <input\r\n      id=\"file-input\"\r\n      type=\"file\"\r\n      multiple\r\n      accept=\".jpeg, .jpg, .png, .gif, .mp4\"\r\n      onChange={handleFileChange}\r\n      style={{ display: \"none\" }}\r\n    />\r\n  </Button>\r\n  \r\n  {/* File format text on the second line */}\r\n  <Typography style={{ fontSize: \"12px\", color: \"#A3A3A3\" }}>\r\n    .png, .jpg, .gif, .mp4\r\n  </Typography>\r\n</div>\r\n{fileError && (\r\n \t<div style={{ display: \"flex\", alignItems: \"center\" , color: \"#e6a957\",padding:\"0 8px\",textAlign:\"left\", width: \"-webkit-fill-available\"}}>\r\n\r\n    <span\r\n      style={{ marginRight: \"4px\",display:\"flex\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    <div style={{fontSize: \"12px\"}}>{fileError}</div>\r\n  </div>\r\n)}\r\n\r\n\r\n\r\n      {/* Display uploaded images */}\r\n      <Box sx={{width:\"-webkit-fill-available\"}}>\r\n        {files.map((file, index) => (\r\n          <Box\r\n            key={index}\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            justifyContent=\"space-between\"\r\n\t\t\tsx={{\r\n\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\tpadding: \"8px\",\r\n\t\t\t\tmargin: \"8px\",\r\n\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t  }}\r\n\t\t\t  \r\n          >\r\n            <img\r\n              src={URL.createObjectURL(file)}\r\n              alt={`uploaded-${index}`}\r\n              style={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n            />\r\n\t\t\t\t{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}\r\n            <Typography\r\n              sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}\r\n            >\r\n              {file.name}\r\n            </Typography>\r\n            <IconButton onClick={() => handleDeleteFile(index)} size=\"small\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: \"1\" ,display:\"flex\" }} />\r\n            </IconButton>\r\n          </Box>\r\n        ))}\r\n\r\n        {/* Display uploaded GIF separately */}\r\n        {gifFile && (\r\n          <Box\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            justifyContent=\"space-between\"\r\n            sx={{\r\n\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\tpadding: \"8px\",\r\n\t\t\t\tmargin: \"5px\",\r\n\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t  }}\r\n          >\r\n            <img\r\n              src={URL.createObjectURL(gifFile)}\r\n              alt=\"uploaded-gif\"\r\n              style={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n            />\r\n            <Typography\r\n              sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}\r\n\t\t\t  >\r\n              {gifFile.name}\r\n            </Typography>\r\n            <IconButton onClick={handleDeleteGif} size=\"small\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: \"1\" ,display:\"flex\" }} />\r\n            </IconButton>\r\n        </Box>\r\n    )}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t{videoFile && (\r\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\"\r\n            sx={{\r\n                border: \"1px solid #0a6\",\r\n                borderRadius: \"5px\",\r\n                padding: \"8px\",\r\n                marginBottom: \"5px\",\r\n                width: \"196px\",\r\n                backgroundColor: \"#e6ffe6\",\r\n            }}>\r\n            <video width=\"40\" height=\"40\" controls>\r\n                <source src={URL.createObjectURL(videoFile)} type=\"video/mp4\" />\r\n                Your browser does not support the video tag.\r\n            </video>\r\n            <Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n                {videoFile.name}\r\n            </Typography>\r\n            <IconButton onClick={handleDeleteVideo} size=\"small\">\r\n                <span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: 0.7 }} />\r\n            </IconButton>\r\n      </Box>\r\n    )}\r\n    </Box>\r\n    </Box>\r\n\r\n\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ paddingBottom: \"8 !important\" }}>{translate(\"Media Title\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Title\")}\r\n                                            className=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"mediaTitle\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ paddingBottom: \"8px !important\" }}>{translate(\"Media Description\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.mediaDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\tif (value.length > 200) {\r\n\t\t\t\t\t\t\t\t\t\t\tvalue = value.slice(0, 200);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"mediaDescription\", value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\thelperText={`${checklistCheckpointListProperties.mediaDescription?.length || 0}/200`}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tApply\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n                </div>\r\n                \r\n                \r\n\t\t\t</div>\r\n\t\t\t</>\r\n\t\t);\r\n};\r\n\r\nexport default CheckPointAddPopup;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,CAACC,SAAS,CAAUC,UAAU,KAAqB,OAAO,CAC9F,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,CAAEC,cAAc,CAAEC,WAAW,CAAcC,MAAM,CAAEC,QAAQ,CAA+CC,OAAO,CAAEC,gBAAgB,KAA2B,eAAe,CAC1O,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CAOnD,OAMIC,UAAU,CACbC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,QAAQ,CACRC,OAAO,KACD,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,uBAAuB,KAAM,yCAAyC,CAC7E,OAASC,YAAY,KAAQ,kCAAkC,CAC/D,OAASC,cAAc,KAAQ,yBAAyB,CAExD,MAAO,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGtC,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAAsJ,KAAAC,qBAAA,IAArJ,CAAEC,6BAA6B,CAAEC,gCAA8G,CAAC,CAAAH,IAAA,CAC3K,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAG5B,cAAc,CAAC,CAAC,CACzC,KAAM,CAAC6B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7C,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CACL8C,sBAAsB,CAChBC,uBAAuB,CACvBC,oBAAoB,CACpBC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,UAAU,CACVC,aAAa,CACbC,gBAAgB,CAChBC,mBAAmB,CACnBC,oBAAoB,CACpBC,uBAAuB,CACvBC,0BAA0B,CAC1BC,6BAA6B,CAC7BC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC7BC,uBAAuB,CACpBC,iBAAiB,CACjBC,oBAAoB,CAC1BC,sBAAsB,CACtBC,0BAA0B,CAC1BC,6BAA6B,CAC7BC,mBAAmB,CACnBC,gBAIE,CAAC,CAAGtD,cAAc,CAAEuD,KAAU,EAAKA,KAAK,CAAC,CAG5C,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzE,QAAQ,CAAS,EAAE,CAAC,CAC1E,KAAM,CAAC0E,UAAU,CAAEC,aAAa,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC4E,iCAAiC,CAAEC,oCAAoC,CAAC,CAAG7E,QAAQ,CAAM,CAC/F8E,EAAE,CAAC,EAAE,CACLC,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,EAAE,CACTC,WAAW,CAAE,EAAE,CACfC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,EAAE,CACRC,eAAe,CAAE,EAAE,CACnBC,UAAU,CAAE,EAAE,CACdC,gBAAgB,CAAE,EACnB,CAAC,CAAC,CAGC,KAAM,CAAAC,+BAA+B,CAAIC,CAAM,EAAK3B,sBAAsB,CAAC2B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC1F,KAAM,CAAAC,gCAAgC,CAAIH,CAAM,EAAK/B,uBAAuB,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC/F,KAAM,CAAAE,sCAAsC,CAAIJ,CAAM,EAAK/B,uBAAuB,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAClG,KAAM,CAACG,YAAY,CAAEC,eAAe,CAAC,CAAG9F,QAAQ,CAAQ,EAAE,CAAC,CAE3DC,SAAS,CAAC,IAAM,CACf,GAAIuE,mBAAmB,CAAE,CACxB,KAAM,CAAAuB,uBAAuB,CAAGC,oBAAoB,CAACC,IAAI,CAAClB,WAAW,EAAIA,WAAW,CAACmB,IAAI,GAAK1B,mBAAmB,CAAC,CAElH,GAAIuB,uBAAuB,CAAE,CAC5BlB,oCAAoC,CAAC,CACpCE,WAAW,CAAEP,mBAAmB,CAChCQ,KAAK,CAAEe,uBAAuB,CAACG,IAAI,EAAI,EAAE,CACzCjB,WAAW,CAAEc,uBAAuB,CAACI,WAAW,EAAI,EAAE,CACtDjB,WAAW,CAAEa,uBAAuB,CAACK,SAAS,EAAI,EAAE,CACpDjB,IAAI,CAAEY,uBAAuB,CAACM,SAAS,CACvCjB,eAAe,CAAE,EAAE,CACnBC,UAAU,CAAEU,uBAAuB,CAACG,IAAI,EAAI,EAAE,CAC9CZ,gBAAgB,CAAES,uBAAuB,CAACd,WAAW,EAAI,EAAE,CAC3DH,EAAE,CAACiB,uBAAuB,CAACO,OAAO,EAAE,EACrC,CAAC,CAAC,CACH,CACD,CACD,CAAC,CAAE,CAAC9B,mBAAmB,CAAEqB,YAAY,CAAEnB,UAAU,CAAC,CAAC,CAElD,KAAM,CAAA6B,WAAW,CAAGA,CAAA,GAAM,CACzBrC,sBAAsB,CAAC,KAAK,CAAC,CAC9B,CAAC,CACD,KAAM,CAAAsC,iBAAiB,CAAGA,CAAA,GAAM,CAC/BrD,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CACD,KAAM,CAAAsD,gBAAgB,CAAIf,KAAa,EAAK,CAC3C,KAAM,CAAAgB,QAAQ,CAAG,EAAE,CAAG,CAAChB,KAAK,CAAG,CAAC,EAAI,CAAC,CACrCiB,gBAAgB,CAAC,MAAM,CAAED,QAAQ,CAAC,CACnC,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAEhC,CAAC,CAED,KAAM,CAAAD,gBAAgB,CAAGA,CAACE,GAAQ,CAAEnB,KAAU,GAAK,CAClDb,oCAAoC,CAAEiC,SAAc,GAAM,CACzD,GAAGA,SAAS,CACZ,CAACD,GAAG,EAAGnB,KACR,CAAC,CAAC,CAAC,CACL,CAAC,CACD,KAAM,CAACqB,YAAY,CAAEC,eAAe,CAAC,CAAGhH,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAAiH,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAChCC,YAAY,CAAC,IAAI,CAAC,CAClBtE,iBAAiB,CAAC,IAAI,CAAC,CACtB+B,iCAAiC,CAACO,IAAI,CAAGA,IAAI,CAE9C;AACA,GAAI,CAACP,iCAAiC,CAACG,WAAW,EAAIH,iCAAiC,CAACG,WAAW,CAACqC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAClHvE,iBAAiB,CAACF,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAC5D,OACD,CAEA;AACA,KAAM,CAAA0E,WAAW,EAAAH,qBAAA,CAAG1E,6BAA6B,CAAC8E,eAAe,UAAAJ,qBAAA,iBAA7CA,qBAAA,CAA+CK,IAAI,CACrEC,EAAO,EAAKA,EAAE,CAACzC,WAAW,GAAKH,iCAAiC,CAACG,WACnE,CAAC,CACD,GAAIsC,WAAW,CAAE,CAChBxE,iBAAiB,CAACF,SAAS,CAAC,0BAA0B,CAAC,CAAC,CACxD,OACD,CAEC,KAAM,CAAA8E,iBAAiB,CAAG,CACzB,GAAG7C,iCACJ,CAAC,CACDR,6BAA6B,CAACqD,iBAAiB,CAAC,CAChDT,eAAe,CAAC,IAAI,CAAC,CACrB3C,mBAAmB,CAAC,IAAI,CAAC,CAEzB5B,gCAAgC,CAAEiF,IAAS,GAAM,CAChD,GAAGA,IAAI,CACPJ,eAAe,CAAE,CAChB,GAAGI,IAAI,CAACJ,eAAe,CAAE;AACzBG,iBAAyB;AAAA,CAE3B,CAAC,CAAC,CAAC,CAEHlB,WAAW,CAAC,CAAC,CACd,CAAC,CAGF,KAAM,CAAAoB,eAAe,CAAGA,CAAA,GAAM,CACvB5E,uBAAuB,CAAC,IAAI,CAAC,CACjC,CAAC,CACJ,KAAM,CAAC6E,IAAI,CAAEC,OAAO,CAAC,CAAG7H,QAAQ,CAAC,CAAC,CAAC,CACpC,KAAM,CAAC8H,OAAO,CAAEC,UAAU,CAAC,CAAG/H,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgI,OAAO,CAAEC,UAAU,CAAC,CAAGjI,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgG,oBAAoB,CAAEkC,uBAAuB,CAAC,CAAGlI,QAAQ,CAAQ,EAAE,CAAC,CAC1E,KAAM,CAAEmI,SAAU,CAAC,CAAGjI,UAAU,CAAC4B,cAAc,CAAC,CAChD,KAAM,CAACsG,WAAW,CAAEC,cAAc,CAAC,CAAGrI,QAAQ,CAAC,KAAK,CAAC,CACtD,KAAM,CAAAsI,GAAG,CAAG,EAAE,CAEdrI,SAAS,CAAC,IAAM,CACdsI,SAAS,CAAC,CAAC,CAAC,CAAG;AACjB,CAAC,CAAE,EAAE,CAAC,CAIN,KAAM,CAAAA,SAAS,CAAG,cAAAA,CAAOC,OAAe,CAA6B,IAA5B,CAAAC,aAAoB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAC,EAAE,CAC/D,GAAID,aAAa,EAAE,EAAE,EAAIX,OAAO,CAAE,OAAS;AAC3C,GAAGW,aAAa,EAAI,EAAE,EAAI/D,UAAU,EAAI,EAAE,CAAE+D,aAAa,CAAG/D,UAAU,CACtEqD,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAAAc,OAAO,CAAG,CACd,CACDC,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAEd,SAAS,CAChBe,aAAa,CAAE,KACd,CAAC,CACD,CACDJ,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,WAAW,CACtBC,KAAK,CAAE,WAAW,CAClBC,aAAa,CAAE,KACd,CAAC,CACF,CACD,GAAGT,aAAa,EAAI,EAAE,CAAC,CACtBI,OAAO,CAACM,IAAI,CAAC,CACZL,SAAS,CAAC,MAAM,CAChBC,WAAW,CAAC,QAAQ,CACpBC,SAAS,CAAC,UAAU,CACpBC,KAAK,CAACR,aAAa,CACnBS,aAAa,CAAC,KACf,CAAC,CAAC,CACH,CAEA,GAAI,CACF,KAAM,CAAAE,IAAI,CAAG,KAAM,CAAAvH,YAAY,CAAC2G,OAAO,CAAEF,GAAG,CAAEO,OAAO,CAAE,EAAE,CAAC,CAC3D,KAAM,CAAAQ,eAAe,CAAGD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEE,OAAO,CACpC,GAAGb,aAAa,EAAE,EAAE,CAAE,CACtB,GAAID,OAAO,GAAK,CAAC,CAAE,CACpB1C,eAAe,CAACuD,eAAe,CAAC,CAChCnB,uBAAuB,CAACmB,eAAe,CAAC,CACvC,CAAC,IAAM,CACRvD,eAAe,CAAE4B,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAE,GAAG2B,eAAe,CAAC,CAAC,CACxDnB,uBAAuB,CAAER,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAE,GAAG2B,eAAe,CAAC,CAAC,CAC/D,CACF,CAAC,IACI,CACC,GAAGb,OAAO,GAAK,CAAC,CAAEN,uBAAuB,CAACmB,eAAe,CAAC,CAAC,IACzD,CAAAnB,uBAAuB,CAAER,IAAI,EAAK,CAAC,GAAGA,IAAI,CAAE,GAAG2B,eAAe,CAAC,CAAC,CACxE,CACExB,OAAO,CAACW,OAAO,CAAGF,GAAG,CAAC,CACtBL,UAAU,CAACoB,eAAe,CAACV,MAAM,CAAG,CAAC,CAAC,CACxC,CAAE,MAAOY,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACRxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACC,CAAC,CAEH,KAAM,CAAA0B,gBAAgB,CAAIC,KAAoC,EAAK,CAClE,KAAM,CAAAjE,MAAM,CAAGiE,KAAK,CAACC,aAAa,CAClC,KAAM,CAAEC,SAAS,CAAEC,YAAY,CAAEC,YAAa,CAAC,CAAGrE,MAAM,CAExD,GAAIoE,YAAY,CAAGD,SAAS,CAAGE,YAAY,CAAG,EAAE,EAAI,CAAChC,OAAO,EAAIE,OAAO,CAAG,CACvEO,SAAS,CAACX,IAAI,CAAC,CAAE;AACpB,CACC,CAAC,CAED,KAAM,CAACmC,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGhK,QAAQ,CAAC,KAAK,CAAC,CACjF,KAAM,CAAAiK,uBAAuB,CAAIC,QAAgB,EAAK,CACrDzF,sBAAsB,CAACyF,QAAQ,CAAC,CAChCvD,gBAAgB,CAAC,aAAa,CAAEuD,QAAQ,CAAC,CACzCF,4BAA4B,CAAC,KAAK,CAAC,CACnCrF,aAAa,CAAC,EAAE,CAAC,CAClB9B,iBAAiB,CAAC,IAAI,CAAC,CAAE;AACzB,CAAC,CAED,KAAM,CAAAsH,6BAA6B,CAAGA,CAAA,GAAM,CAC3CH,4BAA4B,CAAC,IAAI,CAAC,CAClC,GAAItF,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAE,CACxBmB,SAAS,CAAC,CAAC,CAAE7D,UAAU,CAAC,CAAE;AACzB,CAAC,IAAM,CACR6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AACjB,CACD,CAAC,CAED,KAAM,CAAA6B,8BAA8B,CAAGA,CAAA,GAAM,CAC5CJ,4BAA4B,CAAC,KAAK,CAAC,CACnCrF,aAAa,CAAC,EAAE,CAAC,CAClB,CAAC,CACH,KAAM,CAAA0F,YAAY,CAAKX,KAA0C,EAAK,CACrE,KAAM,CAAAY,IAAI,CAAGZ,KAAK,CAACjE,MAAM,CAACC,KAAK,CAC/Bf,aAAa,CAAC2F,IAAI,CAAC,CAEnB,GAAI,CAACA,IAAI,CAAClD,IAAI,CAAC,CAAC,CAAE,CAChB;AACAc,uBAAuB,CAACrC,YAAY,CAAC,CACrCwC,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,IAAM,CACP;AACA;AACA;AACA;AACAA,cAAc,CAAC,IAAI,CAAC,CACpBH,uBAAuB,CAAC,EAAE,CAAC,CAC1BK,SAAS,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAClB,CACC,CAAC,CAGD,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGxK,QAAQ,CAAQ,CAC3C,CAAE8E,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAEzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAExJ,OAAQ,CAAE,CAACyJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAC,MAAM,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAC/H,CAAEjG,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAGzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEvJ,OAAQ,CAAE,CAACwJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAC,MAAM,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjI,CAAEjG,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAGzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEtJ,OAAQ,CAAE,CAACuJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAACC,OAAO,CAAC,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAC/H,CAAEjG,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAGzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAErJ,OAAQ,CAAE,CAACsJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAACC,OAAO,CAAC,MAAO,CAAE,CAAE,CAAC,CAAGC,QAAQ,CAAE,KAAM,CAAC,CAClI,CAAEjG,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAGzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEpJ,OAAQ,CAAE,CAACqJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAC,MAAM,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjI,CAAEjG,EAAE,CAAE,CAAC,CAAE2F,SAAS,cAAGzI,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEnJ,OAAQ,CAAE,CAACoJ,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAC,MAAM,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAClI,CAAC,CACH,KAAM,CAACxB,KAAK,CAAEyB,QAAQ,CAAC,CAAGhL,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAiL,eAAe,CAAG,KAAO,CAAAnG,EAAU,EAAK,CAC1C0F,QAAQ,CAACU,SAAS,EACdA,SAAS,CAACC,GAAG,CAAChG,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP4F,QAAQ,CAAE5F,IAAI,CAACL,EAAE,GAAKA,EAC1B,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAsG,YAAY,CAAGb,KAAK,CAACtE,IAAI,CAACd,IAAI,EAAIA,IAAI,CAACL,EAAE,GAAKA,EAAE,CAAC,CACvD,GAAIsG,YAAY,CAAE,KAAAC,qBAAA,CACd,KAAM,CAAAC,UAAU,EAAAD,qBAAA,CAAGD,YAAY,CAACX,SAAS,CAACc,KAAK,CAACb,uBAAuB,UAAAW,qBAAA,iBAApDA,qBAAA,CAAsDV,MAAM,CAC/E,GAAIW,UAAU,CAAE,CACZ,KAAM,CAAAE,UAAU,CAAGC,WAAW,CAACH,UAAU,CAAC,CAC1CI,OAAO,CAACF,UAAU,CAAC,CACnB5G,iCAAiC,CAACO,IAAI,CAACqG,UAAU,CACrD,CACJ,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAIE,SAAiB,EAAa,CAC/C,MAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE,CACzD,CAAC,CAGD,KAAM,CAAAE,gBAAgB,CAAInC,KAA0C,EAAK,KAAAoC,mBAAA,CACxE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGpC,KAAK,CAACjE,MAAM,CAACuG,KAAK,UAAAF,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAI,CAACC,IAAI,CAAE,OAEX,KAAM,CAAAE,KAAK,CAAGF,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAExC;AACA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACvBD,GAAG,CAACE,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC,CACnCK,GAAG,CAACK,MAAM,CAAG,IAAM,CAClB,GAAI,CAACR,KAAK,EAAIG,GAAG,CAACM,KAAK,CAAG,EAAE,EAAIN,GAAG,CAACO,MAAM,CAAG,EAAE,CAAE,CAChD3B,QAAQ,CAACrI,SAAS,CAAC,8CAA8C,CAAC,CAAC,CACpE,CAAC,IAAM,CACNqI,QAAQ,CAAC,IAAI,CAAC,CACdR,QAAQ,CAACU,SAAS,EAAI,CACrB,GAAGA,SAAS,CACZ,CAAEpG,EAAE,CAAEoG,SAAS,CAACvC,MAAM,CAAG,CAAC,CAAE8B,SAAS,cAAEzI,IAAA,QAAKsK,GAAG,CAAEF,GAAG,CAACE,GAAI,CAACM,GAAG,CAAC,aAAa,CAACF,KAAK,CAAE,EAAG,CAAE,CAAC,CAAE3B,QAAQ,CAAE,KAAM,CAAC,CAC5G,CAAC,CACH,CACD,CAAC,CACD,CAAC,CAID,KAAM,CAAC5F,IAAI,CAAEuG,OAAO,CAAC,CAAG1L,QAAQ,CAAM,CAAC,CAExCC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4M,mBAAmB,CAAGtC,KAAK,CAACtE,IAAI,CAACd,IAAI,EAAIA,IAAI,CAAC4F,QAAQ,CAAC,CAC7D,GAAI8B,mBAAmB,EAAI,CAACjI,iCAAiC,CAACO,IAAI,CAAE,KAAA2H,qBAAA,CAC9D,KAAM,CAAAxB,UAAU,EAAAwB,qBAAA,CAAGD,mBAAmB,CAACpC,SAAS,CAACc,KAAK,CAACb,uBAAuB,UAAAoC,qBAAA,iBAA3DA,qBAAA,CAA6DnC,MAAM,CACtF,GAAIW,UAAU,CAAE,CACZ,KAAM,CAAAE,UAAU,CAAGC,WAAW,CAACH,UAAU,CAAC,CAC1CI,OAAO,CAACF,UAAU,CAAC,CAC5B5G,iCAAiC,CAACO,IAAI,CAACqG,UAAU,CAEhD,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEL,KAAM,CAACQ,KAAK,CAAEe,QAAQ,CAAC,CAAG/M,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACgN,OAAO,CAAEC,UAAU,CAAC,CAAGjN,QAAQ,CAAc,IAAI,CAAC,CACzD,KAAM,CAACkN,SAAS,CAAEC,YAAY,CAAC,CAAGnN,QAAQ,CAAc,IAAI,CAAC,CAC7D,KAAM,CAACoN,SAAS,CAAEC,YAAY,CAAC,CAAGrN,QAAQ,CAAgB,IAAI,CAAC,CAAE;AACjE,KAAM,CAACsN,SAAS,CAAEnG,YAAY,CAAC,CAAGnH,QAAQ,CAAgB,IAAI,CAAC,CAE/D,KAAM,CAAAuN,gBAAgB,CAAG,KAAO,CAAA7D,KAA0C,EAAK,CAC9EvC,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CAACuC,KAAK,CAACjE,MAAM,CAACuG,KAAK,CAAE,OAEzB,KAAM,CAAAwB,QAAQ,CAAGC,KAAK,CAACC,IAAI,CAAChE,KAAK,CAACjE,MAAM,CAACuG,KAAK,CAAC,CAE/C,KAAM,CAAA2B,QAAQ,CAAGH,QAAQ,CAACI,KAAK,CAAC7B,IAAI,EAAIA,IAAI,CAAC8B,IAAI,GAAK,WAAW,CAAC,CAClE,KAAM,CAAAC,QAAQ,CAAGN,QAAQ,CAACI,KAAK,CAAC7B,IAAI,EAAIA,IAAI,CAAC8B,IAAI,GAAK,WAAW,CAAC,CAClE,KAAM,CAAAE,WAAW,CAAGP,QAAQ,CAACI,KAAK,CAAC7B,IAAI,EACtC,CAAC,YAAY,CAAE,WAAW,CAAE,WAAW,CAAC,CAACiC,QAAQ,CAACjC,IAAI,CAAC8B,IAAI,CAC5D,CAAC,CACD,KAAM,CAAAI,WAAW,CAAG,EAAEN,QAAQ,EAAIG,QAAQ,EAAIC,WAAW,CAAC,CAE1D,GAAIE,WAAW,CAAE,CAChB9G,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CAEA,GAAIqK,OAAO,CAAE,CACZ,GAAIW,QAAQ,CAAE,CACbxG,YAAY,CAACxE,SAAS,CAAC,0BAA0B,CAAC,CAAC,CACnD,OACD,CAAC,IAAM,CACNwE,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CACD,CAEA,GAAIuK,SAAS,CAAE,CACd,GAAIY,QAAQ,CAAE,CACb3G,YAAY,CAACxE,SAAS,CAAC,4BAA4B,CAAC,CAAC,CACrD,OACD,CAAC,IAAM,CACNwE,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CACD,CAEA,GAAIqJ,KAAK,CAACrD,MAAM,CAAG,CAAC,CAAE,CACrB,GAAI,CAACoF,WAAW,CAAE,CACjB5G,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CACA,GAAIyK,SAAS,EAAI,CAACI,QAAQ,CAACI,KAAK,CAAC7B,IAAI,EAAIA,IAAI,CAAC8B,IAAI,GAAKT,SAAS,CAAC,CAAE,CAClEjG,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAC9D,OACD,CACD,CAEA,GAAIgL,QAAQ,CAAE,CACb,GAAIH,QAAQ,CAAC7E,MAAM,CAAG,CAAC,CAAE,CACxBxB,YAAY,CAACxE,SAAS,CAAC,0BAA0B,CAAC,CAAC,CACnD,OACD,CACAsK,UAAU,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxB,CAAC,IAAM,IAAIM,QAAQ,CAAE,CACpB,GAAIN,QAAQ,CAAC7E,MAAM,CAAG,CAAC,CAAE,CACxBxB,YAAY,CAACxE,SAAS,CAAC,4BAA4B,CAAC,CAAC,CACrD,OACD,CACAwK,YAAY,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1B,CAAC,IAAM,IAAIO,WAAW,CAAE,CACvB,KAAM,CAAAG,YAAY,CAAGV,QAAQ,CAAC,CAAC,CAAC,CAACK,IAAI,CACrC,GAAI,CAACT,SAAS,CAAE,CACfC,YAAY,CAACa,YAAY,CAAC,CAAE;AAC7B,CACAnB,QAAQ,CAACoB,SAAS,EAAI,CACrB,KAAM,CAAAC,YAAY,CAAG,CAAC,GAAGD,SAAS,CAAE,GAAGX,QAAQ,CAAC,CAChDY,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC3B,KAAM,CAAAC,KAAK,CAAGF,CAAC,CAACpC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACJ,CAAC,CAACpC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC7E,KAAM,CAAAE,KAAK,CAAGJ,CAAC,CAACrC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACH,CAAC,CAACrC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC7E,MAAO,CAAAD,KAAK,CAAGG,KAAK,CACrB,CAAC,CAAC,CACF,MAAO,CAAAP,YAAY,CACpB,CAAC,CAAC,CACH,CAEA,KAAM,CAAAQ,WAAW,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CACpCtB,QAAQ,CAACrC,GAAG,CAAC,KAAO,CAAAY,IAAI,GAAM,CAC7B7F,IAAI,CAAE6F,IAAI,CAACG,IAAI,CACf6C,IAAI,CAAEhD,IAAI,CAAC8B,IAAI,CACfmB,MAAM,CAAE,KAAM,CAAAC,YAAY,CAAClD,IAAI,CAChC,CAAC,CAAC,CACH,CAAC,CAEDlH,oCAAoC,CAAEiC,SAAc,EAAK,CACxD,KAAM,CAAAoI,YAAY,CAAG,CAAC,IAAIpI,SAAS,CAAC1B,eAAe,EAAI,EAAE,CAAC,CAAE,GAAGwJ,WAAW,CAAC,CAC3EM,YAAY,CAACb,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC3B,KAAM,CAAAC,KAAK,CAAGF,CAAC,CAACpI,IAAI,CAACuI,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACJ,CAAC,CAACpI,IAAI,CAACuI,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC7E,KAAM,CAAAE,KAAK,CAAGJ,CAAC,CAACrI,IAAI,CAACuI,KAAK,CAAC,KAAK,CAAC,CAAGC,QAAQ,CAACH,CAAC,CAACrI,IAAI,CAACuI,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,CAAC,CAC7E,MAAO,CAAAD,KAAK,CAAGG,KAAK,CACrB,CAAC,CAAC,CACF,MAAO,CACN,GAAG7H,SAAS,CACZ1B,eAAe,CAAE8J,YAClB,CAAC,CACF,CAAC,CAAC,CACH,CAAC,CAID,KAAM,CAAAD,YAAY,CAAIlD,IAAU,EAAsB,CACrD,MAAO,IAAI,CAAA8C,OAAO,CAAC,CAACM,OAAO,CAAEC,MAAM,GAAK,CACvC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,aAAa,CAACxD,IAAI,CAAC,CAC1BsD,MAAM,CAAC5C,MAAM,CAAG,IAAM0C,OAAO,CAACE,MAAM,CAACG,MAAgB,CAAC,CACtDH,MAAM,CAACI,OAAO,CAAGlG,KAAK,EAAI6F,MAAM,CAAC7F,KAAK,CAAC,CACxC,CAAC,CAAC,CACH,CAAC,CAID,KAAM,CAAAmG,gBAAgB,CAAIC,KAAa,EAAK,CAC3CxI,YAAY,CAAC,IAAI,CAAC,CAClB4F,QAAQ,CAAEoB,SAAS,EAAK,CACvB,KAAM,CAAAX,QAAQ,CAAGW,SAAS,CAACyB,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKH,KAAK,CAAC,CACxD,GAAInC,QAAQ,CAAC7E,MAAM,GAAK,CAAC,CAAE0E,YAAY,CAAC,IAAI,CAAC,CAAE;AAE/CxI,oCAAoC,CAAEkL,cAAmB,GAAM,CAC9D,GAAGA,cAAc,CACjB3K,eAAe,CAAE2K,cAAc,CAAC3K,eAAe,CAACwK,MAAM,CAAC,CAACC,CAAM,CAAEC,CAAM,GAAKA,CAAC,GAAKH,KAAK,CACvF,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAnC,QAAQ,CAChB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAwC,eAAe,CAAGA,CAAA,GAAM,CAC7B/C,UAAU,CAAC,IAAI,CAAC,CAChBpI,oCAAoC,CAAEkL,cAAmB,GAAM,CAC9D,GAAGA,cAAc,CACjB3K,eAAe,CAAE2K,cAAc,CAAC3K,eAAe,CAACwK,MAAM,CACpD7D,IAAS,OAAAkE,UAAA,OAAK,GAAAA,UAAA,CAAClE,IAAI,CAAC7F,IAAI,UAAA+J,UAAA,WAATA,UAAA,CAAWC,WAAW,CAAC,CAAC,CAAC/D,QAAQ,CAAC,MAAM,CAAC,GAC1D,CACD,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAgE,iBAAiB,CAAGA,CAAA,GAAM,CAC/BhD,YAAY,CAAC,IAAI,CAAC,CAClBtI,oCAAoC,CAAEkL,cAAmB,GAAM,CAC9D,GAAGA,cAAc,CACjB3K,eAAe,CAAE2K,cAAc,CAAC3K,eAAe,CAACwK,MAAM,CACpD7D,IAAS,OAAAqE,WAAA,OAAK,GAAAA,WAAA,CAACrE,IAAI,CAAC7F,IAAI,UAAAkK,WAAA,WAATA,WAAA,CAAWF,WAAW,CAAC,CAAC,CAAC/D,QAAQ,CAAC,MAAM,CAAC,GAC1D,CACD,CAAC,CAAC,CAAC,CACJ,CAAC,CAMD,mBAECnK,IAAA,CAAAI,SAAA,EAAAiO,QAAA,cACCrO,IAAA,QACC8C,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAE7BnO,KAAA,QAAKoO,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC7BnO,KAAA,QAAKoO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eACnCrO,IAAA,CAAC1B,UAAU,EACV,aAAW,MAAM,CACjBiQ,OAAO,CAAEhK,WAAY,CAAA8J,QAAA,cAErBrO,IAAA,CAACL,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbO,KAAA,QAAKoO,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAE1N,SAAS,CAAC,MAAM,CAAC,CAAC,GAAC,CAACG,sBAAsB,CAAC,CAAC,CAAC,CAAC0N,WAAW,CAAClJ,eAAe,CAACqB,MAAM,CAAG,CAAC,EAAM,CAAC,cACzH3G,IAAA,CAAC1B,UAAU,EACVmQ,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBF,OAAO,CAAEhK,WAAY,CAAA8J,QAAA,cAErBrO,IAAA,CAAClB,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAINkB,IAAA,QAAKsO,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC9BnO,KAAA,QAAKoO,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC/BnO,KAAA,CAAC/B,GAAG,EACN2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCACV;AAAA,CAAAD,QAAA,eAEArO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAN,QAAA,CAC9E1N,SAAS,CAAC,aAAa,CAAC,CAClB,CAAC,cAEbX,IAAA,CAACvB,WAAW,EACVmQ,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTP,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CACFhE,KAAK,CAAE,iBAAiB,CACxBoE,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,GAAG,CACZI,MAAM,CAAE,cACV,CAAE,CAAAV,QAAA,cAECnO,KAAA,CAACxB,MAAM,EACNsQ,IAAI,CAAEjH,yBAA0B,CAChCkH,MAAM,CAAE9G,6BAA8B,CACtC+G,OAAO,CAAE9G,8BAA+B,CAC1C1E,KAAK,CAAElB,mBAAoB,CAC3B2M,QAAQ,CAAG3L,CAAC,EAAK,CACf,KAAM,CAAA0E,QAAQ,CAAG1E,CAAC,CAACC,MAAM,CAACC,KAAe,CACzCjB,sBAAsB,CAACyF,QAAQ,CAAC,CAChCvD,gBAAgB,CAAC,aAAa,CAAEuD,QAAQ,CAAC,CACzC,CAAE,CAEJgC,IAAI,CAAC,UAAU,CACfkF,YAAY,MACVV,EAAE,CAAE,CACHhE,KAAK,CAAE,iBAAiB,CACxB2E,SAAS,CAAE,MAAM,CACjB,0BAA0B,CAAE,CAC3B,SAAS,CAAE,CACVC,WAAW,CAAE,iBACd,CAAC,CACD,eAAe,CAAE,CAChBA,WAAW,CAAE,iBACd,CACD,CAAC,CACD,oCAAoC,CAAE,CACrCC,MAAM,CAAG,iBACV,CAAC,CACC,qBAAqB,CAAC,CAAC5E,MAAM,CAAC,iBAAiB,CAClD,CAAE,CAEJ6E,SAAS,CAAE,CACTC,UAAU,CAAE,CACVf,EAAE,CAAE,CACFgB,SAAS,CAAE,GAAG,CACtBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,OAAO,CACjB,SAAS,CAAE,CACZC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YAAY,CACvBC,YAAY,CAAE,YAAY,CAC1BH,QAAQ,CAAE,MACT,CAAC,CACD,2CAA2C,CAAE,CAC5Cd,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAC,gBACT,CAAC,CACD,0BAA0B,CAAE,CAC7B,SAAS,CAAE,CACTW,WAAW,CAAE,iBACf,CAAC,CACD,eAAe,CAAE,CACfA,WAAW,CAAE,iBACf,CACC,CAAC,CACD,oCAAoC,CAAE,CACrCA,WAAW,CAAE,iBAAiB,CAChCU,WAAW,CAAC,gBACX,CACM,CACT,CAAC,CACDd,OAAO,CAAE9G,8BACF,CAAE,CAEF6H,WAAW,CAAGlH,QAAQ,EAAKA,QAAQ,EAAIpI,SAAS,CAAC,oBAAoB,CAAE,CAAA0N,QAAA,eAGzErO,IAAA,CAAC7B,GAAG,EACFuQ,EAAE,CAAE,CACFwB,QAAQ,CAAE,QAAQ,CAClB5J,GAAG,CAAE,CAAC,CACN6J,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,OAAO,CACxB3B,OAAO,CAAE,KAAK,CACd4B,YAAY,CAAE,gBAChB,CACD;AAAA,CACCC,SAAS,CAAGhN,CAAC,EAAKA,CAAC,CAACiN,eAAe,CAAC,CAAG;AAAA,CAAApC,QAAA,cAEvCrO,IAAA,CAAC3B,SAAS,EACRwQ,SAAS,MACT6B,WAAW,CAAE/P,SAAS,CAAC,wBAAwB,CAAE,CACjDiO,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZ/K,KAAK,CAAEhB,UAAW,CAClB6L,OAAO,CAAG/K,CAAC,EAAK,CACfA,CAAC,CAACiN,eAAe,CAAC,CAAC,CAAE;AACrBzI,4BAA4B,CAAC,IAAI,CAAC,CACjC,CAAE,CACFwI,SAAS,CAAGhN,CAAC,EAAK,CACnBA,CAAC,CAACiN,eAAe,CAAC,CAAC,CAAE;AACrB,GAAIjN,CAAC,CAACqB,GAAG,GAAK,QAAQ,CAAE,CACtBuD,8BAA8B,CAAC,CAAC,CAAE;AACpC,CACA,GAAI5E,CAAC,CAACqB,GAAG,GAAK,OAAO,CAAG,CACvBnC,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAACmB,SAAS,CAAC,CAAC,CAAE7D,UAAU,CAAC,CAAE6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AAC7D,CACD,CAAE,CACF4I,QAAQ,CAAG3L,CAAC,EAAKb,aAAa,CAACa,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;AAAA,CACjDiN,SAAS,MACTC,UAAU,CAAE,CACXC,cAAc,cACZ7Q,IAAA,CAACxB,cAAc,EAAC0R,QAAQ,CAAC,OAAO,CAAA7B,QAAA,cACjCrO,IAAA,CAAC1B,UAAU,EACT,aAAW,QAAQ,CACnBiQ,OAAO,CAAG/K,CAAC,EAAK,CACjBA,CAAC,CAACiN,eAAe,CAAC,CAAC,CACnB,GAAI/N,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAE,CAAG;AACxBmB,SAAS,CAAC,CAAC,CAAE7D,UAAU,CAAC,CAC1B,CAAC,IACG,CACH6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CACjB,CACC,CAAE,CAAA8H,QAAA,cAEFrO,IAAA,CAACf,UAAU,GAAE,CAAC,CACJ,CAAC,CACI,CAEjB,CAAE,CACJyP,EAAE,CAAE,CACH,0BAA0B,CAAE,CAAEI,YAAY,CAAE,KAAM,CAAC,CACpD,2BAA2B,CAAE,CAC5BC,MAAM,CAAC,cACP,CACD,CAAE,CACH,CAAC,CACC,CAAC,cAGN7O,KAAA,CAAC/B,GAAG,EACFuQ,EAAE,CAAE,CACFgB,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,MACb,CAAE,CACFmB,QAAQ,CAAErJ,gBAAkB;AAAA,CAAA4G,QAAA,EAE3BrK,oBAAoB,EAAIA,oBAAoB,CAAC2C,MAAM,GAAK,CAAC,EAAI,CAACb,OAAO,eACpE9F,IAAA,CAACrB,QAAQ,EAACoS,QAAQ,MAAA1C,QAAA,CAAE1N,SAAS,CAAC,uBAAuB,CAAC,CAAW,CAClE,CACAqD,oBAAoB,EAAIA,oBAAoB,CAACmF,GAAG,CAAEpG,WAAW,eAC5D/C,IAAA,CAACrB,QAAQ,EAEP+E,KAAK,CAAEX,WAAW,CAACmB,IAAK,CACxBqK,OAAO,CAAEA,CAAA,GAAM,CACzBtG,uBAAuB,CAAClF,WAAW,CAACmB,IAAI,CAAC,CACxC;AACS,CAAE,CAAAmK,QAAA,CAEDtL,WAAW,CAACmB,IAAI,EAPZnB,WAAW,CAACuB,OAQT,CACX,CAAC,CACDwB,OAAO,eACN9F,IAAA,CAACrB,QAAQ,EAACoS,QAAQ,MAACrC,EAAE,CAAE,CAAE5F,OAAO,CAAE,MAAM,CAAEkI,cAAc,CAAE,QAAS,CAAE,CAAA3C,QAAA,cACnErO,IAAA,CAACnB,gBAAgB,EAAC4P,IAAI,CAAE,EAAG,CAAE,CAAC,CACtB,CACX,EACE,CAAC,EACA,CAAC,CACE,CAAC,CACT7N,cAAc,eACdZ,IAAA,QAAK4I,KAAK,CAAE,CAAEqI,KAAK,CAAE,SAAS,CAAEtC,OAAO,CAAE,EAAE,CAAEuC,YAAY,CAAE,CAAC,CAAEC,QAAQ,CAAE,EAAE,CAAE9B,SAAS,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAC9FzN,cAAc,CACX,CACL,EACF,CAAC,cACDV,KAAA,CAAC/B,GAAG,EACF2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDrO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEuC,YAAY,CAAE,gBAAiB,CAAE,CAAA7C,QAAA,CAAE1N,SAAS,CAAC,OAAO,CAAC,CAAa,CAAC,cAE7IX,IAAA,CAAC3B,SAAS,EACRuQ,OAAO,CAAC,UAAU,CACeH,IAAI,CAAC,OAAO,CAC/CiC,WAAW,CAAE/P,SAAS,CAAC,YAAY,CAAE,CACF2N,SAAS,CAAC,qBAAqB,CAClE1F,KAAK,CAAE,CAAE8B,KAAK,CAAE,MAAO,CAAE,CACzBhH,KAAK,CAAEd,iCAAiC,CAACI,KAAM,CAC7CmM,QAAQ,CAAG3L,CAAC,EAAKmB,gBAAgB,CAAC,OAAO,CAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC3DkN,UAAU,CAAE,CACXQ,YAAY,CAAE,EAAE,CAChB1C,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEF,SAAS,CAAE,iBAAiB,CAAEgC,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAC1G,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAGNzK,KAAA,CAAC/B,GAAG,EACF2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDrO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEuC,YAAY,CAAE,gBAAiB,CAAE,CAAA7C,QAAA,CAAG1N,SAAS,CAAC,aAAa,CAAC,CAAa,CAAC,cAEpJX,IAAA,CAAC3B,SAAS,EACRuQ,OAAO,CAAC,UAAU,CACeH,IAAI,CAAC,OAAO,CAC/CiC,WAAW,CAAE/P,SAAS,CAAC,WAAW,CAAE,CACpC2N,SAAS,CAAC,qBAAqB,CAC/BgD,SAAS,MACTC,OAAO,CAAE,CAAE,CACX7N,KAAK,CAAEd,iCAAiC,CAACK,WAAY,CACrDkM,QAAQ,CAAG3L,CAAC,EAAKmB,gBAAgB,CAAC,aAAa,CAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAE9BkF,KAAK,CAAE,CAAC8B,KAAK,CAAC,MAAM,CAAE,CACvDkG,UAAU,CAAE,CACXQ,YAAY,CAAE,EAAE,CAChB1C,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEF,SAAS,CAAE,iBAAiB,CAAEgC,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAC1G,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAINzK,KAAA,CAAC/B,GAAG,EACF2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDnO,KAAA,QAAKoO,SAAS,CAAC,qBAAqB,CAAC1F,KAAK,CAAE,CAACE,OAAO,CAAC,MAAM,CAAC0I,aAAa,CAAC,KAAK,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAC,KAAK,CAAC/C,OAAO,CAAC,GAAG,CAAEuC,YAAY,CAAC,MAAM,CAAE,CAAA7C,QAAA,eAC/IrO,IAAA,CAAC5B,UAAU,EAACsQ,EAAE,CAAE,CAAEuC,KAAK,CAAE,SAAS,CAAEU,UAAU,CAAE,KAAM,CAAE,CAAAtD,QAAA,CAAE1N,SAAS,CAAC,cAAc,CAAC,CAAa,CAAC,cACjGX,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAElJ,QAAS,CAAE,CAACmJ,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,IAAC,EAAK,CAAC,cAC3F5I,KAAA,CAAC9B,UAAU,EAACwK,KAAK,CAAE,CAAEuI,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,SAAS,CAAE5B,SAAS,CAAE,MAAM,CAAEV,OAAO,CAAE,GAAG,CAAEuC,YAAY,CAAE,MAAO,CAAE,CAAA7C,QAAA,EAAC,WAAS,CAAC1N,SAAS,CAAC,+FAA+F,CAAC,EAAa,CAAC,cAEnPX,IAAA,CAAC3B,SAAS,EACRuQ,OAAO,CAAC,UAAU,CACeH,IAAI,CAAC,OAAO,CAC/CiC,WAAW,CAAE/P,SAAS,CAAC,iBAAiB,CAAE,CACP2N,SAAS,CAAC,qBAAqB,CAC/B1F,KAAK,CAAE,CAAC8B,KAAK,CAAE,MAAM,CAAE,CAC1DhH,KAAK,CAAEd,iCAAiC,CAACM,WAAY,CACnDiM,QAAQ,CAAG3L,CAAC,EAAKmB,gBAAgB,CAAC,aAAa,CAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjEkN,UAAU,CAAE,CACXQ,YAAY,CAAE,EAAE,CAChB1C,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEF,SAAS,CAAE,iBAAiB,CAAEgC,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAC1G,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAINzK,KAAA,CAAC/B,GAAG,EAAC2E,EAAE,CAAC,mBAAmB,CAACwL,SAAS,CAAC,wCACrC;AAAA,CAAAD,QAAA,eAEArO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAN,QAAA,CAClF1N,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,cAC7BX,IAAA,CAAC7B,GAAG,EAACuQ,EAAE,CAAE,CAAE5F,OAAO,CAAE,MAAM,CAAE4I,GAAG,CAAE,CAAC,CAAED,UAAU,CAAE,QAAQ,CAAE/G,KAAK,CAAC,wBAAwB,CAACkH,QAAQ,CAAC,MAAM,CAAE,CAAAvD,QAAA,CACrG9F,KAAK,CAACY,GAAG,CAAChG,IAAI,eACXnD,IAAA,CAACpB,OAAO,EAAeoE,KAAK,CAAC,aAAa,CAAC6O,KAAK,MAAAxD,QAAA,cAC5CrO,IAAA,CAAC1B,UAAU,EACPiQ,OAAO,CAAEA,CAAA,GAAMtF,eAAe,CAAC9F,IAAI,CAACL,EAAE,CAAE,CACxC4L,EAAE,CAAE,CACAa,MAAM,CAAEpM,IAAI,CAAC4F,QAAQ,CAAG,+BAA+B,CAAG,MAAM,CAChE+F,YAAY,CAAE,KAAK,CAC3CH,OAAO,CAAE,KAAK,CACdmD,UAAU,CAAC,SACS,CAAE,CAAAzD,QAAA,CAEDlL,IAAI,CAACsF,SAAS,CACP,CAAC,EAXHtF,IAAI,CAACL,EAYV,CACZ,CAAC,CAGD,CAAC,EAGL,CAAC,cAGZ5C,KAAA,CAAC/B,GAAG,EACA2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCACZ;AAAA,CAAAD,QAAA,eAEIrO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEC,OAAO,CAAE,sBAAuB,CAAE,CAAAN,QAAA,CAAE1N,SAAS,CAAC,kBAAkB,CAAC,CAAa,CAAC,cAEtIT,KAAA,QACD0I,KAAK,CAAE,CACL8B,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,MAAM,CACdoE,MAAM,CAAE,eAAe,CACvBjG,OAAO,CAAE,MAAM,CACf0I,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBT,cAAc,CAAE,QAAQ,CACxBzB,MAAM,CAAE,gCAAgC,CACxCT,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACdmD,UAAU,CAAE,SAAS,CACrBzC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,eAEFnO,KAAA,CAAC3B,MAAM,EACL+P,SAAS,CAAC,qBAAqB,CAC/B1F,KAAK,CAAE,CACT+B,MAAM,CAAE,MAAM,CACdgE,OAAO,CAAE,GAAG,CACRjE,KAAK,CAAE,MAAM,CACb5B,OAAO,CAAE,MAAM,CACf0I,aAAa,CAAE,KAAK,CAAE;AACtBC,UAAU,CAAE,QAAQ,CACpBT,cAAc,CAAE,QAAQ,CACxBU,GAAG,CAAE,KAAK,CACVT,KAAK,CAAE,MAAM,CACbX,eAAe,CAAE,SAAS,CAC1ByB,aAAa,CAAE,YAAY,CAC3BC,SAAS,CAAE,MACb,CAAE,CACFvJ,SAAS,CAAC,OAAO,CAAA4F,QAAA,eAEjBrO,IAAA,CAACJ,uBAAuB,EAAC8O,EAAE,CAAE,CAAC7F,IAAI,CAAC,KAAK,CAAE,CAAE,CAAC,cAE7C,cAAA7I,IAAA,UACE8C,EAAE,CAAC,YAAY,CACf+I,IAAI,CAAC,MAAM,CACXoG,QAAQ,MACRC,MAAM,CAAC,+BAA+B,CACtC/C,QAAQ,CAAE5D,gBAAiB,CAC3B3C,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,EACI,CAAC,cAGT9I,IAAA,CAAC5B,UAAU,EAACwK,KAAK,CAAE,CAAEuI,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,SAAU,CAAE,CAAA5C,QAAA,CAAC,wBAE3D,CAAY,CAAC,EACV,CAAC,CACL/C,SAAS,eACRpL,KAAA,QAAK0I,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAE2I,UAAU,CAAE,QAAQ,CAAGR,KAAK,CAAE,SAAS,CAACtC,OAAO,CAAC,OAAO,CAACU,SAAS,CAAC,MAAM,CAAE3E,KAAK,CAAE,wBAAwB,CAAE,CAAA2D,QAAA,eAExIrO,IAAA,SACE4I,KAAK,CAAE,CAAEuJ,WAAW,CAAE,KAAK,CAACrJ,OAAO,CAAC,MAAO,CAAE,CAC7CJ,uBAAuB,CAAE,CAAEC,MAAM,CAAEjJ,OAAQ,CAAE,CAC9C,CAAC,cACFM,IAAA,QAAK4I,KAAK,CAAE,CAACuI,QAAQ,CAAE,MAAM,CAAE,CAAA9C,QAAA,CAAE/C,SAAS,CAAM,CAAC,EAC9C,CACN,cAKKpL,KAAA,CAAC/B,GAAG,EAACuQ,EAAE,CAAE,CAAChE,KAAK,CAAC,wBAAwB,CAAE,CAAA2D,QAAA,EACvCrE,KAAK,CAACb,GAAG,CAAC,CAACY,IAAI,CAAE4D,KAAK,gBACrBzN,KAAA,CAAC/B,GAAG,EAEF2K,OAAO,CAAC,MAAM,CACd2I,UAAU,CAAC,QAAQ,CACnBT,cAAc,CAAC,eAAe,CACvCtC,EAAE,CAAE,CACHI,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACdI,MAAM,CAAE,KAAK,CACbuB,eAAe,CAAE,SAChB,CAAE,CAAAjC,QAAA,eAGKrO,IAAA,QACEsK,GAAG,CAAEC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAE,CAC/Ba,GAAG,CAAE,YAAY+C,KAAK,EAAG,CACzB/E,KAAK,CAAE,CAAE8B,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEmE,YAAY,CAAE,KAAM,CAAE,CAC/D,CAAC,cAEF9O,IAAA,CAAC5B,UAAU,EACTsQ,EAAE,CAAE,CAAE0D,IAAI,CAAE,CAAC,CAAEjB,QAAQ,CAAE,MAAM,CAAErB,SAAS,CAAE,YAAa,CAAE,CAAAzB,QAAA,CAE1DtE,IAAI,CAACG,IAAI,CACA,CAAC,cACblK,IAAA,CAAC1B,UAAU,EAACiQ,OAAO,CAAEA,CAAA,GAAMb,gBAAgB,CAACC,KAAK,CAAE,CAACc,IAAI,CAAC,OAAO,CAAAJ,QAAA,cACzErO,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEzJ,UAAW,CAAE,CAAC0J,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,OAAO,CAAC,MAAO,CAAE,CAAE,CAAC,CAC1E,CAAC,GAzBR6E,KA0BF,CACN,CAAC,CAGD3C,OAAO,eACN9K,KAAA,CAAC/B,GAAG,EACF2K,OAAO,CAAC,MAAM,CACd2I,UAAU,CAAC,QAAQ,CACnBT,cAAc,CAAC,eAAe,CAC9BtC,EAAE,CAAE,CACZI,YAAY,CAAE,MAAM,CACpBH,OAAO,CAAE,KAAK,CACdI,MAAM,CAAE,KAAK,CACbuB,eAAe,CAAE,SAChB,CAAE,CAAAjC,QAAA,eAEKrO,IAAA,QACEsK,GAAG,CAAEC,GAAG,CAACC,eAAe,CAACQ,OAAO,CAAE,CAClCJ,GAAG,CAAC,cAAc,CAClBhC,KAAK,CAAE,CAAE8B,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEmE,YAAY,CAAE,KAAM,CAAE,CAC/D,CAAC,cACF9O,IAAA,CAAC5B,UAAU,EACTsQ,EAAE,CAAE,CAAE0D,IAAI,CAAE,CAAC,CAAEjB,QAAQ,CAAE,MAAM,CAAErB,SAAS,CAAE,YAAa,CAAE,CAAAzB,QAAA,CAE1DrD,OAAO,CAACd,IAAI,CACH,CAAC,cACblK,IAAA,CAAC1B,UAAU,EAACiQ,OAAO,CAAEP,eAAgB,CAACS,IAAI,CAAC,OAAO,CAAAJ,QAAA,cAC3DrO,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEzJ,UAAW,CAAE,CAAC0J,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,OAAO,CAAC,MAAO,CAAE,CAAE,CAAC,CAC1E,CAAC,EACZ,CACR,CAEKoC,SAAS,eACXhL,KAAA,CAAC/B,GAAG,EAAC2K,OAAO,CAAC,MAAM,CAAC2I,UAAU,CAAC,QAAQ,CAACT,cAAc,CAAC,eAAe,CAClEtC,EAAE,CAAE,CACAa,MAAM,CAAE,gBAAgB,CACxBT,YAAY,CAAE,KAAK,CACnBH,OAAO,CAAE,KAAK,CACduC,YAAY,CAAE,KAAK,CACnBxG,KAAK,CAAE,OAAO,CACd4F,eAAe,CAAE,SACrB,CAAE,CAAAjC,QAAA,eACFnO,KAAA,UAAOwK,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC0H,QAAQ,MAAAhE,QAAA,eAClCrO,IAAA,WAAQsK,GAAG,CAAEC,GAAG,CAACC,eAAe,CAACU,SAAS,CAAE,CAACW,IAAI,CAAC,WAAW,CAAE,CAAC,+CAEpE,EAAO,CAAC,cACR7L,IAAA,CAAC5B,UAAU,EAACsQ,EAAE,CAAE,CAAE0D,IAAI,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAC,CAAEnB,QAAQ,CAAE,MAAM,CAAErB,SAAS,CAAE,YAAa,CAAE,CAAAzB,QAAA,CACzEnD,SAAS,CAAChB,IAAI,CACP,CAAC,cACblK,IAAA,CAAC1B,UAAU,EAACiQ,OAAO,CAAEJ,iBAAkB,CAACM,IAAI,CAAC,OAAO,CAAAJ,QAAA,cAChDrO,IAAA,SAAM0I,uBAAuB,CAAE,CAAEC,MAAM,CAAEzJ,UAAW,CAAE,CAAC0J,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CAAE,CAAC,CACvE,CAAC,EACd,CACN,EACI,CAAC,EACD,CAAC,cAKH3I,KAAA,CAAC/B,GAAG,EACF2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDrO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAE6D,aAAa,CAAE,cAAe,CAAE,CAAAlE,QAAA,CAAE1N,SAAS,CAAC,aAAa,CAAC,CAAa,CAAC,cAEzHX,IAAA,CAAC3B,SAAS,EACRuQ,OAAO,CAAC,UAAU,CACeH,IAAI,CAAC,OAAO,CAC/CiC,WAAW,CAAE/P,SAAS,CAAC,aAAa,CAAE,CACH2N,SAAS,CAAC,qBAAqB,CAChE1F,KAAK,CAAE,CAAC8B,KAAK,CAAE,MAAM,CAAE,CACzBhH,KAAK,CAAEd,iCAAiC,CAACS,UAAW,CAClD8L,QAAQ,CAAG3L,CAAC,EAAKmB,gBAAgB,CAAC,YAAY,CAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAChEkN,UAAU,CAAE,CACXQ,YAAY,CAAE,EAAE,CAChB1C,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEF,SAAS,CAAE,iBAAiB,CAAEgC,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAC1G,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAGNzK,KAAA,CAAC/B,GAAG,EACF2E,EAAE,CAAC,mBAAmB,CACtBwL,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEnDrO,IAAA,CAAC5B,UAAU,EAACkQ,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAE6D,aAAa,CAAE,gBAAiB,CAAE,CAAAlE,QAAA,CAAE1N,SAAS,CAAC,mBAAmB,CAAC,CAAa,CAAC,cAElIX,IAAA,CAAC3B,SAAS,EACTqF,KAAK,CAAEd,iCAAiC,CAACU,gBAAiB,CAC1D6L,QAAQ,CAAG3L,CAAC,EAAK,CAChB,GAAI,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC1B,GAAIA,KAAK,CAACiD,MAAM,CAAG,GAAG,CAAE,CACvBjD,KAAK,CAAGA,KAAK,CAAC8O,KAAK,CAAC,CAAC,CAAE,GAAG,CAAC,CAC5B,CACA7N,gBAAgB,CAAC,kBAAkB,CAAEjB,KAAK,CAAC,CAC5C,CAAE,CACAkL,OAAO,CAAC,UAAU,CACeH,IAAI,CAAC,OAAO,CAC/CiC,WAAW,CAAE/P,SAAS,CAAC,YAAY,CAAE,CACrC2N,SAAS,CAAC,qBAAqB,CAC/BgD,SAAS,MACTC,OAAO,CAAE,CAAE,CACX3I,KAAK,CAAE,CAAC8B,KAAK,CAAE,MAAM,CAAE,CACvB+H,UAAU,CAAE,GAAG,EAAAlS,qBAAA,CAAAqC,iCAAiC,CAACU,gBAAgB,UAAA/C,qBAAA,iBAAlDA,qBAAA,CAAoDoG,MAAM,GAAI,CAAC,MAAO,CACrFiK,UAAU,CAAE,CACTQ,YAAY,CAAE,EAAE,CAChB1C,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEa,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEF,SAAS,CAAE,iBAAiB,CAAEgC,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAC1G,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,EAIF,CAAC,CACF,CAAC,cAGN3K,IAAA,QAAKsO,SAAS,CAAC,oBAAoB,CAAAD,QAAA,cAElCrO,IAAA,CAACzB,MAAM,EACNqQ,OAAO,CAAC,WAAW,CACnBL,OAAO,CAAEtJ,kBAAmB,CAC5BqJ,SAAS,CAAC,WAAW,CAAAD,QAAA,CACrB,OAED,CAAQ,CAAC,CACL,CAAC,EACU,CAAC,CAGd,CAAC,CACJ,CAAC,CAEN,CAAC,CAED,cAAe,CAAAhO,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}