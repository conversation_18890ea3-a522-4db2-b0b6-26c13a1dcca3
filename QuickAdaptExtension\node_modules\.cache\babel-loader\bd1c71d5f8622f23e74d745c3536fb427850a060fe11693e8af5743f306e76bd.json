{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nexport const useExtensionInitialization = () => {\n  _s();\n  const [state, setState] = useState({\n    isInitializing: true,\n    isFirstLoad: true,\n    initializationComplete: false\n  });\n  useEffect(() => {\n    // Check if this is the first time the extension is being loaded\n    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');\n    if (!hasBeenInitialized) {\n      // First time loading - show popup loader\n      setState(prev => ({\n        ...prev,\n        isFirstLoad: true,\n        isInitializing: true\n      }));\n\n      // Simulate initialization process\n      const initializationSteps = [{\n        delay: 500,\n        message: 'Loading extension...'\n      }, {\n        delay: 1000,\n        message: 'Initializing components...'\n      }, {\n        delay: 1500,\n        message: 'Setting up environment...'\n      }, {\n        delay: 2000,\n        message: 'Almost ready...'\n      }, {\n        delay: 2500,\n        message: 'Complete!'\n      }];\n\n      // Complete initialization after all steps\n      setTimeout(() => {\n        setState(prev => ({\n          ...prev,\n          isInitializing: false,\n          initializationComplete: true\n        }));\n\n        // Mark as initialized in session storage\n        sessionStorage.setItem('quickadapt-initialized', 'true');\n      }, 3000);\n    } else {\n      // Already initialized in this session - skip popup\n      setState({\n        isInitializing: false,\n        isFirstLoad: false,\n        initializationComplete: true\n      });\n    }\n  }, []);\n  const resetInitialization = () => {\n    sessionStorage.removeItem('quickadapt-initialized');\n    setState({\n      isInitializing: true,\n      isFirstLoad: true,\n      initializationComplete: false\n    });\n  };\n  const hideInitializationLoader = () => {\n    setState(prev => ({\n      ...prev,\n      isInitializing: false\n    }));\n  };\n  return {\n    ...state,\n    resetInitialization,\n    hideInitializationLoader\n  };\n};\n_s(useExtensionInitialization, \"KX2m/39mANXHPocLJOghF0AXg9Y=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useExtensionInitialization", "_s", "state", "setState", "isInitializing", "isFirstLoad", "initializationComplete", "hasBeenInitialized", "sessionStorage", "getItem", "prev", "initializationSteps", "delay", "message", "setTimeout", "setItem", "resetInitialization", "removeItem", "hideInitializationLoader"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/hooks/useExtensionInitialization.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\n\r\ninterface ExtensionInitializationState {\r\n  isInitializing: boolean;\r\n  isFirstLoad: boolean;\r\n  initializationComplete: boolean;\r\n}\r\n\r\nexport const useExtensionInitialization = () => {\r\n  const [state, setState] = useState<ExtensionInitializationState>({\r\n    isInitializing: true,\r\n    isFirstLoad: true,\r\n    initializationComplete: false\r\n  });\r\n\r\n  useEffect(() => {\r\n    // Check if this is the first time the extension is being loaded\r\n    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');\r\n    \r\n    if (!hasBeenInitialized) {\r\n      // First time loading - show popup loader\r\n      setState(prev => ({\r\n        ...prev,\r\n        isFirstLoad: true,\r\n        isInitializing: true\r\n      }));\r\n\r\n      // Simulate initialization process\r\n      const initializationSteps = [\r\n        { delay: 500, message: 'Loading extension...' },\r\n        { delay: 1000, message: 'Initializing components...' },\r\n        { delay: 1500, message: 'Setting up environment...' },\r\n        { delay: 2000, message: 'Almost ready...' },\r\n        { delay: 2500, message: 'Complete!' }\r\n      ];\r\n\r\n      // Complete initialization after all steps\r\n      setTimeout(() => {\r\n        setState(prev => ({\r\n          ...prev,\r\n          isInitializing: false,\r\n          initializationComplete: true\r\n        }));\r\n        \r\n        // Mark as initialized in session storage\r\n        sessionStorage.setItem('quickadapt-initialized', 'true');\r\n      }, 3000);\r\n\r\n    } else {\r\n      // Already initialized in this session - skip popup\r\n      setState({\r\n        isInitializing: false,\r\n        isFirstLoad: false,\r\n        initializationComplete: true\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  const resetInitialization = () => {\r\n    sessionStorage.removeItem('quickadapt-initialized');\r\n    setState({\r\n      isInitializing: true,\r\n      isFirstLoad: true,\r\n      initializationComplete: false\r\n    });\r\n  };\r\n\r\n  const hideInitializationLoader = () => {\r\n    setState(prev => ({\r\n      ...prev,\r\n      isInitializing: false\r\n    }));\r\n  };\r\n\r\n  return {\r\n    ...state,\r\n    resetInitialization,\r\n    hideInitializationLoader\r\n  };\r\n};\r\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAQ3C,OAAO,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGL,QAAQ,CAA+B;IAC/DM,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFP,SAAS,CAAC,MAAM;IACd;IACA,MAAMQ,kBAAkB,GAAGC,cAAc,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAE3E,IAAI,CAACF,kBAAkB,EAAE;MACvB;MACAJ,QAAQ,CAACO,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPL,WAAW,EAAE,IAAI;QACjBD,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMO,mBAAmB,GAAG,CAC1B;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAuB,CAAC,EAC/C;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA6B,CAAC,EACtD;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA4B,CAAC,EACrD;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAkB,CAAC,EAC3C;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAY,CAAC,CACtC;;MAED;MACAC,UAAU,CAAC,MAAM;QACfX,QAAQ,CAACO,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPN,cAAc,EAAE,KAAK;UACrBE,sBAAsB,EAAE;QAC1B,CAAC,CAAC,CAAC;;QAEH;QACAE,cAAc,CAACO,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC;MAC1D,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,MAAM;MACL;MACAZ,QAAQ,CAAC;QACPC,cAAc,EAAE,KAAK;QACrBC,WAAW,EAAE,KAAK;QAClBC,sBAAsB,EAAE;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCR,cAAc,CAACS,UAAU,CAAC,wBAAwB,CAAC;IACnDd,QAAQ,CAAC;MACPC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE,IAAI;MACjBC,sBAAsB,EAAE;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;IACrCf,QAAQ,CAACO,IAAI,KAAK;MAChB,GAAGA,IAAI;MACPN,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAO;IACL,GAAGF,KAAK;IACRc,mBAAmB;IACnBE;EACF,CAAC;AACH,CAAC;AAACjB,EAAA,CAvEWD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}