import React from 'react';
import { render, screen } from '@testing-library/react';
import PopupLoader from './PopupLoader';

describe('PopupLoader', () => {
  test('renders loader when visible is true', () => {
    render(<PopupLoader isVisible={true} />);
    
    // Check if the loader container is present
    const loaderContainer = document.querySelector('.qadpt-popup-loader');
    expect(loaderContainer).toBeInTheDocument();
    
    // Check if the spinner is present
    const spinner = document.querySelector('.qadpt-loader-spinner');
    expect(spinner).toBeInTheDocument();
    
    // Check if the text is present
    expect(screen.getByText('QuickAdapt')).toBeInTheDocument();
  });

  test('does not render loader when visible is false', () => {
    render(<PopupLoader isVisible={false} />);
    
    // Check if the loader container is not present
    const loaderContainer = document.querySelector('.qadpt-popup-loader');
    expect(loaderContainer).not.toBeInTheDocument();
  });

  test('has correct CSS classes applied', () => {
    render(<PopupLoader isVisible={true} />);
    
    // Check if the main container has the correct class
    const loaderContainer = document.querySelector('.qadpt-popup-loader');
    expect(loaderContainer).toHaveClass('qadpt-popup-loader');
    
    // Check if the inner container has the correct class
    const innerContainer = document.querySelector('.qadpt-loader-container');
    expect(innerContainer).toHaveClass('qadpt-loader-container');
    
    // Check if the spinner has the correct class
    const spinner = document.querySelector('.qadpt-loader-spinner');
    expect(spinner).toHaveClass('qadpt-loader-spinner');
    
    // Check if the text has the correct class
    const text = document.querySelector('.qadpt-loader-text');
    expect(text).toHaveClass('qadpt-loader-text');
  });
});
