{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ChecklistPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\nimport useDrawerStore from '../../store/drawerStore';\nimport ImageCarousel from \"./ImageCarousel\";\nimport VideoPlayer from \"./VideoPlayer\";\nimport { chkdefault, closepluginicon, maximize } from '../../assets/icons/icons';\nimport { GetGudeDetailsByGuideId } from '../../services/GuideListServices';\nimport { useTranslation } from 'react-i18next';\nimport '../../styles/rtl_styles.scss';\n\n// Function to modify the color of an SVG icon\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst modifySVGColor = (base64SVG, color) => {\n  if (!base64SVG) {\n    return \"\";\n  }\n  try {\n    // Check if the string is a valid base64 SVG\n    if (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\n      return base64SVG; // Return the original if it's not an SVG\n    }\n    const decodedSVG = atob(base64SVG.split(\",\")[1]);\n\n    // Check if this is primarily a stroke-based or fill-based icon\n    const hasStroke = decodedSVG.includes('stroke=\"');\n    const hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\n    let modifiedSVG = decodedSVG;\n    if (hasStroke && !hasColoredFill) {\n      // This is a stroke-based icon (like chkicn2-6) - only change stroke color\n      modifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\n    } else if (hasColoredFill) {\n      // This is a fill-based icon (like chkicn1) - only change fill color\n      modifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\n    } else {\n      // No existing fill or stroke, add fill to make it visible\n      modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\n      modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\n    }\n    const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\n    return modifiedBase64;\n  } catch (error) {\n    console.error(\"Error modifying SVG color:\", error);\n    return base64SVG; // Return the original if there's an error\n  }\n};\nconst ChecklistPreview = ({\n  isOpen,\n  onClose,\n  onRemainingCountUpdate,\n  data,\n  guideDetails,\n  isRightPanelVisible,\n  setIsRightPanelVisible\n}) => {\n  _s();\n  var _checklistGuideMetaDa, _checklistGuideMetaDa2, _checklistGuideMetaDa3, _checkpointslistData$, _checklistGuideMetaDa4, _checklistGuideMetaDa5, _checklistGuideMetaDa9, _checklistGuideMetaDa10, _checklistGuideMetaDa11, _checklistGuideMetaDa12, _checklistGuideMetaDa13, _checklistGuideMetaDa14, _checklistGuideMetaDa15, _checklistGuideMetaDa16, _checklistGuideMetaDa17, _checklistGuideMetaDa18, _checklistGuideMetaDa19, _checklistGuideMetaDa20, _checklistGuideMetaDa21, _checklistGuideMetaDa22, _checklistGuideMetaDa23, _checklistGuideMetaDa24, _checklistGuideMetaDa25, _checklistGuideMetaDa26, _checklistGuideMetaDa27, _checklistGuideMetaDa28, _checklistGuideMetaDa29, _checklistGuideMetaDa30, _checklistGuideMetaDa31, _checklistGuideMetaDa32, _checklistGuideMetaDa33, _checklistGuideMetaDa34, _checklistGuideMetaDa35, _checklistGuideMetaDa36, _checklistGuideMetaDa37, _checklistGuideMetaDa38, _checklistGuideMetaDa39, _checklistGuideMetaDa40, _checklistGuideMetaDa41, _checklistGuideMetaDa42, _checklistGuideMetaDa43, _checklistGuideMetaDa44, _checklistGuideMetaDa45, _checklistGuideMetaDa46, _checklistGuideMetaDa47, _selectedItem$support, _selectedItem$support2, _checklistGuideMetaDa54, _selectedItem$support3, _checklistGuideMetaDa55, _checklistGuideMetaDa56, _checklistGuideMetaDa57, _checklistGuideMetaDa58, _checklistGuideMetaDa59, _checklistGuideMetaDa60, _checklistGuideMetaDa61, _checklistGuideMetaDa62, _checklistGuideMetaDa63, _checklistGuideMetaDa64, _selectedItem$support4, _selectedItem$support5, _checklistGuideMetaDa65, _checklistGuideMetaDa66, _selectedItem$support6, _checklistGuideMetaDa67, _checklistGuideMetaDa68;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    checklistGuideMetaData,\n    createWithAI,\n    interactionData\n  } = useDrawerStore(state => state);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [completedStatus, setCompletedStatus] = useState({});\n  const checkpointslistData = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : (_checklistGuideMetaDa2 = _checklistGuideMetaDa.checkpoints) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : (_checklistGuideMetaDa3 = _checklistGuideMetaDa2.checkpointsList) === null || _checklistGuideMetaDa3 === void 0 ? void 0 : _checklistGuideMetaDa3.map((checkpoint, index) => ({\n    ...checkpoint,\n    completed: index === 0 ? true : false\n  }))) || [];\n  const [checklistItems, setChecklistItems] = useState(checkpointslistData);\n  const [activeItem, setActiveItem] = useState(((_checkpointslistData$ = checkpointslistData[0]) === null || _checkpointslistData$ === void 0 ? void 0 : _checkpointslistData$.id) || \"\");\n  useEffect(() => {\n    if (Object.keys(completedStatus).length === 0) {\n      const initialCompletedStatus = {};\n      checkpointslistData.forEach((item, index) => {\n        initialCompletedStatus[item.id] = index === 0;\n      });\n      setCompletedStatus(initialCompletedStatus);\n    }\n  }, []);\n  const checklistColor = (_checklistGuideMetaDa4 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa4 === void 0 ? void 0 : (_checklistGuideMetaDa5 = _checklistGuideMetaDa4.canvas) === null || _checklistGuideMetaDa5 === void 0 ? void 0 : _checklistGuideMetaDa5.primaryColor;\n  const selectedItem = checkpointslistData === null || checkpointslistData === void 0 ? void 0 : checkpointslistData.find(item => item.id === activeItem);\n  useEffect(() => {\n    document.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\n  }, [checklistColor]);\n  const [isPublished, setIsPublished] = useState(true);\n  useEffect(() => {\n    const fetchGuideDetails = async () => {\n      if (!(selectedItem !== null && selectedItem !== void 0 && selectedItem.id)) return; // Ensure there's a valid ID\n\n      try {\n        var _res$GuideDetails, _res$GuideDetails2;\n        const res = await GetGudeDetailsByGuideId(selectedItem.id, createWithAI, interactionData);\n        if ((res === null || res === void 0 ? void 0 : (_res$GuideDetails = res.GuideDetails) === null || _res$GuideDetails === void 0 ? void 0 : _res$GuideDetails.GuideStatus) === \"InActive\" || (res === null || res === void 0 ? void 0 : (_res$GuideDetails2 = res.GuideDetails) === null || _res$GuideDetails2 === void 0 ? void 0 : _res$GuideDetails2.GuideStatus) === \"Draft\") {\n          setIsPublished(false);\n        } else {\n          setIsPublished(true);\n        }\n      } catch (error) {}\n    };\n    fetchGuideDetails();\n  }, [selectedItem, activeItem]);\n  useEffect(() => {\n    var _checkpointslistData$2;\n    if (checkpointslistData) setActiveItem((_checkpointslistData$2 = checkpointslistData[0]) === null || _checkpointslistData$2 === void 0 ? void 0 : _checkpointslistData$2.id);\n  }, [(checkpointslistData === null || checkpointslistData === void 0 ? void 0 : checkpointslistData.length) == 1]);\n  useEffect(() => {\n    var _checklistGuideMetaDa6;\n    if (((_checklistGuideMetaDa6 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa6 === void 0 ? void 0 : _checklistGuideMetaDa6.length) > 0) {\n      var _checklistGuideMetaDa7, _checklistGuideMetaDa8;\n      const checkpointList = ((_checklistGuideMetaDa7 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa7 === void 0 ? void 0 : (_checklistGuideMetaDa8 = _checklistGuideMetaDa7.checkpoints) === null || _checklistGuideMetaDa8 === void 0 ? void 0 : _checklistGuideMetaDa8.checkpointlist) || [];\n      const formattedChecklist = checkpointList.map((checkpoint, index) => ({\n        id: checkpoint.id || index + 1,\n        title: checkpoint.title || `Step ${index + 1}`,\n        description: checkpoint.description || \"No description provided\",\n        redirectURL: checkpoint.redirectURL || \"\",\n        icon: checkpoint.icon || \"\",\n        supportingMedia: checkpoint.supportingMedia || \"\",\n        mediaTitle: checkpoint.mediaTitle || \"\",\n        mediaDescription: checkpoint.mediaDescription || \"\"\n      }));\n      setChecklistItems(formattedChecklist);\n      const initialCompletedStatus = {};\n      formattedChecklist.forEach(item => {\n        initialCompletedStatus[item.id] = false;\n      });\n      setCompletedStatus(initialCompletedStatus);\n    }\n  }, [checklistGuideMetaData[0]]); // Update when checklistGuideMetaData changes\n\n  const totalItems = checkpointslistData.length || 1;\n  const progress = Object.values(completedStatus).filter(status => status).length || 1;\n\n  // We'll let the ChecklistPopup component update the count based on completed status\n  // This useEffect is no longer needed as we're getting the count from ChecklistPopup\n\n  const toggleItemCompletion = id => {\n    setCompletedStatus(prevStatus => ({\n      ...prevStatus,\n      [id]: !prevStatus[id]\n    }));\n  };\n  const handleMarkAsCompleted = id => {\n    setCompletedStatus(prevStatus => ({\n      ...prevStatus,\n      [id]: true\n    }));\n  };\n  const handleSelect = id => {\n    setActiveItem(id);\n    setIsRightPanelVisible(true);\n  };\n  const handleClose = () => {\n    if (isRightPanelVisible) {\n      setIsRightPanelVisible(false);\n    } else {\n      onClose();\n    }\n  };\n  const handleMinimize = () => {\n    setIsMaximized(false);\n  };\n  if (!isOpen) return null;\n  if (!isOpen) return null;\n  const handleNavigate = () => {\n    // window.open(\"http://localhost:3000/\", '_blank');\n  };\n  const handleMaximize = () => {\n    setIsMaximized(true);\n  };\n  const xOffset = parseInt(((_checklistGuideMetaDa9 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa9 === void 0 ? void 0 : (_checklistGuideMetaDa10 = _checklistGuideMetaDa9.launcher) === null || _checklistGuideMetaDa10 === void 0 ? void 0 : (_checklistGuideMetaDa11 = _checklistGuideMetaDa10.launcherposition) === null || _checklistGuideMetaDa11 === void 0 ? void 0 : _checklistGuideMetaDa11.xaxisOffset) || \"10\");\n  const xOffsetWithUnit = `${xOffset + 30}px`;\n  const isRTL = document.documentElement.getAttribute('dir') === 'rtl' || document.body.getAttribute('dir') === 'rtl';\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        inset: 0,\n        display: \"flex\",\n        alignItems: \"center\",\n        // justifyContent: 'center',\n        zIndex: '99999'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"absolute\",\n          inset: 0,\n          backgroundColor: \"rgba(0, 0, 0, 0.3)\"\n        },\n        onClick: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          boxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\n          zIndex: 9,\n          marginTop: \"auto\",\n          \"--x-offset\": xOffsetWithUnit,\n          marginBottom: `${parseInt(((_checklistGuideMetaDa12 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa12 === void 0 ? void 0 : (_checklistGuideMetaDa13 = _checklistGuideMetaDa12.launcher) === null || _checklistGuideMetaDa13 === void 0 ? void 0 : (_checklistGuideMetaDa14 = _checklistGuideMetaDa13.launcherposition) === null || _checklistGuideMetaDa14 === void 0 ? void 0 : _checklistGuideMetaDa14.yaxisOffset) || \"10\") + 70}px`\n          // marginLeft: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px` : \"auto\",\n          // marginRight: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? \"auto\" : `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px`,\n        },\n        className: `qadpt-prvchkpopup ${(_checklistGuideMetaDa15 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa15 !== void 0 && _checklistGuideMetaDa15.launcher.launcherposition.left ? \"left-position\" : \"right-position\"}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: (_checklistGuideMetaDa16 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa16 === void 0 ? void 0 : (_checklistGuideMetaDa17 = _checklistGuideMetaDa16.canvas) === null || _checklistGuideMetaDa17 === void 0 ? void 0 : _checklistGuideMetaDa17.backgroundColor,\n            border: `${(_checklistGuideMetaDa18 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa18 === void 0 ? void 0 : (_checklistGuideMetaDa19 = _checklistGuideMetaDa18.canvas) === null || _checklistGuideMetaDa19 === void 0 ? void 0 : _checklistGuideMetaDa19.borderWidth}px solid ${(_checklistGuideMetaDa20 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa20 === void 0 ? void 0 : (_checklistGuideMetaDa21 = _checklistGuideMetaDa20.canvas) === null || _checklistGuideMetaDa21 === void 0 ? void 0 : _checklistGuideMetaDa21.borderColor}`,\n            borderRadius: `${(_checklistGuideMetaDa22 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa22 === void 0 ? void 0 : (_checklistGuideMetaDa23 = _checklistGuideMetaDa22.canvas) === null || _checklistGuideMetaDa23 === void 0 ? void 0 : _checklistGuideMetaDa23.cornerRadius}px`,\n            width: isRightPanelVisible ? `${((_checklistGuideMetaDa24 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa24 === void 0 ? void 0 : (_checklistGuideMetaDa25 = _checklistGuideMetaDa24.canvas) === null || _checklistGuideMetaDa25 === void 0 ? void 0 : _checklistGuideMetaDa25.width) || 930}px` : \"350px\",\n            height: `${((_checklistGuideMetaDa26 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa26 === void 0 ? void 0 : (_checklistGuideMetaDa27 = _checklistGuideMetaDa26.canvas) === null || _checklistGuideMetaDa27 === void 0 ? void 0 : _checklistGuideMetaDa27.height) || 500}px`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              height: \"100%\",\n              width: \"100%\",\n              overflow: \"auto hidden\"\n            },\n            className: \"qadpt-chkcontent\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: isRightPanelVisible ? \"40%\" : \"100%\",\n                borderRight: \"1px solid #e5e7eb\",\n                textAlign: isRTL ? \"right\" : \"left\"\n              },\n              className: \"qadpt-chkrgt\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  gap: \"16px\",\n                  borderBottom: \"1px solid #E8E8E8\",\n                  padding: \"24px 24px 16px 24px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"6px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"space-between\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"20px\",\n                        fontWeight: (_checklistGuideMetaDa28 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa28 !== void 0 && (_checklistGuideMetaDa29 = _checklistGuideMetaDa28.TitleSubTitle) !== null && _checklistGuideMetaDa29 !== void 0 && _checklistGuideMetaDa29.titleBold ? \"bold\" : \"normal\",\n                        fontStyle: (_checklistGuideMetaDa30 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa30 !== void 0 && (_checklistGuideMetaDa31 = _checklistGuideMetaDa30.TitleSubTitle) !== null && _checklistGuideMetaDa31 !== void 0 && _checklistGuideMetaDa31.titleItalic ? \"italic\" : \"normal\",\n                        color: ((_checklistGuideMetaDa32 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa32 === void 0 ? void 0 : (_checklistGuideMetaDa33 = _checklistGuideMetaDa32.TitleSubTitle) === null || _checklistGuideMetaDa33 === void 0 ? void 0 : _checklistGuideMetaDa33.titleColor) || \"#333\",\n                        display: \"block\",\n                        textOverflow: \"ellipsis\",\n                        whiteSpace: \"nowrap\",\n                        wordBreak: \"break-word\",\n                        overflow: \"hidden\"\n                      },\n                      children: translate(((_checklistGuideMetaDa34 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa34 === void 0 ? void 0 : (_checklistGuideMetaDa35 = _checklistGuideMetaDa34.TitleSubTitle) === null || _checklistGuideMetaDa35 === void 0 ? void 0 : _checklistGuideMetaDa35.title) || \"Checklist Title\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 14\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: !isRightPanelVisible && /*#__PURE__*/_jsxDEV(\"span\", {\n                        dangerouslySetInnerHTML: {\n                          __html: closepluginicon\n                        },\n                        onClick: handleClose,\n                        style: {\n                          background: \"#e8e8e8\",\n                          borderRadius: \"50%\",\n                          padding: \"8px\",\n                          display: \"flex\",\n                          cursor: \"pointer\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 16\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 14\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: \"14px\",\n                      fontWeight: (_checklistGuideMetaDa36 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa36 !== void 0 && (_checklistGuideMetaDa37 = _checklistGuideMetaDa36.TitleSubTitle) !== null && _checklistGuideMetaDa37 !== void 0 && _checklistGuideMetaDa37.subTitleBold ? \"bold\" : \"normal\",\n                      fontStyle: (_checklistGuideMetaDa38 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa38 !== void 0 && (_checklistGuideMetaDa39 = _checklistGuideMetaDa38.TitleSubTitle) !== null && _checklistGuideMetaDa39 !== void 0 && _checklistGuideMetaDa39.subTitleItalic ? \"italic\" : \"normal\",\n                      color: ((_checklistGuideMetaDa40 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa40 === void 0 ? void 0 : (_checklistGuideMetaDa41 = _checklistGuideMetaDa40.TitleSubTitle) === null || _checklistGuideMetaDa41 === void 0 ? void 0 : _checklistGuideMetaDa41.subTitleColor) || \"#8D8D8D\"\n                    },\n                    className: \"qadpt-subtl\",\n                    children: translate(((_checklistGuideMetaDa42 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa42 === void 0 ? void 0 : (_checklistGuideMetaDa43 = _checklistGuideMetaDa42.TitleSubTitle) === null || _checklistGuideMetaDa43 === void 0 ? void 0 : _checklistGuideMetaDa43.subTitle) || \"Context about the tasks in the checklist below users should prioritize completing.\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"space-between\",\n                      marginBottom: \"8px\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"14px\",\n                        color: \"#6b7280\"\n                      },\n                      children: [progress, \"/\", totalItems]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 14\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: \"8px\",\n                      backgroundColor: \"#e5e7eb\",\n                      borderRadius: \"9999px\",\n                      overflow: \"hidden\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: \"100%\",\n                        backgroundColor: (_checklistGuideMetaDa44 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa44 === void 0 ? void 0 : (_checklistGuideMetaDa45 = _checklistGuideMetaDa44.canvas) === null || _checklistGuideMetaDa45 === void 0 ? void 0 : _checklistGuideMetaDa45.primaryColor,\n                        borderRadius: \"9999px\",\n                        width: `${progress / totalItems * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 14\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: `calc(${((_checklistGuideMetaDa46 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa46 === void 0 ? void 0 : (_checklistGuideMetaDa47 = _checklistGuideMetaDa46.canvas) === null || _checklistGuideMetaDa47 === void 0 ? void 0 : _checklistGuideMetaDa47.height) || 500}px - 190px)`,\n                  overflow: \"auto\"\n                },\n                className: \"qadpt-chklist\",\n                children: checkpointslistData === null || checkpointslistData === void 0 ? void 0 : checkpointslistData.map(item => {\n                  var _checklistGuideMetaDa48, _checklistGuideMetaDa49, _checklistGuideMetaDa50, _checklistGuideMetaDa51, _checklistGuideMetaDa52, _checklistGuideMetaDa53;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${activeItem === item.id ? \"qadpt-chkstp\" : \"\"}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        padding: \"10px 16px 10px 10px\",\n                        cursor: \"pointer\",\n                        // borderLeft: activeItem === item.id ? `4px solid ${checklistGuideMetaData[0]?.canvas?.primaryColor}` : '4px solid transparent', // Straight left border highlight\n                        borderBottom: \"1px solid #E8E8E8\"\n                      },\n                      onClick: () => handleSelect(item.id),\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          paddingLeft: \"10px\",\n                          display: \"flex\",\n                          gap: \"6px\",\n                          flexDirection: \"column\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            width: \"100%\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: \"flex\",\n                              alignItems: \"center\",\n                              gap: \"10px\",\n                              flexDirection: \"row\",\n                              width: \"calc(100% - 60px)\"\n                            },\n                            children: [item.icon && typeof item.icon === \"string\" ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: modifySVGColor(item.icon, ((_checklistGuideMetaDa48 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa48 === void 0 ? void 0 : (_checklistGuideMetaDa49 = _checklistGuideMetaDa48.checkpoints) === null || _checklistGuideMetaDa49 === void 0 ? void 0 : _checklistGuideMetaDa49.checkpointsIcons) || \"#333\"),\n                              alt: \"icon\",\n                              style: {\n                                width: \"20px\",\n                                height: \"20px\"\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 400,\n                              columnNumber: 19\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                width: \"20px\",\n                                height: \"20px\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\"\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  width: \"16px\",\n                                  height: \"16px\"\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 415,\n                                columnNumber: 20\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 406,\n                              columnNumber: 19\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: ((_checklistGuideMetaDa50 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa50 === void 0 ? void 0 : (_checklistGuideMetaDa51 = _checklistGuideMetaDa50.checkpoints) === null || _checklistGuideMetaDa51 === void 0 ? void 0 : _checklistGuideMetaDa51.checkpointTitles) || \"#333\",\n                                overflow: \"hidden\",\n                                textOverflow: \"ellipsis\",\n                                whiteSpace: \"nowrap\",\n                                wordBreak: \"break-word\"\n                              },\n                              children: item.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 419,\n                              columnNumber: 18\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 390,\n                            columnNumber: 17\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(ChecklistCircle, {\n                              completed: completedStatus[item.id],\n                              onClick: () => {},\n                              size: \"sm\"\n                            }, item.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 432,\n                              columnNumber: 18\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 431,\n                            columnNumber: 17\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 382,\n                          columnNumber: 16\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            style: {\n                              fontSize: \"14px\",\n                              color: (_checklistGuideMetaDa52 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa52 === void 0 ? void 0 : (_checklistGuideMetaDa53 = _checklistGuideMetaDa52.checkpoints) === null || _checklistGuideMetaDa53 === void 0 ? void 0 : _checklistGuideMetaDa53.checkpointsDescription\n                            },\n                            className: \"qadpt-chkpopdesc\",\n                            children: item.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 441,\n                            columnNumber: 17\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 440,\n                          columnNumber: 16\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 15\n                      }, this)\n                    }, item.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 14\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 13\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 10\n            }, this), isRightPanelVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: \"60%\",\n                padding: \"20px 20px 0 20px\"\n              },\n              className: \"qadpt-chklft\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  placeContent: \"end\",\n                  width: \"100%\",\n                  gap: \"6px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: maximize\n                  },\n                  style: {\n                    background: \"#e8e8e8\",\n                    borderRadius: \"50%\",\n                    padding: \"6px\",\n                    display: \"flex\",\n                    cursor: \"pointer\"\n                  },\n                  onClick: handleMaximize\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: closepluginicon\n                  },\n                  onClick: handleClose,\n                  style: {\n                    background: \"#e8e8e8\",\n                    borderRadius: \"50%\",\n                    padding: \"8px\",\n                    display: \"flex\",\n                    cursor: \"pointer\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 14\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  flexDirection: \"column\",\n                  gap: \"10px\",\n                  height: \"calc(100% - 90px)\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    overflow: \"hidden auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    flexDirection: \"column\",\n                    width: \"-webkit-fill-available\"\n                  },\n                  children: [(selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support = selectedItem.supportingMedia) === null || _selectedItem$support === void 0 ? void 0 : _selectedItem$support.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [selectedItem.supportingMedia.some(file => {\n                      var _file$Base;\n                      return file === null || file === void 0 ? void 0 : (_file$Base = file.Base64) === null || _file$Base === void 0 ? void 0 : _file$Base.startsWith(\"data:image\");\n                    }) && /*#__PURE__*/_jsxDEV(ImageCarousel, {\n                      selectedItem: selectedItem,\n                      activeItem: activeItem,\n                      images: selectedItem.supportingMedia.filter(file => {\n                        var _file$Base2;\n                        return file === null || file === void 0 ? void 0 : (_file$Base2 = file.Base64) === null || _file$Base2 === void 0 ? void 0 : _file$Base2.startsWith(\"data:image\");\n                      }).map(file => file.Base64),\n                      isMaximized: isMaximized\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 16\n                    }, this), selectedItem.supportingMedia.some(file => {\n                      var _file$Base3;\n                      return file === null || file === void 0 ? void 0 : (_file$Base3 = file.Base64) === null || _file$Base3 === void 0 ? void 0 : _file$Base3.startsWith(\"data:video\");\n                    }) && selectedItem.supportingMedia.filter(file => {\n                      var _file$Base4;\n                      return file === null || file === void 0 ? void 0 : (_file$Base4 = file.Base64) === null || _file$Base4 === void 0 ? void 0 : _file$Base4.startsWith(\"data:video\");\n                    }).map((file, index) => /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                      videoFile: file.Base64,\n                      isMaximized: isMaximized\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 18\n                    }, this))]\n                  }, void 0, true), ((selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support2 = selectedItem.supportingMedia) === null || _selectedItem$support2 === void 0 ? void 0 : _selectedItem$support2.length) === 0 || !(selectedItem !== null && selectedItem !== void 0 && selectedItem.supportingMedia)) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"auto\",\n                      height: \"244px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: chkdefault\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 15\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        color: \"#8D8D8D\"\n                      },\n                      children: translate(\"Check tasks, stay organized, and finish strong!\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 15\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 14\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"100%\",\n                      marginTop: \"10px\"\n                    },\n                    className: \"qadpt-chkdesc\",\n                    children: selectedItem && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: \"100%\",\n                        display: \"flex\",\n                        flexDirection: \"column\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          textAlign: isRTL ? \"right\" : \"left\",\n                          display: \"flex\",\n                          flexDirection: \"column\",\n                          gap: \"12px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: \"16px\",\n                            fontWeight: 600,\n                            color: \"#333\",\n                            overflow: \"hidden\",\n                            textOverflow: \"ellipsis\",\n                            whiteSpace: \"nowrap\",\n                            wordBreak: \"break-word\"\n                          },\n                          children: selectedItem.mediaTitle\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 563,\n                          columnNumber: 17\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-desc\",\n                          style: {\n                            color: \"#8D8D8D\"\n                          },\n                          children: selectedItem.mediaDescription\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 577,\n                          columnNumber: 17\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 555,\n                        columnNumber: 16\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 15\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 14\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  gap: \"12px\",\n                  alignItems: \"center\",\n                  placeContent: \"end\",\n                  paddingBottom: \"20px\"\n                },\n                className: \"qadpt-btnsec\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    backgroundColor: (_checklistGuideMetaDa54 = checklistGuideMetaData[0].canvas) === null || _checklistGuideMetaDa54 === void 0 ? void 0 : _checklistGuideMetaDa54.primaryColor,\n                    borderRadius: \"10px\",\n                    padding: \"9px 16px\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    cursor: isPublished ? \"pointer\" : \"not-allowed\"\n                  },\n                  disabled: !isPublished,\n                  children: isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 13\n                }, this), (selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support3 = selectedItem.supportingMedia) === null || _selectedItem$support3 === void 0 ? void 0 : _selectedItem$support3.length) > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    borderRadius: \"10px\",\n                    padding: \"9px 16px\",\n                    color: (_checklistGuideMetaDa55 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa55 === void 0 ? void 0 : (_checklistGuideMetaDa56 = _checklistGuideMetaDa55.canvas) === null || _checklistGuideMetaDa56 === void 0 ? void 0 : _checklistGuideMetaDa56.primaryColor,\n                    border: \"none\",\n                    background: \"#D3D9DA\",\n                    cursor: \"pointer\"\n                  },\n                  onClick: e => handleMarkAsCompleted(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.id),\n                  children: translate(\"Mark as Completed\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 14\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 6\n    }, this), isMaximized && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        zIndex: 99999\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"fixed\",\n          inset: 0,\n          display: \"flex\",\n          alignItems: \"center\",\n          // justifyContent: 'center',\n          zIndex: 50\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            boxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\n            zIndex: 9,\n            marginTop: \"8%\",\n            marginBottom: \"5%\",\n            // marginLeft: 'auto',\n            //       marginRight: '100px',\n            display: \"flex\",\n            alignItems: \"center\",\n            placeContent: \"center\",\n            width: \"100%\"\n          },\n          className: \"qadpt-chkpopup\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: (_checklistGuideMetaDa57 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa57 === void 0 ? void 0 : (_checklistGuideMetaDa58 = _checklistGuideMetaDa57.canvas) === null || _checklistGuideMetaDa58 === void 0 ? void 0 : _checklistGuideMetaDa58.backgroundColor,\n              border: `${(_checklistGuideMetaDa59 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa59 === void 0 ? void 0 : (_checklistGuideMetaDa60 = _checklistGuideMetaDa59.canvas) === null || _checklistGuideMetaDa60 === void 0 ? void 0 : _checklistGuideMetaDa60.borderWidth}px solid ${(_checklistGuideMetaDa61 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa61 === void 0 ? void 0 : (_checklistGuideMetaDa62 = _checklistGuideMetaDa61.canvas) === null || _checklistGuideMetaDa62 === void 0 ? void 0 : _checklistGuideMetaDa62.borderColor}`,\n              borderRadius: `${(_checklistGuideMetaDa63 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa63 === void 0 ? void 0 : (_checklistGuideMetaDa64 = _checklistGuideMetaDa63.canvas) === null || _checklistGuideMetaDa64 === void 0 ? void 0 : _checklistGuideMetaDa64.cornerRadius}px`,\n              width: \"calc(-250px + 100vw)\",\n              height: \"calc(100vh - 140px)\",\n              overflow: \"auto\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                height: \"100%\",\n                width: \"100%\"\n              },\n              className: \"qadpt-chkcontent\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: \"100%\",\n                  padding: \"20px 20px 0 20px\"\n                },\n                className: \"qadpt-chklft\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    flexDirection: \"column\",\n                    gap: \"10px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      placeContent: \"end\",\n                      width: \"100%\",\n                      gap: \"6px\"\n                    },\n                    onClick: handleMinimize,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: closepluginicon\n                      },\n                      style: {\n                        background: \"#e8e8e8\",\n                        borderRadius: \"50%\",\n                        padding: \"8px\",\n                        display: \"flex\",\n                        cursor: \"pointer\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 14\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 13\n                  }, this), (selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support4 = selectedItem.supportingMedia) === null || _selectedItem$support4 === void 0 ? void 0 : _selectedItem$support4.length) === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"auto\",\n                      height: \"244px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: chkdefault\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 15\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        color: \"#8D8D8D\"\n                      },\n                      children: translate(\"Check tasks, stay organized, and finish strong!\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 15\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 14\n                  }, this), (selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support5 = selectedItem.supportingMedia) === null || _selectedItem$support5 === void 0 ? void 0 : _selectedItem$support5.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [selectedItem.supportingMedia.some(file => {\n                      var _file$Base5;\n                      return file === null || file === void 0 ? void 0 : (_file$Base5 = file.Base64) === null || _file$Base5 === void 0 ? void 0 : _file$Base5.startsWith(\"data:image\");\n                    }) && /*#__PURE__*/_jsxDEV(ImageCarousel, {\n                      selectedItem: selectedItem,\n                      activeItem: activeItem,\n                      images: selectedItem.supportingMedia.filter(file => {\n                        var _file$Base6;\n                        return file === null || file === void 0 ? void 0 : (_file$Base6 = file.Base64) === null || _file$Base6 === void 0 ? void 0 : _file$Base6.startsWith(\"data:image\");\n                      }).map(file => file.Base64),\n                      isMaximized: isMaximized\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 16\n                    }, this), selectedItem.supportingMedia.some(file => {\n                      var _file$Base7;\n                      return file === null || file === void 0 ? void 0 : (_file$Base7 = file.Base64) === null || _file$Base7 === void 0 ? void 0 : _file$Base7.startsWith(\"data:video\");\n                    }) && selectedItem.supportingMedia.filter(file => {\n                      var _file$base;\n                      return file === null || file === void 0 ? void 0 : (_file$base = file.base64) === null || _file$base === void 0 ? void 0 : _file$base.startsWith(\"data:video\");\n                    }).map((file, index) => /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                      videoFile: file.base64,\n                      isMaximized: isMaximized\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 18\n                    }, this))]\n                  }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"100%\",\n                      marginTop: \"10px\"\n                    },\n                    className: \"qadpt-chkdesc\",\n                    children: selectedItem && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: \"100%\",\n                        display: \"flex\",\n                        flexDirection: \"column\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          textAlign: isRTL ? \"right\" : \"left\",\n                          display: \"flex\",\n                          flexDirection: \"column\",\n                          gap: \"12px\",\n                          width: \"100%\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: \"16px\",\n                            fontWeight: 600,\n                            color: \"#333\",\n                            overflow: \"hidden\",\n                            textOverflow: \"ellipsis\",\n                            whiteSpace: \"nowrap\",\n                            wordBreak: \"break-word\"\n                          },\n                          children: selectedItem.mediaTitle\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 775,\n                          columnNumber: 17\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-desc\",\n                          children: selectedItem.mediaDescription\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 17\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 766,\n                        columnNumber: 16\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: \"flex\",\n                          gap: \"12px\",\n                          alignItems: \"center\",\n                          placeContent: \"end\",\n                          paddingBottom: \"20px\"\n                        },\n                        className: \"qadpt-btnsec\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          style: {\n                            backgroundColor: (_checklistGuideMetaDa65 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa65 === void 0 ? void 0 : (_checklistGuideMetaDa66 = _checklistGuideMetaDa65.canvas) === null || _checklistGuideMetaDa66 === void 0 ? void 0 : _checklistGuideMetaDa66.primaryColor,\n                            borderRadius: \"10px\",\n                            padding: \"9px 16px\",\n                            color: \"#fff\",\n                            border: \"none\",\n                            cursor: isPublished ? \"pointer\" : \"not-allowed\"\n                          },\n                          onClick: handleNavigate,\n                          disabled: !isPublished,\n                          children: isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 802,\n                          columnNumber: 17\n                        }, this), (selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support6 = selectedItem.supportingMedia) === null || _selectedItem$support6 === void 0 ? void 0 : _selectedItem$support6.length) > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                          style: {\n                            borderRadius: \"10px\",\n                            padding: \"9px 16px\",\n                            color: (_checklistGuideMetaDa67 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa67 === void 0 ? void 0 : (_checklistGuideMetaDa68 = _checklistGuideMetaDa67.canvas) === null || _checklistGuideMetaDa68 === void 0 ? void 0 : _checklistGuideMetaDa68.primaryColor,\n                            border: \"none\",\n                            background: \"#D3D9DA\",\n                            cursor: \"pointer\"\n                          },\n                          onClick: e => handleMarkAsCompleted(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.id),\n                          children: translate(\"Mark as Completed\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 817,\n                          columnNumber: 14\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 792,\n                        columnNumber: 16\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 765,\n                      columnNumber: 15\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 8\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 6\n    }, this)]\n  }, void 0, true);\n};\n_s(ChecklistPreview, \"xG3w2ajBA78qrIWHLfmQWu67ZrI=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ChecklistPreview;\nexport default ChecklistPreview;\nvar _c;\n$RefreshReg$(_c, \"ChecklistPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ChecklistCircle", "useDrawerStore", "ImageCarousel", "VideoPlayer", "chkdefault", "closepluginicon", "maximize", "GetGudeDetailsByGuideId", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "btoa", "error", "console", "ChecklistPreview", "isOpen", "onClose", "onRemainingCountUpdate", "data", "guideDetails", "isRightPanelVisible", "setIsRightPanelVisible", "_s", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checkpointslistData$", "_checklistGuideMetaDa4", "_checklistGuideMetaDa5", "_checklistGuideMetaDa9", "_checklistGuideMetaDa10", "_checklistGuideMetaDa11", "_checklistGuideMetaDa12", "_checklistGuideMetaDa13", "_checklistGuideMetaDa14", "_checklistGuideMetaDa15", "_checklistGuideMetaDa16", "_checklistGuideMetaDa17", "_checklistGuideMetaDa18", "_checklistGuideMetaDa19", "_checklistGuideMetaDa20", "_checklistGuideMetaDa21", "_checklistGuideMetaDa22", "_checklistGuideMetaDa23", "_checklistGuideMetaDa24", "_checklistGuideMetaDa25", "_checklistGuideMetaDa26", "_checklistGuideMetaDa27", "_checklistGuideMetaDa28", "_checklistGuideMetaDa29", "_checklistGuideMetaDa30", "_checklistGuideMetaDa31", "_checklistGuideMetaDa32", "_checklistGuideMetaDa33", "_checklistGuideMetaDa34", "_checklistGuideMetaDa35", "_checklistGuideMetaDa36", "_checklistGuideMetaDa37", "_checklistGuideMetaDa38", "_checklistGuideMetaDa39", "_checklistGuideMetaDa40", "_checklistGuideMetaDa41", "_checklistGuideMetaDa42", "_checklistGuideMetaDa43", "_checklistGuideMetaDa44", "_checklistGuideMetaDa45", "_checklistGuideMetaDa46", "_checklistGuideMetaDa47", "_selectedItem$support", "_selectedItem$support2", "_checklistGuideMetaDa54", "_selectedItem$support3", "_checklistGuideMetaDa55", "_checklistGuideMetaDa56", "_checklistGuideMetaDa57", "_checklistGuideMetaDa58", "_checklistGuideMetaDa59", "_checklistGuideMetaDa60", "_checklistGuideMetaDa61", "_checklistGuideMetaDa62", "_checklistGuideMetaDa63", "_checklistGuideMetaDa64", "_selectedItem$support4", "_selectedItem$support5", "_checklistGuideMetaDa65", "_checklistGuideMetaDa66", "_selectedItem$support6", "_checklistGuideMetaDa67", "_checklistGuideMetaDa68", "t", "translate", "checklistGuideMetaData", "createWithAI", "interactionData", "state", "isMaximized", "setIsMaximized", "completedStatus", "setCompletedStatus", "checkpointslistData", "checkpoints", "checkpointsList", "map", "checkpoint", "index", "completed", "checklistItems", "setChecklistItems", "activeItem", "setActiveItem", "id", "Object", "keys", "length", "initialCompletedStatus", "for<PERSON>ach", "item", "checklistColor", "canvas", "primaryColor", "selectedItem", "find", "document", "documentElement", "style", "setProperty", "isPublished", "setIsPublished", "fetchGuideDetails", "_res$GuideDetails", "_res$GuideDetails2", "res", "GuideDetails", "GuideStatus", "_checkpointslistData$2", "_checklistGuideMetaDa6", "_checklistGuideMetaDa7", "_checklistGuideMetaDa8", "checkpointList", "checkpointlist", "formattedChecklist", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "totalItems", "progress", "values", "filter", "status", "toggleItemCompletion", "prevStatus", "handleMarkAsCompleted", "handleSelect", "handleClose", "handleMinimize", "handleNavigate", "handleMaximize", "xOffset", "parseInt", "launcher", "launcherposition", "xaxisOffset", "xOffsetWithUnit", "isRTL", "getAttribute", "body", "children", "position", "inset", "display", "alignItems", "zIndex", "backgroundColor", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "boxShadow", "marginTop", "marginBottom", "yaxisOffset", "className", "left", "border", "borderWidth", "borderColor", "borderRadius", "cornerRadius", "width", "height", "overflow", "borderRight", "textAlign", "flexDirection", "gap", "borderBottom", "padding", "justifyContent", "fontSize", "fontWeight", "TitleSubTitle", "titleBold", "fontStyle", "titleItalic", "titleColor", "textOverflow", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "__html", "background", "cursor", "subTitleBold", "subTitleItalic", "subTitleColor", "subTitle", "maxHeight", "_checklistGuideMetaDa48", "_checklistGuideMetaDa49", "_checklistGuideMetaDa50", "_checklistGuideMetaDa51", "_checklistGuideMetaDa52", "_checklistGuideMetaDa53", "paddingLeft", "src", "checkpointsIcons", "alt", "checkpointTitles", "size", "checkpointsDescription", "place<PERSON><PERSON>nt", "some", "file", "_file$Base", "Base64", "startsWith", "images", "_file$Base2", "_file$Base3", "_file$Base4", "videoFile", "paddingBottom", "disabled", "e", "top", "right", "bottom", "_file$Base5", "_file$Base6", "_file$Base7", "_file$base", "base64", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistPreview.tsx"], "sourcesContent": ["\r\n\r\n\r\n\r\nimport React, { useEffect, useMemo, useState } from 'react';\r\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport LauncherSettings from './LauncherSettings';\r\nimport ImageCarousel from \"./ImageCarousel\";\r\nimport VideoPlayer from \"./VideoPlayer\";\r\nimport {  chkdefault, closepluginicon, maximize } from '../../assets/icons/icons';\r\nimport { GetGudeDetailsByGuideId } from '../../services/GuideListServices';\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\n// Function to modify the color of an SVG icon\r\nconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\tif (!base64SVG) {\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\ttry {\r\n\t\t// Check if the string is a valid base64 SVG\r\n\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t}\r\n\r\n\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t} else if (hasColoredFill) {\r\n\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t} else {\r\n\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t}\r\n\r\n\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\treturn modifiedBase64;\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\treturn base64SVG; // Return the original if there's an error\r\n\t}\r\n};\r\ninterface CheckListPopupProps {\r\n    isOpen: any;\r\n    onClose: () => void;\r\n    onRemainingCountUpdate: (formattedCount: string) => void;\r\n    data: any;\r\n    guideDetails: any;\r\n    isRightPanelVisible: any;\r\n    setIsRightPanelVisible: any;\r\n  }\r\n  const ChecklistPreview: React.FC<CheckListPopupProps> = ({\r\n\t\tisOpen,\r\n\t\tonClose,\r\n\t\tonRemainingCountUpdate,\r\n\t\tdata,\r\n\t\tguideDetails,\r\n\t\tisRightPanelVisible,\r\n\t\tsetIsRightPanelVisible,\r\n\t}) => {\r\n\t  const { t: translate } = useTranslation();\r\n\t\tconst { checklistGuideMetaData, createWithAI, interactionData } = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst [isMaximized, setIsMaximized] = useState(false);\r\n\t\tconst [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});\r\n\r\n\t\tconst checkpointslistData =\r\n\t\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({\r\n\t\t\t\t...checkpoint,\r\n\t\t\t\tcompleted: index === 0 ? true : false,\r\n\t\t\t})) || [];\r\n\r\n\t\tconst [checklistItems, setChecklistItems] = useState(checkpointslistData);\r\n\t\tconst [activeItem, setActiveItem] = useState(checkpointslistData[0]?.id || \"\");\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (Object.keys(completedStatus).length === 0) {\r\n\t\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t\t\t\tcheckpointslistData.forEach((item: any, index: number) => {\r\n\t\t\t\t\tinitialCompletedStatus[item.id] = index === 0;\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\t\t\t}\r\n\t\t}, []);\r\n\t\tconst checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;\r\n\t\tconst selectedItem = checkpointslistData?.find((item: any) => item.id === activeItem);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tdocument.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\r\n\t\t}, [checklistColor]);\r\n\t\tconst [isPublished, setIsPublished] = useState(true);\r\n\t\tuseEffect(() => {\r\n\t\t\tconst fetchGuideDetails = async () => {\r\n\t\t\t\tif (!selectedItem?.id) return; // Ensure there's a valid ID\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await GetGudeDetailsByGuideId(selectedItem.id, createWithAI, interactionData);\r\n\t\t\t\t\tif (res?.GuideDetails?.GuideStatus === \"InActive\" || res?.GuideDetails?.GuideStatus === \"Draft\") {\r\n\t\t\t\t\t\tsetIsPublished(false);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tsetIsPublished(true);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {}\r\n\t\t\t};\r\n\r\n\t\t\tfetchGuideDetails();\r\n\t\t}, [selectedItem, activeItem]);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointslistData) setActiveItem(checkpointslistData[0]?.id);\r\n\t\t}, [checkpointslistData?.length == 1]);\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checklistGuideMetaData[0]?.length > 0) {\r\n\t\t\t\tconst checkpointList = checklistGuideMetaData[0]?.checkpoints?.checkpointlist || [];\r\n\r\n\t\t\t\tconst formattedChecklist = checkpointList.map((checkpoint: any, index: number) => ({\r\n\t\t\t\t\tid: checkpoint.id || index + 1,\r\n\t\t\t\t\ttitle: checkpoint.title || `Step ${index + 1}`,\r\n\t\t\t\t\tdescription: checkpoint.description || \"No description provided\",\r\n\t\t\t\t\tredirectURL: checkpoint.redirectURL || \"\",\r\n\t\t\t\t\ticon: checkpoint.icon || \"\",\r\n\t\t\t\t\tsupportingMedia: checkpoint.supportingMedia || \"\",\r\n\t\t\t\t\tmediaTitle: checkpoint.mediaTitle || \"\",\r\n\t\t\t\t\tmediaDescription: checkpoint.mediaDescription || \"\",\r\n\t\t\t\t}));\r\n\t\t\t\tsetChecklistItems(formattedChecklist);\r\n\t\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\t\t\t\tformattedChecklist.forEach((item: any) => {\r\n\t\t\t\t\tinitialCompletedStatus[item.id] = false;\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\t\t\t}\r\n\t\t}, [checklistGuideMetaData[0]]); // Update when checklistGuideMetaData changes\r\n\r\n\t\tconst totalItems = checkpointslistData.length || 1;\r\n\t\tconst progress = Object.values(completedStatus).filter((status) => status).length || 1;\r\n\r\n\t\t// We'll let the ChecklistPopup component update the count based on completed status\r\n\t\t// This useEffect is no longer needed as we're getting the count from ChecklistPopup\r\n\r\n\t\tconst toggleItemCompletion = (id: string) => {\r\n\t\t\tsetCompletedStatus((prevStatus) => ({\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: !prevStatus[id],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleMarkAsCompleted = (id: string) => {\r\n\t\t\tsetCompletedStatus((prevStatus) => ({\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: true,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleSelect = (id: any) => {\r\n\t\t\tsetActiveItem(id);\r\n\t\t\tsetIsRightPanelVisible(true);\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tif (isRightPanelVisible) {\r\n\t\t\t\tsetIsRightPanelVisible(false);\r\n\t\t\t} else {\r\n\t\t\t\tonClose();\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst handleMinimize = () => {\r\n\t\t\tsetIsMaximized(false);\r\n\t\t};\r\n\r\n\t\tif (!isOpen) return null;\r\n\r\n\t\tif (!isOpen) return null;\r\n\r\n\t\tconst handleNavigate = () => {\r\n\t\t\t// window.open(\"http://localhost:3000/\", '_blank');\r\n\t\t};\r\n\r\n\t\tconst handleMaximize = () => {\r\n\t\t\tsetIsMaximized(true);\r\n\t  };\r\n\t  const xOffset = parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\");\r\nconst xOffsetWithUnit = `${xOffset + 30}px`;\r\n\t  \t\tconst isRTL = \r\n  document.documentElement.getAttribute('dir') === 'rtl' ||\r\n  document.body.getAttribute('dir') === 'rtl';\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t{isOpen && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\t\t\tzIndex: '99999',\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.3)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t></div>\r\n\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\r\n\t\t\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t\t\t\tmarginTop: \"auto\",\r\n\t\t\t\t\t\t\t\t\"--x-offset\": xOffsetWithUnit,\r\n\t\t\t\t\t\t\t\tmarginBottom: `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.yaxisOffset || \"10\") + 70}px`,\r\n\t\t\t\t\t\t\t\t// marginLeft: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px` : \"auto\",\r\n\t\t\t\t\t\t\t\t// marginRight: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? \"auto\" : `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px`,\r\n\t\t\t\t\t\t\t} as any }\r\n\t\t\t\t\t\t\tclassName={`qadpt-prvchkpopup ${\r\n\t\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.launcherposition.left ? \"left-position\" : \"right-position\"\r\n\t\t\t\t\t\t\t  }`}\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\t\t\twidth: isRightPanelVisible ? `${checklistGuideMetaData[0]?.canvas?.width || 930}px` : \"350px\",\r\n\t\t\t\t\t\t\t\t\theight: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\toverflow: \"auto hidden\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: isRightPanelVisible ? \"40%\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #e5e7eb\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkrgt\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"24px 24px 16px 24px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", justifyContent: \"space-between\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(checklistGuideMetaData[0]?.TitleSubTitle?.title || \"Checklist Title\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{!isRightPanelVisible && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || \"#8D8D8D\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-subtl\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(checklistGuideMetaData[0]?.TitleSubTitle?.subTitle || \"Context about the tasks in the checklist below users should prioritize completing.\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ fontSize: \"14px\", color: \"#6b7280\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{progress}/{totalItems}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5e7eb\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: `${(progress / totalItems) * 100}%`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklist\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{checkpointslistData?.map((item: any) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className={`${activeItem === item.id ? \"qadpt-chkstp\" : \"\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 16px 10px 10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// borderLeft: activeItem === item.id ? `4px solid ${checklistGuideMetaData[0]?.canvas?.primaryColor}` : '4px solid transparent', // Straight left border highlight\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSelect(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ paddingLeft: \"10px\", display: \"flex\", gap: \"6px\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 60px)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.icon && typeof item.icon === \"string\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || \"#333\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ width: \"16px\", height: \"16px\" }}></span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.title}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={completedStatus[item.id]}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.description}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t\t\t{/* {activeItem && ( */}\r\n\t\t\t\t\t\t\t\t\t{isRightPanelVisible && (\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: maximize }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleMaximize}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"10px\",    height: \"calc(100% - 90px)\",\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{\r\n    \t\t\t\t\t\t\t\t\toverflow: \"hidden auto\",display: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",width:\"-webkit-fill-available\"}} >\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfile?.Base64?.startsWith(\"data:image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.Base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate(\"Check tasks, stay organized, and finish strong!\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"#8D8D8D\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaDescription}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0].canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: isPublished ? \"pointer\" : \"not-allowed\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={!isPublished}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Mark as Completed\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{isMaximized && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\t\t\t\tzIndex: 50,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"8%\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5%\",\r\n\t\t\t\t\t\t\t\t\t// marginLeft: 'auto',\r\n\t\t\t\t\t\t\t\t\t//       marginRight: '100px',\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopup\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"calc(-250px + 100vw)\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"calc(100vh - 140px)\",\r\n\t\t\t\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t\t\t\t{/* {activeItem && ( */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\", gap: \"10px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleMinimize}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length === 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate(\"Check tasks, stay organized, and finish strong!\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfile?.Base64?.startsWith(\"data:image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"qadpt-desc\">{selectedItem.mediaDescription}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: isPublished ? \"pointer\" : \"not-allowed\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleNavigate}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={!isPublished}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Mark as Completed\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\t\t\t</>\r\n\t\t);\r\n\t};\r\nexport default ChecklistPreview;"], "mappings": ";;AAIA,OAAOA,KAAK,IAAIC,SAAS,EAAWC,QAAQ,QAAQ,OAAO;AAC3D,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AAEpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAAUC,UAAU,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,0BAA0B;AACjF,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAO,8BAA8B;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAc,EAAEC,KAAU,KAAK;EACtD,IAAI,CAACD,SAAS,EAAE;IACf,OAAO,EAAE;EACV;EAEA,IAAI;IACH;IACA,IAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,EAAE;MACtD,OAAOF,SAAS,CAAC,CAAC;IACnB;IAEA,MAAMG,UAAU,GAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhD;IACA,MAAMC,SAAS,GAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC;IACjD,MAAMK,cAAc,GAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC;IAE/D,IAAIM,WAAW,GAAGN,UAAU;IAE5B,IAAIG,SAAS,IAAI,CAACC,cAAc,EAAE;MACjC;MACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,EAAE,WAAWT,KAAK,GAAG,CAAC;IAC1E,CAAC,MAAM,IAAIM,cAAc,EAAE;MAC1B;MACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,EAAE,SAAST,KAAK,GAAG,CAAC;IAC9E,CAAC,MAAM;MACN;MACAQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,EAAE,eAAeT,KAAK,GAAG,CAAC;MAClFQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,EAAE,cAAcT,KAAK,GAAG,CAAC;IACjF;IAEA,MAAMU,cAAc,GAAG,6BAA6BC,IAAI,CAACH,WAAW,CAAC,EAAE;IACvE,OAAOE,cAAc;EACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAOb,SAAS,CAAC,CAAC;EACnB;AACD,CAAC;AAUC,MAAMe,gBAA+C,GAAGA,CAAC;EACzDC,MAAM;EACNC,OAAO;EACPC,sBAAsB;EACtBC,IAAI;EACJC,YAAY;EACZC,mBAAmB;EACnBC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACJ,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjG,cAAc,CAAC,CAAC;EAC1C,MAAM;IAAEkG,sBAAsB;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAG3G,cAAc,CAAE4G,KAAU,IAAKA,KAAK,CAAC;EAEvG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAA6B,CAAC,CAAC,CAAC;EAEtF,MAAMmH,mBAAmB,GACxB,EAAA5E,qBAAA,GAAAoE,sBAAsB,CAAC,CAAC,CAAC,cAAApE,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B6E,WAAW,cAAA5E,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwC6E,eAAe,cAAA5E,sBAAA,uBAAvDA,sBAAA,CAAyD6E,GAAG,CAAC,CAACC,UAAe,EAAEC,KAAa,MAAM;IACjG,GAAGD,UAAU;IACbE,SAAS,EAAED,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG;EACjC,CAAC,CAAC,CAAC,KAAI,EAAE;EAEV,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAG3H,QAAQ,CAACmH,mBAAmB,CAAC;EACzE,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC,EAAA0C,qBAAA,GAAAyE,mBAAmB,CAAC,CAAC,CAAC,cAAAzE,qBAAA,uBAAtBA,qBAAA,CAAwBoF,EAAE,KAAI,EAAE,CAAC;EAE9E/H,SAAS,CAAC,MAAM;IACf,IAAIgI,MAAM,CAACC,IAAI,CAACf,eAAe,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;MAC9C,MAAMC,sBAAkD,GAAG,CAAC,CAAC;MAE7Df,mBAAmB,CAACgB,OAAO,CAAC,CAACC,IAAS,EAAEZ,KAAa,KAAK;QACzDU,sBAAsB,CAACE,IAAI,CAACN,EAAE,CAAC,GAAGN,KAAK,KAAK,CAAC;MAC9C,CAAC,CAAC;MAEFN,kBAAkB,CAACgB,sBAAsB,CAAC;IAC3C;EACD,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,cAAc,IAAA1F,sBAAA,GAAGgE,sBAAsB,CAAC,CAAC,CAAC,cAAAhE,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B2F,MAAM,cAAA1F,sBAAA,uBAAjCA,sBAAA,CAAmC2F,YAAY;EACtE,MAAMC,YAAY,GAAGrB,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEsB,IAAI,CAAEL,IAAS,IAAKA,IAAI,CAACN,EAAE,KAAKF,UAAU,CAAC;EAErF7H,SAAS,CAAC,MAAM;IACf2I,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,YAAY,EAAER,cAAc,CAAC;EACzE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;EACpDD,SAAS,CAAC,MAAM;IACf,MAAMiJ,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI,EAACR,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEV,EAAE,GAAE,OAAO,CAAC;;MAE/B,IAAI;QAAA,IAAAmB,iBAAA,EAAAC,kBAAA;QACH,MAAMC,GAAG,GAAG,MAAM3I,uBAAuB,CAACgI,YAAY,CAACV,EAAE,EAAElB,YAAY,EAAEC,eAAe,CAAC;QACzF,IAAI,CAAAsC,GAAG,aAAHA,GAAG,wBAAAF,iBAAA,GAAHE,GAAG,CAAEC,YAAY,cAAAH,iBAAA,uBAAjBA,iBAAA,CAAmBI,WAAW,MAAK,UAAU,IAAI,CAAAF,GAAG,aAAHA,GAAG,wBAAAD,kBAAA,GAAHC,GAAG,CAAEC,YAAY,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBG,WAAW,MAAK,OAAO,EAAE;UAChGN,cAAc,CAAC,KAAK,CAAC;QACtB,CAAC,MAAM;UACNA,cAAc,CAAC,IAAI,CAAC;QACrB;MACD,CAAC,CAAC,OAAOnH,KAAK,EAAE,CAAC;IAClB,CAAC;IAEDoH,iBAAiB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACR,YAAY,EAAEZ,UAAU,CAAC,CAAC;EAE9B7H,SAAS,CAAC,MAAM;IAAA,IAAAuJ,sBAAA;IACf,IAAInC,mBAAmB,EAAEU,aAAa,EAAAyB,sBAAA,GAACnC,mBAAmB,CAAC,CAAC,CAAC,cAAAmC,sBAAA,uBAAtBA,sBAAA,CAAwBxB,EAAE,CAAC;EACnE,CAAC,EAAE,CAAC,CAAAX,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEc,MAAM,KAAI,CAAC,CAAC,CAAC;EACtClI,SAAS,CAAC,MAAM;IAAA,IAAAwJ,sBAAA;IACf,IAAI,EAAAA,sBAAA,GAAA5C,sBAAsB,CAAC,CAAC,CAAC,cAAA4C,sBAAA,uBAAzBA,sBAAA,CAA2BtB,MAAM,IAAG,CAAC,EAAE;MAAA,IAAAuB,sBAAA,EAAAC,sBAAA;MAC1C,MAAMC,cAAc,GAAG,EAAAF,sBAAA,GAAA7C,sBAAsB,CAAC,CAAC,CAAC,cAAA6C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BpC,WAAW,cAAAqC,sBAAA,uBAAtCA,sBAAA,CAAwCE,cAAc,KAAI,EAAE;MAEnF,MAAMC,kBAAkB,GAAGF,cAAc,CAACpC,GAAG,CAAC,CAACC,UAAe,EAAEC,KAAa,MAAM;QAClFM,EAAE,EAAEP,UAAU,CAACO,EAAE,IAAIN,KAAK,GAAG,CAAC;QAC9BqC,KAAK,EAAEtC,UAAU,CAACsC,KAAK,IAAI,QAAQrC,KAAK,GAAG,CAAC,EAAE;QAC9CsC,WAAW,EAAEvC,UAAU,CAACuC,WAAW,IAAI,yBAAyB;QAChEC,WAAW,EAAExC,UAAU,CAACwC,WAAW,IAAI,EAAE;QACzCC,IAAI,EAAEzC,UAAU,CAACyC,IAAI,IAAI,EAAE;QAC3BC,eAAe,EAAE1C,UAAU,CAAC0C,eAAe,IAAI,EAAE;QACjDC,UAAU,EAAE3C,UAAU,CAAC2C,UAAU,IAAI,EAAE;QACvCC,gBAAgB,EAAE5C,UAAU,CAAC4C,gBAAgB,IAAI;MAClD,CAAC,CAAC,CAAC;MACHxC,iBAAiB,CAACiC,kBAAkB,CAAC;MACrC,MAAM1B,sBAAkD,GAAG,CAAC,CAAC;MAC7D0B,kBAAkB,CAACzB,OAAO,CAAEC,IAAS,IAAK;QACzCF,sBAAsB,CAACE,IAAI,CAACN,EAAE,CAAC,GAAG,KAAK;MACxC,CAAC,CAAC;MAEFZ,kBAAkB,CAACgB,sBAAsB,CAAC;IAC3C;EACD,CAAC,EAAE,CAACvB,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC,MAAMyD,UAAU,GAAGjD,mBAAmB,CAACc,MAAM,IAAI,CAAC;EAClD,MAAMoC,QAAQ,GAAGtC,MAAM,CAACuC,MAAM,CAACrD,eAAe,CAAC,CAACsD,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAC,CAACvC,MAAM,IAAI,CAAC;;EAEtF;EACA;;EAEA,MAAMwC,oBAAoB,GAAI3C,EAAU,IAAK;IAC5CZ,kBAAkB,CAAEwD,UAAU,KAAM;MACnC,GAAGA,UAAU;MACb,CAAC5C,EAAE,GAAG,CAAC4C,UAAU,CAAC5C,EAAE;IACrB,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6C,qBAAqB,GAAI7C,EAAU,IAAK;IAC7CZ,kBAAkB,CAAEwD,UAAU,KAAM;MACnC,GAAGA,UAAU;MACb,CAAC5C,EAAE,GAAG;IACP,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8C,YAAY,GAAI9C,EAAO,IAAK;IACjCD,aAAa,CAACC,EAAE,CAAC;IACjBzF,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMwI,WAAW,GAAGA,CAAA,KAAM;IACzB,IAAIzI,mBAAmB,EAAE;MACxBC,sBAAsB,CAAC,KAAK,CAAC;IAC9B,CAAC,MAAM;MACNL,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EACD,MAAM8I,cAAc,GAAGA,CAAA,KAAM;IAC5B9D,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,IAAI,CAACjF,MAAM,EAAE,OAAO,IAAI;EAExB,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMgJ,cAAc,GAAGA,CAAA,KAAM;IAC5B;EAAA,CACA;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5BhE,cAAc,CAAC,IAAI,CAAC;EACpB,CAAC;EACD,MAAMiE,OAAO,GAAGC,QAAQ,CAAC,EAAArI,sBAAA,GAAA8D,sBAAsB,CAAC,CAAC,CAAC,cAAA9D,sBAAA,wBAAAC,uBAAA,GAAzBD,sBAAA,CAA2BsI,QAAQ,cAAArI,uBAAA,wBAAAC,uBAAA,GAAnCD,uBAAA,CAAqCsI,gBAAgB,cAAArI,uBAAA,uBAArDA,uBAAA,CAAuDsI,WAAW,KAAI,IAAI,CAAC;EACvG,MAAMC,eAAe,GAAG,GAAGL,OAAO,GAAG,EAAE,IAAI;EACtC,MAAMM,KAAK,GACd7C,QAAQ,CAACC,eAAe,CAAC6C,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK,IACtD9C,QAAQ,CAAC+C,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK;EAE3C,oBACC7K,OAAA,CAAAE,SAAA;IAAA6K,QAAA,GACE3J,MAAM,iBACNpB,OAAA;MACCiI,KAAK,EAAE;QACN+C,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpB;QACAC,MAAM,EAAE;MAAc,CAAE;MAAAL,QAAA,gBAEzB/K,OAAA;QACCiI,KAAK,EAAE;UACN+C,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,CAAC;UACRI,eAAe,EAAE;QAClB,CAAE;QACFC,OAAO,EAAEpB;MAAY;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEP1L,OAAA;QACCiI,KAAK,EAAE;UACN0D,SAAS,EAAE,+EAA+E;UAC1FP,MAAM,EAAE,CAAC;UACTQ,SAAS,EAAE,MAAM;UACjB,YAAY,EAAEjB,eAAe;UAC7BkB,YAAY,EAAE,GAAGtB,QAAQ,CAAC,EAAAlI,uBAAA,GAAA2D,sBAAsB,CAAC,CAAC,CAAC,cAAA3D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BmI,QAAQ,cAAAlI,uBAAA,wBAAAC,uBAAA,GAAnCD,uBAAA,CAAqCmI,gBAAgB,cAAAlI,uBAAA,uBAArDA,uBAAA,CAAuDuJ,WAAW,KAAI,IAAI,CAAC,GAAG,EAAE;UAC1G;UACA;QACD,CAAU;QACVC,SAAS,EAAE,qBACV,CAAAvJ,uBAAA,GAAAwD,sBAAsB,CAAC,CAAC,CAAC,cAAAxD,uBAAA,eAAzBA,uBAAA,CAA2BgI,QAAQ,CAACC,gBAAgB,CAACuB,IAAI,GAAG,eAAe,GAAG,gBAAgB,EAC1F;QAAAjB,QAAA,eACL/K,OAAA;UACCiI,KAAK,EAAE;YACNoD,eAAe,GAAA5I,uBAAA,GAAEuD,sBAAsB,CAAC,CAAC,CAAC,cAAAvD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BkF,MAAM,cAAAjF,uBAAA,uBAAjCA,uBAAA,CAAmC2I,eAAe;YACnEY,MAAM,EAAE,IAAAtJ,uBAAA,GAAGqD,sBAAsB,CAAC,CAAC,CAAC,cAAArD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BgF,MAAM,cAAA/E,uBAAA,uBAAjCA,uBAAA,CAAmCsJ,WAAW,aAAArJ,uBAAA,GAAYmD,sBAAsB,CAAC,CAAC,CAAC,cAAAnD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B8E,MAAM,cAAA7E,uBAAA,uBAAjCA,uBAAA,CAAmCqJ,WAAW,EAAE;YACrHC,YAAY,EAAE,IAAArJ,uBAAA,GAAGiD,sBAAsB,CAAC,CAAC,CAAC,cAAAjD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B4E,MAAM,cAAA3E,uBAAA,uBAAjCA,uBAAA,CAAmCqJ,YAAY,IAAI;YACpEC,KAAK,EAAE7K,mBAAmB,GAAG,GAAG,EAAAwB,uBAAA,GAAA+C,sBAAsB,CAAC,CAAC,CAAC,cAAA/C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B0E,MAAM,cAAAzE,uBAAA,uBAAjCA,uBAAA,CAAmCoJ,KAAK,KAAI,GAAG,IAAI,GAAG,OAAO;YAC7FC,MAAM,EAAE,GAAG,EAAApJ,uBAAA,GAAA6C,sBAAsB,CAAC,CAAC,CAAC,cAAA7C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BwE,MAAM,cAAAvE,uBAAA,uBAAjCA,uBAAA,CAAmCmJ,MAAM,KAAI,GAAG;UAC5D,CAAE;UAAAxB,QAAA,eAEF/K,OAAA;YACCiI,KAAK,EAAE;cACNiD,OAAO,EAAE,MAAM;cACfqB,MAAM,EAAE,MAAM;cACdD,KAAK,EAAE,MAAM;cACbE,QAAQ,EAAE;YACX,CAAE;YACFT,SAAS,EAAC,kBAAkB;YAAAhB,QAAA,gBAG5B/K,OAAA;cACCiI,KAAK,EAAE;gBACNqE,KAAK,EAAE7K,mBAAmB,GAAG,KAAK,GAAG,MAAM;gBAC3CgL,WAAW,EAAE,mBAAmB;gBAEhCC,SAAS,EAAE9B,KAAK,GAAG,OAAO,GAAG;cAC9B,CAAE;cACFmB,SAAS,EAAC,cAAc;cAAAhB,QAAA,gBAExB/K,OAAA;gBACCiI,KAAK,EAAE;kBACNiD,OAAO,EAAE,MAAM;kBACfyB,aAAa,EAAE,QAAQ;kBACvBC,GAAG,EAAE,MAAM;kBACXC,YAAY,EAAE,mBAAmB;kBACjCC,OAAO,EAAE;gBACV,CAAE;gBAAA/B,QAAA,gBAEF/K,OAAA;kBACCiI,KAAK,EAAE;oBACNiD,OAAO,EAAE,MAAM;oBACfyB,aAAa,EAAE,QAAQ;oBACvBC,GAAG,EAAE;kBACN,CAAE;kBAAA7B,QAAA,gBAEF/K,OAAA;oBAAKiI,KAAK,EAAE;sBAAEiD,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAE4B,cAAc,EAAE;oBAAgB,CAAE;oBAAAhC,QAAA,gBACtF/K,OAAA;sBACCiI,KAAK,EAAE;wBACN+E,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,CAAA5J,uBAAA,GAAA2C,sBAAsB,CAAC,CAAC,CAAC,cAAA3C,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6J,aAAa,cAAA5J,uBAAA,eAAxCA,uBAAA,CAA0C6J,SAAS,GAAG,MAAM,GAAG,QAAQ;wBACnFC,SAAS,EAAE,CAAA7J,uBAAA,GAAAyC,sBAAsB,CAAC,CAAC,CAAC,cAAAzC,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B2J,aAAa,cAAA1J,uBAAA,eAAxCA,uBAAA,CAA0C6J,WAAW,GAAG,QAAQ,GAAG,QAAQ;wBACtFhN,KAAK,EAAE,EAAAoD,uBAAA,GAAAuC,sBAAsB,CAAC,CAAC,CAAC,cAAAvC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2ByJ,aAAa,cAAAxJ,uBAAA,uBAAxCA,uBAAA,CAA0C4J,UAAU,KAAI,MAAM;wBACrEpC,OAAO,EAAE,OAAO;wBAChBqC,YAAY,EAAE,UAAU;wBACxBC,UAAU,EAAE,QAAQ;wBACpBC,SAAS,EAAE,YAAY;wBACvBjB,QAAQ,EAAE;sBACX,CAAE;sBAAAzB,QAAA,EAEDhF,SAAS,CAAC,EAAApC,uBAAA,GAAAqC,sBAAsB,CAAC,CAAC,CAAC,cAAArC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BuJ,aAAa,cAAAtJ,uBAAA,uBAAxCA,uBAAA,CAA0CsF,KAAK,KAAI,iBAAiB;oBAAC;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,eACN1L,OAAA;sBAAA+K,QAAA,EACE,CAACtJ,mBAAmB,iBACpBzB,OAAA;wBACC0N,uBAAuB,EAAE;0BAAEC,MAAM,EAAEhO;wBAAgB,CAAE;wBACrD2L,OAAO,EAAEpB,WAAY;wBACrBjC,KAAK,EAAE;0BACN2F,UAAU,EAAE,SAAS;0BACrBxB,YAAY,EAAE,KAAK;0BACnBU,OAAO,EAAE,KAAK;0BACd5B,OAAO,EAAE,MAAM;0BACf2C,MAAM,EAAE;wBACT;sBAAE;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBACD;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACN1L,OAAA;oBACCiI,KAAK,EAAE;sBACN+E,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,CAAApJ,uBAAA,GAAAmC,sBAAsB,CAAC,CAAC,CAAC,cAAAnC,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqJ,aAAa,cAAApJ,uBAAA,eAAxCA,uBAAA,CAA0CgK,YAAY,GAAG,MAAM,GAAG,QAAQ;sBACtFV,SAAS,EAAE,CAAArJ,uBAAA,GAAAiC,sBAAsB,CAAC,CAAC,CAAC,cAAAjC,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BmJ,aAAa,cAAAlJ,uBAAA,eAAxCA,uBAAA,CAA0C+J,cAAc,GAAG,QAAQ,GAAG,QAAQ;sBACzF1N,KAAK,EAAE,EAAA4D,uBAAA,GAAA+B,sBAAsB,CAAC,CAAC,CAAC,cAAA/B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BiJ,aAAa,cAAAhJ,uBAAA,uBAAxCA,uBAAA,CAA0C8J,aAAa,KAAI;oBACnE,CAAE;oBACFjC,SAAS,EAAC,aAAa;oBAAAhB,QAAA,EAEtBhF,SAAS,CAAC,EAAA5B,uBAAA,GAAA6B,sBAAsB,CAAC,CAAC,CAAC,cAAA7B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B+I,aAAa,cAAA9I,uBAAA,uBAAxCA,uBAAA,CAA0C6J,QAAQ,KAAI,oFAAoF;kBAAC;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEN1L,OAAA;kBAAA+K,QAAA,gBACC/K,OAAA;oBACCiI,KAAK,EAAE;sBACNiD,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB4B,cAAc,EAAE,eAAe;sBAC/BlB,YAAY,EAAE;oBACf,CAAE;oBAAAd,QAAA,eAEF/K,OAAA;sBAAMiI,KAAK,EAAE;wBAAE+E,QAAQ,EAAE,MAAM;wBAAE3M,KAAK,EAAE;sBAAU,CAAE;sBAAA0K,QAAA,GAClDrB,QAAQ,EAAC,GAAC,EAACD,UAAU;oBAAA;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1L,OAAA;oBACCiI,KAAK,EAAE;sBACNsE,MAAM,EAAE,KAAK;sBACblB,eAAe,EAAE,SAAS;sBAC1Be,YAAY,EAAE,QAAQ;sBACtBI,QAAQ,EAAE;oBACX,CAAE;oBAAAzB,QAAA,eAEF/K,OAAA;sBACCiI,KAAK,EAAE;wBACNsE,MAAM,EAAE,MAAM;wBACdlB,eAAe,GAAAhH,uBAAA,GAAE2B,sBAAsB,CAAC,CAAC,CAAC,cAAA3B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BsD,MAAM,cAAArD,uBAAA,uBAAjCA,uBAAA,CAAmCsD,YAAY;wBAChEwE,YAAY,EAAE,QAAQ;wBACtBE,KAAK,EAAE,GAAI5C,QAAQ,GAAGD,UAAU,GAAI,GAAG;sBACxC;oBAAE;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEN1L,OAAA;gBACCiI,KAAK,EAAE;kBACLiG,SAAS,EAAE,QAAQ,EAAA3J,uBAAA,GAAAyB,sBAAsB,CAAC,CAAC,CAAC,cAAAzB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BoD,MAAM,cAAAnD,uBAAA,uBAAjCA,uBAAA,CAAmC+H,MAAM,KAAI,GAAG,aAAa;kBAChFC,QAAQ,EAAE;gBACV,CAAE;gBACJT,SAAS,EAAC,eAAe;gBAAAhB,QAAA,EAExBvE,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEG,GAAG,CAAEc,IAAS;kBAAA,IAAA0G,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBAAA,oBACnCxO,OAAA;oBAAK+L,SAAS,EAAE,GAAG9E,UAAU,KAAKQ,IAAI,CAACN,EAAE,GAAG,cAAc,GAAG,EAAE,EAAG;oBAAA4D,QAAA,eACjE/K,OAAA;sBAECiI,KAAK,EAAE;wBACNiD,OAAO,EAAE,MAAM;wBACfyB,aAAa,EAAE,QAAQ;wBACvBG,OAAO,EAAE,qBAAqB;wBAC9Be,MAAM,EAAE,SAAS;wBACjB;wBACAhB,YAAY,EAAE;sBACf,CAAE;sBACFvB,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAACxC,IAAI,CAACN,EAAE,CAAE;sBAAA4D,QAAA,eAGrC/K,OAAA;wBAAKiI,KAAK,EAAE;0BAAEwG,WAAW,EAAE,MAAM;0BAAEvD,OAAO,EAAE,MAAM;0BAAE0B,GAAG,EAAE,KAAK;0BAAED,aAAa,EAAE;wBAAS,CAAE;wBAAA5B,QAAA,gBACzF/K,OAAA;0BACCiI,KAAK,EAAE;4BACNiD,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB4B,cAAc,EAAE,eAAe;4BAC/BT,KAAK,EAAE;0BACR,CAAE;0BAAAvB,QAAA,gBAEF/K,OAAA;4BACCiI,KAAK,EAAE;8BACNiD,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByB,GAAG,EAAE,MAAM;8BACXD,aAAa,EAAE,KAAK;8BACpBL,KAAK,EAAE;4BACR,CAAE;4BAAAvB,QAAA,GAEDtD,IAAI,CAAC4B,IAAI,IAAI,OAAO5B,IAAI,CAAC4B,IAAI,KAAK,QAAQ,gBAC1CrJ,OAAA;8BACC0O,GAAG,EAAEvO,cAAc,CAACsH,IAAI,CAAC4B,IAAI,EAAE,EAAA8E,uBAAA,GAAAnI,sBAAsB,CAAC,CAAC,CAAC,cAAAmI,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B1H,WAAW,cAAA2H,uBAAA,uBAAtCA,uBAAA,CAAwCO,gBAAgB,KAAI,MAAM,CAAE;8BACnGC,GAAG,EAAC,MAAM;8BACV3G,KAAK,EAAE;gCAAEqE,KAAK,EAAE,MAAM;gCAAEC,MAAM,EAAE;8BAAO;4BAAE;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzC,CAAC,gBAEF1L,OAAA;8BACCiI,KAAK,EAAE;gCACNqE,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACdrB,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB4B,cAAc,EAAE;8BACjB,CAAE;8BAAAhC,QAAA,eAEF/K,OAAA;gCAAMiI,KAAK,EAAE;kCAAEqE,KAAK,EAAE,MAAM;kCAAEC,MAAM,EAAE;gCAAO;8BAAE;gCAAAhB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnD,CACL,eAED1L,OAAA;8BACCiI,KAAK,EAAE;gCACN5H,KAAK,EAAE,EAAAgO,uBAAA,GAAArI,sBAAsB,CAAC,CAAC,CAAC,cAAAqI,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B5H,WAAW,cAAA6H,uBAAA,uBAAtCA,uBAAA,CAAwCO,gBAAgB,KAAI,MAAM;gCACzErC,QAAQ,EAAE,QAAQ;gCAClBe,YAAY,EAAE,UAAU;gCACxBC,UAAU,EAAE,QAAQ;gCACpBC,SAAS,EAAE;8BACZ,CAAE;8BAAA1C,QAAA,EAEDtD,IAAI,CAACyB;4BAAK;8BAAAqC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACN1L,OAAA;4BAAA+K,QAAA,eACC/K,OAAA,CAACV,eAAe;8BAEfwH,SAAS,EAAER,eAAe,CAACmB,IAAI,CAACN,EAAE,CAAE;8BACpCmE,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;8BAClBwD,IAAI,EAAC;4BAAI,GAHJrH,IAAI,CAACN,EAAE;8BAAAoE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAIZ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACN1L,OAAA;0BAAA+K,QAAA,eACC/K,OAAA;4BACCiI,KAAK,EAAE;8BACN+E,QAAQ,EAAE,MAAM;8BAChB3M,KAAK,GAAAkO,uBAAA,GAAEvI,sBAAsB,CAAC,CAAC,CAAC,cAAAuI,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B9H,WAAW,cAAA+H,uBAAA,uBAAtCA,uBAAA,CAAwCO;4BAChD,CAAE;4BACFhD,SAAS,EAAC,kBAAkB;4BAAAhB,QAAA,EAE3BtD,IAAI,CAAC0B;0BAAW;4BAAAoC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC,GAlFDjE,IAAI,CAACN,EAAE;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAmFR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAILjK,mBAAmB,iBACnBzB,OAAA;cACCiI,KAAK,EAAE;gBACNqE,KAAK,EAAE,KAAK;gBACZQ,OAAO,EAAE;cACV,CAAE;cACFf,SAAS,EAAC,cAAc;cAAAhB,QAAA,gBAExB/K,OAAA;gBACEiI,KAAK,EAAE;kBACNiD,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBAEpB6D,YAAY,EAAE,KAAK;kBACnB1C,KAAK,EAAE,MAAM;kBACbM,GAAG,EAAE;gBACN,CAAE;gBAAA7B,QAAA,gBAEF/K,OAAA;kBACC0N,uBAAuB,EAAE;oBAAEC,MAAM,EAAE/N;kBAAS,CAAE;kBAC9CqI,KAAK,EAAE;oBACN2F,UAAU,EAAE,SAAS;oBACrBxB,YAAY,EAAE,KAAK;oBACnBU,OAAO,EAAE,KAAK;oBACd5B,OAAO,EAAE,MAAM;oBACf2C,MAAM,EAAE;kBACT,CAAE;kBACFvC,OAAO,EAAEjB;gBAAe;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACF1L,OAAA;kBACC0N,uBAAuB,EAAE;oBAAEC,MAAM,EAAEhO;kBAAgB,CAAE;kBACrD2L,OAAO,EAAEpB,WAAY;kBACrBjC,KAAK,EAAE;oBACN2F,UAAU,EAAE,SAAS;oBACrBxB,YAAY,EAAE,KAAK;oBACnBU,OAAO,EAAE,KAAK;oBACd5B,OAAO,EAAE,MAAM;oBACf2C,MAAM,EAAE;kBACT;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACR1L,OAAA;gBACFiI,KAAK,EAAE;kBACNiD,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwB,aAAa,EAAE,QAAQ;kBACvBC,GAAG,EAAE,MAAM;kBAAKL,MAAM,EAAE;gBAEzB,CAAE;gBAAAxB,QAAA,eAGE/K,OAAA;kBAAKiI,KAAK,EAAE;oBACZuE,QAAQ,EAAE,aAAa;oBAACtB,OAAO,EAAE,MAAM;oBAC1CC,UAAU,EAAE,QAAQ;oBACpBwB,aAAa,EAAE,QAAQ;oBAACL,KAAK,EAAC;kBAAwB,CAAE;kBAAAvB,QAAA,GACrD,CAAAlD,YAAY,aAAZA,YAAY,wBAAApD,qBAAA,GAAZoD,YAAY,CAAEyB,eAAe,cAAA7E,qBAAA,uBAA7BA,qBAAA,CAA+B6C,MAAM,IAAG,CAAC,iBACzCtH,OAAA,CAAAE,SAAA;oBAAA6K,QAAA,GACElD,YAAY,CAACyB,eAAe,CAAC2F,IAAI,CAAEC,IAAS;sBAAA,IAAAC,UAAA;sBAAA,OAC5CD,IAAI,aAAJA,IAAI,wBAAAC,UAAA,GAAJD,IAAI,CAAEE,MAAM,cAAAD,UAAA,uBAAZA,UAAA,CAAcE,UAAU,CAAC,YAAY,CAAC;oBAAA,CACvC,CAAC,iBACArP,OAAA,CAACR,aAAa;sBACbqI,YAAY,EAAEA,YAAa;sBAC3BZ,UAAU,EAAEA,UAAW;sBACvBqI,MAAM,EAAEzH,YAAY,CAACyB,eAAe,CAClCM,MAAM,CAAEsF,IAAS;wBAAA,IAAAK,WAAA;wBAAA,OAAKL,IAAI,aAAJA,IAAI,wBAAAK,WAAA,GAAJL,IAAI,CAAEE,MAAM,cAAAG,WAAA,uBAAZA,WAAA,CAAcF,UAAU,CAAC,YAAY,CAAC;sBAAA,EAAC,CAC7D1I,GAAG,CAAEuI,IAAS,IAAKA,IAAI,CAACE,MAAM,CAAE;sBAClChJ,WAAW,EAAEA;oBAAY;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CACD,EAEA7D,YAAY,CAACyB,eAAe,CAAC2F,IAAI,CAAEC,IAAS;sBAAA,IAAAM,WAAA;sBAAA,OAAKN,IAAI,aAAJA,IAAI,wBAAAM,WAAA,GAAJN,IAAI,CAAEE,MAAM,cAAAI,WAAA,uBAAZA,WAAA,CAAcH,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,IACxFxH,YAAY,CAACyB,eAAe,CAC1BM,MAAM,CAAEsF,IAAS;sBAAA,IAAAO,WAAA;sBAAA,OAAKP,IAAI,aAAJA,IAAI,wBAAAO,WAAA,GAAJP,IAAI,CAAEE,MAAM,cAAAK,WAAA,uBAAZA,WAAA,CAAcJ,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,CAC7D1I,GAAG,CAAC,CAACuI,IAAS,EAAErI,KAAa,kBAC7B7G,OAAA,CAACP,WAAW;sBAEXiQ,SAAS,EAAER,IAAI,CAACE,MAAO;sBACvBhJ,WAAW,EAAEA;oBAAY,GAFpBS,KAAK;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACD,CAAC;kBAAA,eACH,CACF,EACA,CAAC,CAAA7D,YAAY,aAAZA,YAAY,wBAAAnD,sBAAA,GAAZmD,YAAY,CAAEyB,eAAe,cAAA5E,sBAAA,uBAA7BA,sBAAA,CAA+B4C,MAAM,MAAK,CAAC,IAAI,EAACO,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEyB,eAAe,mBAC9EtJ,OAAA;oBAAKiI,KAAK,EAAE;sBAAEqE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE;oBAAQ,CAAE;oBAAAxB,QAAA,gBAC9C/K,OAAA;sBAAM0N,uBAAuB,EAAE;wBAAEC,MAAM,EAAEjO;sBAAW;oBAAE;sBAAA6L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzD1L,OAAA;sBAAKiI,KAAK,EAAE;wBAAE5H,KAAK,EAAE;sBAAU,CAAE;sBAAA0K,QAAA,EAAEhF,SAAS,CAAC,iDAAiD;oBAAC;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CACL,eAED1L,OAAA;oBACCiI,KAAK,EAAE;sBAAEqE,KAAK,EAAE,MAAM;sBAAEV,SAAS,EAAE;oBAAO,CAAE;oBAC5CG,SAAS,EAAC,eAAe;oBAAAhB,QAAA,EAExBlD,YAAY,iBACZ7H,OAAA;sBAAKiI,KAAK,EAAE;wBAAEsE,MAAM,EAAE,MAAM;wBAAErB,OAAO,EAAE,MAAM;wBAAEyB,aAAa,EAAE;sBAAS,CAAE;sBAAA5B,QAAA,eACxE/K,OAAA;wBACCiI,KAAK,EAAE;0BACNyE,SAAS,EAAE9B,KAAK,GAAG,OAAO,GAAG,MAAM;0BACnCM,OAAO,EAAE,MAAM;0BACfyB,aAAa,EAAE,QAAQ;0BACvBC,GAAG,EAAE;wBACN,CAAE;wBAAA7B,QAAA,gBAEF/K,OAAA;0BACCiI,KAAK,EAAE;4BACN+E,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE,GAAG;4BACf5M,KAAK,EAAE,MAAM;4BACbmM,QAAQ,EAAE,QAAQ;4BAClBe,YAAY,EAAE,UAAU;4BACxBC,UAAU,EAAE,QAAQ;4BACpBC,SAAS,EAAE;0BACZ,CAAE;0BAAA1C,QAAA,EAEDlD,YAAY,CAAC0B;wBAAU;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAEN1L,OAAA;0BACC+L,SAAS,EAAC,YAAY;0BACtB9D,KAAK,EAAE;4BAAE5H,KAAK,EAAE;0BAAU,CAAE;0BAAA0K,QAAA,EAE3BlD,YAAY,CAAC2B;wBAAgB;0BAAA+B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACP1L,OAAA;gBACCiI,KAAK,EAAE;kBACNiD,OAAO,EAAE,MAAM;kBACf0B,GAAG,EAAE,MAAM;kBACXzB,UAAU,EAAE,QAAQ;kBACpB6D,YAAY,EAAE,KAAK;kBACnBW,aAAa,EAAE;gBAChB,CAAE;gBACF5D,SAAS,EAAC,cAAc;gBAAAhB,QAAA,gBAExB/K,OAAA;kBACCiI,KAAK,EAAE;oBACNoD,eAAe,GAAA1G,uBAAA,GAAEqB,sBAAsB,CAAC,CAAC,CAAC,CAAC2B,MAAM,cAAAhD,uBAAA,uBAAhCA,uBAAA,CAAkCiD,YAAY;oBAC/DwE,YAAY,EAAE,MAAM;oBACpBU,OAAO,EAAE,UAAU;oBACnBzM,KAAK,EAAE,MAAM;oBACb4L,MAAM,EAAE,MAAM;oBACd4B,MAAM,EAAE1F,WAAW,GAAG,SAAS,GAAG;kBACnC,CAAE;kBACFyH,QAAQ,EAAE,CAACzH,WAAY;kBAAA4C,QAAA,EAEtB5C,WAAW,GAAGpC,SAAS,CAAC,WAAW,CAAC,GAAGA,SAAS,CAAC,2BAA2B;gBAAC;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,EACR,CAAA7D,YAAY,aAAZA,YAAY,wBAAAjD,sBAAA,GAAZiD,YAAY,CAAEyB,eAAe,cAAA1E,sBAAA,uBAA7BA,sBAAA,CAA+B0C,MAAM,IAAG,CAAC,iBACzCtH,OAAA;kBACCiI,KAAK,EAAE;oBACNmE,YAAY,EAAE,MAAM;oBACpBU,OAAO,EAAE,UAAU;oBACnBzM,KAAK,GAAAwE,uBAAA,GAAEmB,sBAAsB,CAAC,CAAC,CAAC,cAAAnB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B8C,MAAM,cAAA7C,uBAAA,uBAAjCA,uBAAA,CAAmC8C,YAAY;oBACtDqE,MAAM,EAAE,MAAM;oBACd2B,UAAU,EAAE,SAAS;oBACrBC,MAAM,EAAE;kBACT,CAAE;kBACFvC,OAAO,EAAGuE,CAAM,IAAK7F,qBAAqB,CAACnC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEV,EAAE,CAAE;kBAAA4D,QAAA,EAE5DhF,SAAS,CAAC,mBAAmB;gBAAC;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAEP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACL,EAEAtF,WAAW,iBACXpG,OAAA;MACCiI,KAAK,EAAE;QACN+C,QAAQ,EAAE,OAAO;QACjB8E,GAAG,EAAE,CAAC;QACN9D,IAAI,EAAE,CAAC;QACP+D,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT3E,eAAe,EAAE,oBAAoB;QACrCD,MAAM,EAAE;MACT,CAAE;MAAAL,QAAA,eAEF/K,OAAA;QACCiI,KAAK,EAAE;UACN+C,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,CAAC;UACRC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpB;UACAC,MAAM,EAAE;QACT,CAAE;QAAAL,QAAA,eAEF/K,OAAA;UACCiI,KAAK,EAAE;YACN0D,SAAS,EAAE,+EAA+E;YAC1FP,MAAM,EAAE,CAAC;YACTQ,SAAS,EAAE,IAAI;YACfC,YAAY,EAAE,IAAI;YAClB;YACA;YACAX,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB6D,YAAY,EAAE,QAAQ;YACtB1C,KAAK,EAAE;UACR,CAAE;UACFP,SAAS,EAAC,gBAAgB;UAAAhB,QAAA,eAE1B/K,OAAA;YACCiI,KAAK,EAAE;cACNoD,eAAe,GAAAtG,uBAAA,GAAEiB,sBAAsB,CAAC,CAAC,CAAC,cAAAjB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B4C,MAAM,cAAA3C,uBAAA,uBAAjCA,uBAAA,CAAmCqG,eAAe;cACnEY,MAAM,EAAE,IAAAhH,uBAAA,GAAGe,sBAAsB,CAAC,CAAC,CAAC,cAAAf,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B0C,MAAM,cAAAzC,uBAAA,uBAAjCA,uBAAA,CAAmCgH,WAAW,aAAA/G,uBAAA,GAAYa,sBAAsB,CAAC,CAAC,CAAC,cAAAb,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BwC,MAAM,cAAAvC,uBAAA,uBAAjCA,uBAAA,CAAmC+G,WAAW,EAAE;cACrHC,YAAY,EAAE,IAAA/G,uBAAA,GAAGW,sBAAsB,CAAC,CAAC,CAAC,cAAAX,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BsC,MAAM,cAAArC,uBAAA,uBAAjCA,uBAAA,CAAmC+G,YAAY,IAAI;cACpEC,KAAK,EAAE,sBAAsB;cAC7BC,MAAM,EAAE,qBAAqB;cAC7BC,QAAQ,EAAE;YACX,CAAE;YAAAzB,QAAA,eAEF/K,OAAA;cACCiI,KAAK,EAAE;gBACNiD,OAAO,EAAE,MAAM;gBACfqB,MAAM,EAAE,MAAM;gBACdD,KAAK,EAAE;cACR,CAAE;cACFP,SAAS,EAAC,kBAAkB;cAAAhB,QAAA,eAO5B/K,OAAA;gBACCiI,KAAK,EAAE;kBACNqE,KAAK,EAAE,MAAM;kBACbQ,OAAO,EAAE;gBACV,CAAE;gBACFf,SAAS,EAAC,cAAc;gBAAAhB,QAAA,eAExB/K,OAAA;kBAAKiI,KAAK,EAAE;oBAAEiD,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEwB,aAAa,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAO,CAAE;kBAAA7B,QAAA,gBAC3F/K,OAAA;oBACCiI,KAAK,EAAE;sBACNiD,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB6D,YAAY,EAAE,KAAK;sBACnB1C,KAAK,EAAE,MAAM;sBACbM,GAAG,EAAE;oBACN,CAAE;oBACFtB,OAAO,EAAEnB,cAAe;oBAAAY,QAAA,eAExB/K,OAAA;sBACC0N,uBAAuB,EAAE;wBAAEC,MAAM,EAAEhO;sBAAgB,CAAE;sBACrDsI,KAAK,EAAE;wBACN2F,UAAU,EAAE,SAAS;wBACrBxB,YAAY,EAAE,KAAK;wBACnBU,OAAO,EAAE,KAAK;wBACd5B,OAAO,EAAE,MAAM;wBACf2C,MAAM,EAAE;sBACT;oBAAE;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EACL,CAAA7D,YAAY,aAAZA,YAAY,wBAAAtC,sBAAA,GAAZsC,YAAY,CAAEyB,eAAe,cAAA/D,sBAAA,uBAA7BA,sBAAA,CAA+B+B,MAAM,MAAK,CAAC,iBAC3CtH,OAAA;oBAAKiI,KAAK,EAAE;sBAAEqE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE;oBAAQ,CAAE;oBAAAxB,QAAA,gBAC9C/K,OAAA;sBAAM0N,uBAAuB,EAAE;wBAAEC,MAAM,EAAEjO;sBAAW;oBAAE;sBAAA6L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzD1L,OAAA;sBAAKiI,KAAK,EAAE;wBAAE5H,KAAK,EAAE;sBAAU,CAAE;sBAAA0K,QAAA,EAAEhF,SAAS,CAAC,iDAAiD;oBAAC;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CACL,EACA,CAAA7D,YAAY,aAAZA,YAAY,wBAAArC,sBAAA,GAAZqC,YAAY,CAAEyB,eAAe,cAAA9D,sBAAA,uBAA7BA,sBAAA,CAA+B8B,MAAM,IAAG,CAAC,iBACzCtH,OAAA,CAAAE,SAAA;oBAAA6K,QAAA,GACElD,YAAY,CAACyB,eAAe,CAAC2F,IAAI,CAAEC,IAAS;sBAAA,IAAAe,WAAA;sBAAA,OAC5Cf,IAAI,aAAJA,IAAI,wBAAAe,WAAA,GAAJf,IAAI,CAAEE,MAAM,cAAAa,WAAA,uBAAZA,WAAA,CAAcZ,UAAU,CAAC,YAAY,CAAC;oBAAA,CACvC,CAAC,iBACArP,OAAA,CAACR,aAAa;sBACbqI,YAAY,EAAEA,YAAa;sBAC3BZ,UAAU,EAAEA,UAAW;sBACvBqI,MAAM,EAAEzH,YAAY,CAACyB,eAAe,CAClCM,MAAM,CAAEsF,IAAS;wBAAA,IAAAgB,WAAA;wBAAA,OAAKhB,IAAI,aAAJA,IAAI,wBAAAgB,WAAA,GAAJhB,IAAI,CAAEE,MAAM,cAAAc,WAAA,uBAAZA,WAAA,CAAcb,UAAU,CAAC,YAAY,CAAC;sBAAA,EAAC,CAC7D1I,GAAG,CAAEuI,IAAS,IAAKA,IAAI,CAACE,MAAM,CAAE;sBAClChJ,WAAW,EAAEA;oBAAY;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CACD,EAEA7D,YAAY,CAACyB,eAAe,CAAC2F,IAAI,CAAEC,IAAS;sBAAA,IAAAiB,WAAA;sBAAA,OAAKjB,IAAI,aAAJA,IAAI,wBAAAiB,WAAA,GAAJjB,IAAI,CAAEE,MAAM,cAAAe,WAAA,uBAAZA,WAAA,CAAcd,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,IACxFxH,YAAY,CAACyB,eAAe,CAC1BM,MAAM,CAAEsF,IAAS;sBAAA,IAAAkB,UAAA;sBAAA,OAAKlB,IAAI,aAAJA,IAAI,wBAAAkB,UAAA,GAAJlB,IAAI,CAAEmB,MAAM,cAAAD,UAAA,uBAAZA,UAAA,CAAcf,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,CAC7D1I,GAAG,CAAC,CAACuI,IAAS,EAAErI,KAAa,kBAC7B7G,OAAA,CAACP,WAAW;sBAEXiQ,SAAS,EAAER,IAAI,CAACmB,MAAO;sBACvBjK,WAAW,EAAEA;oBAAY,GAFpBS,KAAK;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACD,CAAC;kBAAA,eACH,CACF,eACD1L,OAAA;oBACCiI,KAAK,EAAE;sBAAEqE,KAAK,EAAE,MAAM;sBAAEV,SAAS,EAAE;oBAAO,CAAE;oBAC5CG,SAAS,EAAC,eAAe;oBAAAhB,QAAA,EAExBlD,YAAY,iBACZ7H,OAAA;sBAAKiI,KAAK,EAAE;wBAAEsE,MAAM,EAAE,MAAM;wBAAErB,OAAO,EAAE,MAAM;wBAAEyB,aAAa,EAAE;sBAAS,CAAE;sBAAA5B,QAAA,gBACxE/K,OAAA;wBACCiI,KAAK,EAAE;0BACNyE,SAAS,EAAE9B,KAAK,GAAG,OAAO,GAAG,MAAM;0BACnCM,OAAO,EAAE,MAAM;0BACfyB,aAAa,EAAE,QAAQ;0BACvBC,GAAG,EAAE,MAAM;0BACXN,KAAK,EAAE;wBACR,CAAE;wBAAAvB,QAAA,gBAEF/K,OAAA;0BACCiI,KAAK,EAAE;4BACN+E,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE,GAAG;4BACf5M,KAAK,EAAE,MAAM;4BACbmM,QAAQ,EAAE,QAAQ;4BAClBe,YAAY,EAAE,UAAU;4BACxBC,UAAU,EAAE,QAAQ;4BACpBC,SAAS,EAAE;0BACZ,CAAE;0BAAA1C,QAAA,EAEDlD,YAAY,CAAC0B;wBAAU;0BAAAgC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAEN1L,OAAA;0BAAK+L,SAAS,EAAC,YAAY;0BAAAhB,QAAA,EAAElD,YAAY,CAAC2B;wBAAgB;0BAAA+B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7D,CAAC,eAEN1L,OAAA;wBACCiI,KAAK,EAAE;0BACNiD,OAAO,EAAE,MAAM;0BACf0B,GAAG,EAAE,MAAM;0BACXzB,UAAU,EAAE,QAAQ;0BACpB6D,YAAY,EAAE,KAAK;0BACnBW,aAAa,EAAE;wBAChB,CAAE;wBACF5D,SAAS,EAAC,cAAc;wBAAAhB,QAAA,gBAExB/K,OAAA;0BACCiI,KAAK,EAAE;4BACNoD,eAAe,GAAA5F,uBAAA,GAAEO,sBAAsB,CAAC,CAAC,CAAC,cAAAP,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BkC,MAAM,cAAAjC,uBAAA,uBAAjCA,uBAAA,CAAmCkC,YAAY;4BAChEwE,YAAY,EAAE,MAAM;4BACpBU,OAAO,EAAE,UAAU;4BACnBzM,KAAK,EAAE,MAAM;4BACb4L,MAAM,EAAE,MAAM;4BACd4B,MAAM,EAAE1F,WAAW,GAAG,SAAS,GAAG;0BACnC,CAAE;0BACFmD,OAAO,EAAElB,cAAe;0BACxBwF,QAAQ,EAAE,CAACzH,WAAY;0BAAA4C,QAAA,EAEtB5C,WAAW,GAAGpC,SAAS,CAAC,WAAW,CAAC,GAAGA,SAAS,CAAC,2BAA2B;wBAAC;0BAAAwF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvE,CAAC,EACR,CAAA7D,YAAY,aAAZA,YAAY,wBAAAlC,sBAAA,GAAZkC,YAAY,CAAEyB,eAAe,cAAA3D,sBAAA,uBAA7BA,sBAAA,CAA+B2B,MAAM,IAAG,CAAC,iBAC7CtH,OAAA;0BACCiI,KAAK,EAAE;4BACNmE,YAAY,EAAE,MAAM;4BACpBU,OAAO,EAAE,UAAU;4BACnBzM,KAAK,GAAAuF,uBAAA,GAAEI,sBAAsB,CAAC,CAAC,CAAC,cAAAJ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B+B,MAAM,cAAA9B,uBAAA,uBAAjCA,uBAAA,CAAmC+B,YAAY;4BACtDqE,MAAM,EAAE,MAAM;4BACd2B,UAAU,EAAE,SAAS;4BACrBC,MAAM,EAAE;0BACT,CAAE;0BACFvC,OAAO,EAAGuE,CAAM,IAAK7F,qBAAqB,CAACnC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEV,EAAE,CAAE;0BAAA4D,QAAA,EAExDhF,SAAS,CAAC,mBAAmB;wBAAC;0BAAAwF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CACR;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACL;EAAA,eACA,CAAC;AAEL,CAAC;AAAC/J,EAAA,CA/wBKR,gBAA+C;EAAA,QAS3BrB,cAAc,EAC0BP,cAAc;AAAA;AAAA+Q,EAAA,GAV1EnP,gBAA+C;AAgxBvD,eAAeA,gBAAgB;AAAC,IAAAmP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}