{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport \"./App.scss\";\nimport Drawer from \"./components/drawer/Drawer\";\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\nimport { AccountProvider } from \"./components/login/AccountContext\";\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\nimport jwtDecode from \"jwt-decode\";\nimport useInfoStore from \"./store/UserInfoStore\";\nimport { initializeI18n } from \"./multilinguial/i18n\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isI18nReady, setIsI18nReady] = useState(false);\n  const [isAppReady, setIsAppReady] = useState(false);\n  const accessToken = useInfoStore(state => state.accessToken);\n  const {\n    clearAll,\n    clearAccessToken\n  } = useInfoStore.getState();\n\n  // Initialize i18n once when app starts\n  useEffect(() => {\n    const setupI18n = async () => {\n      try {\n        await initializeI18n();\n        console.log('✅ i18n ready for use');\n        setIsI18nReady(true);\n      } catch (error) {\n        console.error('❌ Failed to initialize i18n:', error);\n        // Set ready anyway to prevent infinite loading\n        setIsI18nReady(true);\n      }\n    };\n    setupI18n();\n  }, []);\n\n  // Signal when app is fully ready\n  useEffect(() => {\n    if (isI18nReady) {\n      // Add a small delay to ensure the Drawer component has started rendering\n      const timer = setTimeout(() => {\n        setIsAppReady(true);\n        // Dispatch custom event to signal the content script that the app is ready\n        window.dispatchEvent(new CustomEvent('quickadapt-app-ready'));\n      }, 100);\n      return () => clearTimeout(timer);\n    }\n  }, [isI18nReady]);\n\n  // Check token validity\n  useEffect(() => {\n    if (accessToken) {\n      try {\n        const decodedToken = jwtDecode(accessToken);\n        const currentTime = Math.floor(Date.now() / 1000);\n        if (decodedToken.exp < currentTime) {\n          console.log('🔐 Token expired, clearing session');\n          clearAll();\n          clearAccessToken();\n        }\n      } catch (error) {\n        console.error('❌ Invalid token, clearing session:', error);\n        clearAll();\n        clearAccessToken();\n      }\n    }\n  }, [accessToken, clearAll, clearAccessToken]);\n\n  // Show loading until i18n is ready\n  if (!isI18nReady) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 4\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(TranslationProvider, {\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(AccountProvider, {\n          children: /*#__PURE__*/_jsxDEV(SnackbarProvider, {\n            children: /*#__PURE__*/_jsxDEV(Drawer, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 3\n  }, this);\n}\n_s(App, \"9vW/KWktHaxAaj13SAazfnZMpQo=\", false, function () {\n  return [useInfoStore];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Drawer", "<PERSON>th<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>", "SnackbarProvider", "TranslationProvider", "jwtDecode", "useInfoStore", "initializeI18n", "jsxDEV", "_jsxDEV", "App", "_s", "isI18nReady", "setIsI18nReady", "isAppReady", "setIsAppReady", "accessToken", "state", "clearAll", "clearAccessToken", "getState", "setupI18n", "console", "log", "error", "timer", "setTimeout", "window", "dispatchEvent", "CustomEvent", "clearTimeout", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "className", "style", "display", "justifyContent", "alignItems", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport \"./App.scss\";\r\nimport GuidePopup from \"./components/guideSetting/GuidePopUp\";\r\nimport Drawer from \"./components/drawer/Drawer\";\r\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\r\nimport { AccountProvider } from \"./components/login/AccountContext\";\r\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\r\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\r\nimport Rte from \"./components/guideSetting/RTE\";\r\nimport jwtDecode from \"jwt-decode\";\r\nimport useInfoStore from \"./store/UserInfoStore\";\r\nimport { initializeI18n } from \"./multilinguial/i18n\";\r\n\r\nfunction App() {\r\n\tconst [isI18nReady, setIsI18nReady] = useState(false);\r\n\tconst [isAppReady, setIsAppReady] = useState(false);\r\n\tconst accessToken = useInfoStore((state) => state.accessToken);\r\n\tconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\n\r\n\t// Initialize i18n once when app starts\r\n\tuseEffect(() => {\r\n\t\tconst setupI18n = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait initializeI18n();\r\n\t\t\t\tconsole.log('✅ i18n ready for use');\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Failed to initialize i18n:', error);\r\n\t\t\t\t// Set ready anyway to prevent infinite loading\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tsetupI18n();\r\n\t}, []);\r\n\r\n\t// Signal when app is fully ready\r\n\tuseEffect(() => {\r\n\t\tif (isI18nReady) {\r\n\t\t\t// Add a small delay to ensure the Drawer component has started rendering\r\n\t\t\tconst timer = setTimeout(() => {\r\n\t\t\t\tsetIsAppReady(true);\r\n\t\t\t\t// Dispatch custom event to signal the content script that the app is ready\r\n\t\t\t\twindow.dispatchEvent(new CustomEvent('quickadapt-app-ready'));\r\n\t\t\t}, 100);\r\n\r\n\t\t\treturn () => clearTimeout(timer);\r\n\t\t}\r\n\t}, [isI18nReady]);\r\n\r\n\t// Check token validity\r\n\tuseEffect(() => {\r\n\t\tif (accessToken) {\r\n\t\t\ttry {\r\n\t\t\t\tconst decodedToken: any = jwtDecode(accessToken);\r\n\t\t\t\tconst currentTime = Math.floor(Date.now() / 1000);\r\n\t\t\t\tif (decodedToken.exp < currentTime) {\r\n\t\t\t\t\tconsole.log('🔐 Token expired, clearing session');\r\n\t\t\t\t\tclearAll();\r\n\t\t\t\t\tclearAccessToken();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Invalid token, clearing session:', error);\r\n\t\t\t\tclearAll();\r\n\t\t\t\tclearAccessToken();\r\n\t\t\t}\r\n\t\t}\r\n\t}, [accessToken, clearAll, clearAccessToken]);\r\n\r\n\t// Show loading until i18n is ready\r\n\tif (!isI18nReady) {\r\n\t\treturn (\r\n\t\t\t<div className=\"App\" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\r\n\t\t\t\t<div>Loading...</div>\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\treturn (\r\n\t\t<div className=\"App\">\r\n\t\t\t<TranslationProvider>\r\n\t\t\t\t<AuthProvider>\r\n\t\t\t\t\t<AccountProvider>\r\n\t\t\t\t\t\t<SnackbarProvider>\r\n\t\t\t\t\t\t\t<Drawer />\r\n\t\t\t\t\t\t</SnackbarProvider>\r\n\t\t\t\t\t</AccountProvider>\r\n\t\t\t\t</AuthProvider>\r\n\t\t\t</TranslationProvider>\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,YAAY;AAEnB,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,gBAAgB,QAAQ,qDAAqD;AACtF,SAASC,mBAAmB,QAAQ,+BAA+B;AAEnE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMiB,WAAW,GAAGV,YAAY,CAAEW,KAAK,IAAKA,KAAK,CAACD,WAAW,CAAC;EAC9D,MAAM;IAAEE,QAAQ;IAAEC;EAAiB,CAAC,GAAGb,YAAY,CAACc,QAAQ,CAAC,CAAC;;EAE9D;EACAtB,SAAS,CAAC,MAAM;IACf,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACH,MAAMd,cAAc,CAAC,CAAC;QACtBe,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnCV,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,OAAOW,KAAK,EAAE;QACfF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACAX,cAAc,CAAC,IAAI,CAAC;MACrB;IACD,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACf,IAAIc,WAAW,EAAE;MAChB;MACA,MAAMa,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC9BX,aAAa,CAAC,IAAI,CAAC;QACnB;QACAY,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,sBAAsB,CAAC,CAAC;MAC9D,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAMC,YAAY,CAACL,KAAK,CAAC;IACjC;EACD,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;;EAEjB;EACAd,SAAS,CAAC,MAAM;IACf,IAAIkB,WAAW,EAAE;MAChB,IAAI;QACH,MAAMe,YAAiB,GAAG1B,SAAS,CAACW,WAAW,CAAC;QAChD,MAAMgB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACjD,IAAIL,YAAY,CAACM,GAAG,GAAGL,WAAW,EAAE;UACnCV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjDL,QAAQ,CAAC,CAAC;UACVC,gBAAgB,CAAC,CAAC;QACnB;MACD,CAAC,CAAC,OAAOK,KAAK,EAAE;QACfF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DN,QAAQ,CAAC,CAAC;QACVC,gBAAgB,CAAC,CAAC;MACnB;IACD;EACD,CAAC,EAAE,CAACH,WAAW,EAAEE,QAAQ,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,IAAI,CAACP,WAAW,EAAE;IACjB,oBACCH,OAAA;MAAK6B,SAAS,EAAC,KAAK;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAChHnC,OAAA;QAAAmC,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAER;EAEA,oBACCvC,OAAA;IAAK6B,SAAS,EAAC,KAAK;IAAAM,QAAA,eACnBnC,OAAA,CAACL,mBAAmB;MAAAwC,QAAA,eACnBnC,OAAA,CAACR,YAAY;QAAA2C,QAAA,eACZnC,OAAA,CAACP,eAAe;UAAA0C,QAAA,eACfnC,OAAA,CAACN,gBAAgB;YAAAyC,QAAA,eAChBnC,OAAA,CAACT,MAAM;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAER;AAACrC,EAAA,CA9EQD,GAAG;EAAA,QAGSJ,YAAY;AAAA;AAAA2C,EAAA,GAHxBvC,GAAG;AAgFZ,eAAeA,GAAG;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}