{"ast": null, "code": "import React,{useEffect,useState}from'react';import'./ExtensionPopupLoader.css';import <PERSON>tieSpinner from'./LottieSpinner';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExtensionPopupLoader=_ref=>{let{duration=3000,onComplete,position='top-right'}=_ref;const[isVisible,setIsVisible]=useState(true);const[progress,setProgress]=useState(0);useEffect(()=>{// Animate progress bar\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=100){clearInterval(progressInterval);return 100;}return prev+100/(duration/50);// Update every 50ms\n});},50);// Auto hide after duration\nconst hideTimer=setTimeout(()=>{setIsVisible(false);if(onComplete){setTimeout(onComplete,300);// Wait for fade out animation\n}},duration);return()=>{clearInterval(progressInterval);clearTimeout(hideTimer);};},[duration,onComplete]);if(!isVisible)return null;return/*#__PURE__*/_jsx(\"div\",{className:`extension-popup-loader ${position} ${isVisible?'visible':'hidden'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"popup-loader-container\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"popup-close-btn-minimal\",onClick:()=>setIsVisible(false),\"aria-label\":\"Close\",children:\"\\xD7\"}),/*#__PURE__*/_jsx(\"div\",{className:\"popup-lottie-content\",children:/*#__PURE__*/_jsx(LottieSpinner,{size:window.innerWidth<=480?45:55,backgroundColor:\"#ff8c00\"})})]})});};export default ExtensionPopupLoader;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ExtensionPopup<PERSON><PERSON>der", "_ref", "duration", "onComplete", "position", "isVisible", "setIsVisible", "progress", "setProgress", "progressInterval", "setInterval", "prev", "clearInterval", "hide<PERSON><PERSON>r", "setTimeout", "clearTimeout", "className", "children", "onClick", "size", "window", "innerWidth", "backgroundColor"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionPopupLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\nimport LottieSpinner from './LottieSpinner';\n\ninterface ExtensionPopupLoaderProps {\n  duration?: number;\n  onComplete?: () => void;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\n}\n\nconst ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + (100 / (duration / 50)); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>\n      <div className=\"popup-loader-container\">\n        {/* Close button */}\n        <button\n          className=\"popup-close-btn-minimal\"\n          onClick={() => setIsVisible(false)}\n          aria-label=\"Close\"\n        >\n          ×\n        </button>\n\n        {/* Lottie Spinner Content */}\n        <div className=\"popup-lottie-content\">\n          <LottieSpinner\n            size={window.innerWidth <= 480 ? 45 : 55}\n            backgroundColor=\"#ff8c00\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExtensionPopupLoader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,4BAA4B,CACnC,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ5C,KAAM,CAAAC,oBAAyD,CAAGC,IAAA,EAI5D,IAJ6D,CACjEC,QAAQ,CAAG,IAAI,CACfC,UAAU,CACVC,QAAQ,CAAG,WACb,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACa,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,CAAC,CAAC,CAE3CD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAgB,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCF,WAAW,CAACG,IAAI,EAAI,CAClB,GAAIA,IAAI,EAAI,GAAG,CAAE,CACfC,aAAa,CAACH,gBAAgB,CAAC,CAC/B,MAAO,IAAG,CACZ,CACA,MAAO,CAAAE,IAAI,CAAI,GAAG,EAAIT,QAAQ,CAAG,EAAE,CAAE,CAAE;AACzC,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAW,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjCR,YAAY,CAAC,KAAK,CAAC,CACnB,GAAIH,UAAU,CAAE,CACdW,UAAU,CAACX,UAAU,CAAE,GAAG,CAAC,CAAE;AAC/B,CACF,CAAC,CAAED,QAAQ,CAAC,CAEZ,MAAO,IAAM,CACXU,aAAa,CAACH,gBAAgB,CAAC,CAC/BM,YAAY,CAACF,SAAS,CAAC,CACzB,CAAC,CACH,CAAC,CAAE,CAACX,QAAQ,CAAEC,UAAU,CAAC,CAAC,CAE1B,GAAI,CAACE,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACER,IAAA,QAAKmB,SAAS,CAAE,0BAA0BZ,QAAQ,IAAIC,SAAS,CAAG,SAAS,CAAG,QAAQ,EAAG,CAAAY,QAAA,cACvFlB,KAAA,QAAKiB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErCpB,IAAA,WACEmB,SAAS,CAAC,yBAAyB,CACnCE,OAAO,CAAEA,CAAA,GAAMZ,YAAY,CAAC,KAAK,CAAE,CACnC,aAAW,OAAO,CAAAW,QAAA,CACnB,MAED,CAAQ,CAAC,cAGTpB,IAAA,QAAKmB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCpB,IAAA,CAACF,aAAa,EACZwB,IAAI,CAAEC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAG,EAAE,CAAG,EAAG,CACzCC,eAAe,CAAC,SAAS,CAC1B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}