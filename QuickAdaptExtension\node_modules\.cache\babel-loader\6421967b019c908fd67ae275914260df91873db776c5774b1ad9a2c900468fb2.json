{"ast": null, "code": "import React from'react';import useDrawerStore from'../../store/drawerStore';import{Select,MenuItem,Typography}from\"@mui/material\";import{warning}from\"../../assets/icons/icons\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StepCreationPopup=props=>{const{t:translate}=useTranslation();const{isOpen,onClose,onCreateStep,stepData,setStepData,isEditStepName,stepName,setStepName,editType,setEditType,editDescription,setDescription,setTemplateSelectionPopup,setDeleteClicked,setUpdateStepClicked}=props;const{setSelectedTemplate,setBannerPopup,setSelectedTemplateTour,selectedTemplateTour,selectedTemplate,steps,setSelectedStepTypeHotspot,selectedStepTypeHotspot,setBposition}=useDrawerStore(state=>state);if(!isOpen)return null;const handleStepTypeChange=e=>{const selectedType=e.target.value;setStepData(prevData=>({...prevData,type:selectedType}));setEditType(selectedType);// update editType state for future reference\n};const handleCreate=()=>{setDeleteClicked(false);const updatedStepData={...stepData,type:(stepData===null||stepData===void 0?void 0:stepData.type)||\"Announcement\"};// If creating a NEW Banner step in a Tour, set default position to Cover Top\nif(updatedStepData.type===\"Banner\"&&selectedTemplate===\"Tour\"){// Only set default position for new banners\nsetBposition(\"Cover Top\");}onCreateStep(updatedStepData);onClose();setStepData(\"\");};let isDuplicateStep=false;if(isEditStepName){setDeleteClicked(false);setUpdateStepClicked(true);const originalStepName=stepData.stepNumber;isDuplicateStep=steps.some(step=>step.name===stepName&&step.name!==originalStepName);}else{setDeleteClicked(false);isDuplicateStep=steps.some(step=>step.name===stepName);}const stepNameTrimmed=stepName?stepName.trim():'';const descriptionTrimmed=stepData.description?stepData.description.replace(/\\s+/g,''):editDescription?editDescription.replace(/\\s+/g,''):'';const isCreateButtonDisabled=!stepName||stepNameTrimmed.length<3||stepNameTrimmed.length>50||isDuplicateStep||descriptionTrimmed.length>50||stepNameTrimmed.length>20;return/*#__PURE__*/_jsxs(\"div\",{style:{width:'195px',backgroundColor:'#fff',borderRadius:'6px',boxShadow:'0px 4px 14px 0px #00000026',border:'1px solid #e0e0e0',zIndex:1000,height:'auto',overflow:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'10px 12px'// width: '171px',\n// maxHeight: '265px',    \n},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'start',marginBottom:'4px'},children:/*#__PURE__*/_jsx(\"span\",{style:{color:'#616365',lineHeight:'18px'},children:translate(\"Step Name\",{defaultValue:\"Step Name\"})})}),/*#__PURE__*/_jsx(\"div\",{className:`step-input ${isDuplicateStep?'qadpt-stbdr':''}`,children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:stepName,onChange:e=>{const newStepName=e.target.value;//setStepData({ ...stepData, stepNumber: newStepName });\nsetStepName(newStepName);},onFocus:e=>e.target.style.border='1px solid #a8a8a8',onBlur:e=>e.target.style.border='1px solid #a8a8a8',style:{border:isDuplicateStep||stepNameTrimmed.length>20?'1px solid red':'1px solid #a8a8a8'}})}),isDuplicateStep&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Step name should be unique\",{defaultValue:\"Step name should be unique\"})]}),stepName&&stepNameTrimmed.length<3&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Guide name must be at least 3 characters.\",{defaultValue:\"Guide name must be at least 3 characters.\"})]}),stepName&&stepNameTrimmed.length>20&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Step name should not exceed 20 characters.\",{defaultValue:\"Step name should not exceed 20 characters.\"})]})]}),selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'start',marginBottom:'4px'},children:/*#__PURE__*/_jsx(\"span\",{style:{color:'#616365',lineHeight:'18px'},children:translate(\"Step Type:\",{defaultValue:\"Step Type:\"})})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Select,{value:stepData.type||editType||(selectedStepTypeHotspot===true?\"Hotspot\":\"Announcement\"),disabled:isEditStepName,onOpen:e=>{setTemplateSelectionPopup(true);},onChange:e=>{setStepData({...stepData,type:e.target.value});setEditType(e.target.value);// If Banner is selected in a Tour, ensure position is set to Cover Top\nif(e.target.value===\"Banner\"&&selectedTemplate===\"Tour\"){setBposition(\"Cover Top\");}},onClose:e=>{setTemplateSelectionPopup(false);},displayEmpty:true,MenuProps:{sx:{zIndex:9999999},PopoverClasses:{root:'qadpt-turstp'}},sx:{width:\"171px\",padding:\"6px 10px\",borderRadius:\"6px\",fontSize:\"14px\",height:\"30px\",color:\"#000\",background:\"#fff\",outline:\"none\",textAlign:\"left\",minWidth:\"100%\",\"&:hover .MuiOutlinedInput-notchedOutline\":{borderColor:\"#a8a8a8\"},// Prevents color change on hover\n\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{borderColor:\"#a8a8a8\"},// Prevents focus color change\n\"& .MuiSelect-select\":{paddingLeft:\"0 !important\"}},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Announcement\",children:translate(\"Announcement\",{defaultValue:\"Announcement\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"Banner\",children:translate(\"Banner\",{defaultValue:\"Banner\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"Tooltip\",children:translate(\"Tooltip\",{defaultValue:\"Tooltip\"})}),(selectedStepTypeHotspot===true||editType===\"Hotspot\")&&/*#__PURE__*/_jsx(MenuItem,{value:\"Hotspot\",children:translate(\"Hotspot\",{defaultValue:\"Hotspot\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'start',marginBottom:'4px'},children:/*#__PURE__*/_jsxs(\"span\",{style:{color:'#616365',lineHeight:'18px'},children:[translate(\"Description\",{defaultValue:\"Description\"}),\":\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:`step-input ${descriptionTrimmed.length>50?'qadpt-stbdr':''}`,children:[/*#__PURE__*/_jsx(\"textarea\",{placeholder:!stepData.description&&!editDescription?translate(\"write description\",{defaultValue:\"write description\"}):\"\",value:stepData.description||editDescription||\"\",onChange:e=>{setStepData({...stepData,description:e.target.value});setDescription(e.target.value);},style:{width:\"-webkit-fill-available\",padding:\"6px 10px\",borderRadius:\"6px\",background:\"#fff\",outline:\"none\",textAlign:\"left\",minHeight:\"60px\"}}),descriptionTrimmed.length>50&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Description must be a maximum of 50 characters.\",{defaultValue:\"Description must be a maximum of 50 characters.\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{padding:\"0 10px 10px 10px\"},children:/*#__PURE__*/_jsx(\"button\",{onClick:handleCreate,disabled:isCreateButtonDisabled,style:{width:'100%',padding:'7px 10px',backgroundColor:\"var(--primarycolor)\",// Change color when disabled\nopacity:isCreateButtonDisabled?\"0.5\":\"1\",color:'#fff',border:'none',borderRadius:'7px',cursor:'pointer',display:\"block\"},children:isEditStepName?translate(\"Update\",{defaultValue:\"Update\"}):translate(\"Create\",{defaultValue:\"Create\"})})})]});};export default StepCreationPopup;", "map": {"version": 3, "names": ["React", "useDrawerStore", "Select", "MenuItem", "Typography", "warning", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "StepCreationPopup", "props", "t", "translate", "isOpen", "onClose", "onCreateStep", "stepData", "setStepData", "isEditStepName", "<PERSON><PERSON><PERSON>", "setStepName", "editType", "setEditType", "editDescription", "setDescription", "setTemplateSelectionPopup", "setDeleteClicked", "setUpdateStepClicked", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "selectedTemplateTour", "selectedTemplate", "steps", "setSelectedStepTypeHotspot", "selectedStepTypeHotspot", "setBposition", "state", "handleStepTypeChange", "e", "selectedType", "target", "value", "prevData", "type", "handleCreate", "updatedStepData", "isDuplicateStep", "originalStepName", "<PERSON><PERSON><PERSON><PERSON>", "some", "step", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "descriptionTrimmed", "description", "replace", "isCreateButtonDisabled", "length", "style", "width", "backgroundColor", "borderRadius", "boxShadow", "border", "zIndex", "height", "overflow", "children", "padding", "textAlign", "marginBottom", "color", "lineHeight", "defaultValue", "className", "onChange", "newStepName", "onFocus", "onBlur", "fontSize", "top", "left", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "disabled", "onOpen", "displayEmpty", "MenuProps", "sx", "PopoverClasses", "root", "background", "outline", "min<PERSON><PERSON><PERSON>", "borderColor", "paddingLeft", "placeholder", "minHeight", "onClick", "opacity", "cursor"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/tours/stepPopup.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { Select, MenuItem, Typography } from \"@mui/material\";\r\nimport { ForkLeft } from '@mui/icons-material';\r\nimport {warning} from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst StepCreationPopup: React.FC<{ isOpen: boolean; onClose: () => void; onCreateStep:any,stepData:any,setStepData:any,isEditStepName:any,stepName:any,setStepName:any,editType:any,setEditType:any,editDescription:any,setDescription:any,setTemplateSelectionPopup:any,setDeleteClicked:any,setUpdateStepClicked:any}> = (props) => {    \r\n  const { t: translate } = useTranslation();\r\n  const {\r\n    isOpen, onClose, onCreateStep, stepData, setStepData, isEditStepName, stepName, setStepName, editType, setEditType, editDescription, setDescription, setTemplateSelectionPopup,setDeleteClicked,setUpdateStepClicked\r\n  } = props;\r\n  const {\r\n        setSelectedTemplate,\r\n        setBannerPopup,\r\n        setSelectedTemplateTour,\r\n  selectedTemplateTour,\r\n  selectedTemplate,\r\n  steps,\r\n  setSelectedStepTypeHotspot,\r\n  selectedStepTypeHotspot,\r\n  setBposition,\r\n    } = useDrawerStore((state: DrawerState) => state);\r\n\r\n  if (!isOpen) return null;\r\n  const handleStepTypeChange = (e: React.ChangeEvent<{ value: unknown }>) => {\r\n    const selectedType = e.target.value;\r\n    setStepData((prevData: any) => ({\r\n      ...prevData,\r\n      type: selectedType,\r\n    }));\r\n    setEditType(selectedType); // update editType state for future reference\r\n  };\r\n  const handleCreate = () => {\r\n    setDeleteClicked(false);\r\n    const updatedStepData =\r\n    {\r\n      ...stepData,\r\n      type: stepData?.type||\"Announcement\",\r\n      \r\n      }\r\n\r\n    // If creating a NEW Banner step in a Tour, set default position to Cover Top\r\n    if (updatedStepData.type === \"Banner\" && selectedTemplate === \"Tour\") {\r\n      // Only set default position for new banners\r\n      setBposition(\"Cover Top\");\r\n    }\r\n\r\n    onCreateStep(updatedStepData);\r\n      onClose();\r\n      setStepData(\"\");\r\n  };\r\n  let isDuplicateStep = false;\r\n\r\n\r\n  if (isEditStepName) {\r\n    setDeleteClicked(false);\r\n    setUpdateStepClicked(true);\r\n\r\n    \r\n    const originalStepName = stepData.stepNumber;\r\n    \r\n    isDuplicateStep = steps.some(step => \r\n      step.name === stepName && step.name !== originalStepName\r\n    );\r\n  } else {\r\n        setDeleteClicked(false);\r\n\r\n    isDuplicateStep = steps.some(step => step.name === stepName);\r\n    }\r\n\r\n  const stepNameTrimmed = stepName ? stepName.trim() : '';\r\n  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\\s+/g, '') : editDescription ? editDescription.replace(/\\s+/g, '') : '';\r\n  const isCreateButtonDisabled =\r\n    !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;\r\n  return (\r\n    <div style={{\r\n      width: '195px',\r\n      backgroundColor: '#fff',\r\n      borderRadius: '6px',\r\n      boxShadow: '0px 4px 14px 0px #00000026',\r\n      border: '1px solid #e0e0e0',\r\n      zIndex: 1000,\r\n      height: 'auto',\r\n      overflow:'auto'\r\n    }}>\r\n      <div style={{\r\n              padding: '10px 12px',\r\n        // width: '171px',\r\n        // maxHeight: '265px',    \r\n      }}>\r\n        <div>\r\n        <div style={{\r\n          textAlign: 'start',\r\n          marginBottom:'4px'\r\n        }}>\r\n          <span style={{\r\n            color: '#616365',\r\n            lineHeight: '18px'  \r\n            }}>{translate(\"Step Name\", { defaultValue: \"Step Name\" })}</span>\r\n        </div>\r\n\r\n        <div className={`step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`}>\r\n          <input\r\n            type=\"text\"\r\n            value={stepName}\r\n            onChange={(e) => {\r\n              const newStepName = e.target.value;\r\n              //setStepData({ ...stepData, stepNumber: newStepName });\r\n              setStepName(newStepName);\r\n            }}\r\n            onFocus={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            onBlur={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            style={{\r\n              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n{/* Error message for duplicate step name */}\r\n{isDuplicateStep && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n              {translate(\"Step name should be unique\", { defaultValue: \"Step name should be unique\" })}\r\n  </Typography>\r\n)}\r\n\r\n        {stepName && stepNameTrimmed.length < 3 && (\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n              {translate(\"Guide name must be at least 3 characters.\", { defaultValue: \"Guide name must be at least 3 characters.\" })}\r\n            </Typography>\r\n          )}  \r\n          {stepName && stepNameTrimmed.length > 20 && (\r\n\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n              {translate(\"Step name should not exceed 20 characters.\", { defaultValue: \"Step name should not exceed 20 characters.\" })}\r\n  </Typography>\r\n)}\r\n      </div>\r\n        {selectedTemplate === \"Tour\" && (\r\n\r\n\r\n          <div style={{\r\n            marginBottom: '12px',\r\n          }}>\r\n            <div style={{\r\n              textAlign: 'start',\r\n              marginBottom: '4px'\r\n            }}>\r\n              <span style={{\r\n                color: '#616365',\r\n                lineHeight: '18px'\r\n              }}>{translate(\"Step Type:\", { defaultValue: \"Step Type:\" })}</span>\r\n            </div>\r\n       \r\n            \r\n            <div>\r\n              <Select\r\n               value={stepData.type || editType || (selectedStepTypeHotspot === true ? \"Hotspot\" : \"Announcement\")}\r\n                disabled={isEditStepName}\r\n                onOpen={(e) => {\r\n                  setTemplateSelectionPopup(true);\r\n                }}\r\n                onChange={(e) => {\r\n                  setStepData({ ...stepData, type: e.target.value });\r\n                  setEditType(e.target.value);\r\n                  // If Banner is selected in a Tour, ensure position is set to Cover Top\r\n                  if (e.target.value === \"Banner\" && selectedTemplate === \"Tour\") {\r\n                    setBposition(\"Cover Top\");\r\n                  }\r\n                }\r\n                }\r\n                onClose={(e) => {\r\n                  setTemplateSelectionPopup(false);\r\n                }}\r\n                displayEmpty\r\n                MenuProps={{\r\n                  sx: {\r\n                    zIndex: 9999999, \r\n                  },\r\n                  PopoverClasses: {\r\n                    root: 'qadpt-turstp', \r\n                  },\r\n                }}\r\n                sx={{\r\n                  width: \"171px\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: \"6px\",\r\n                  fontSize: \"14px\",\r\n                  height: \"30px\",\r\n                  color: \"#000\",\r\n                  background: \"#fff\",\r\n                  outline: \"none\",\r\n                  textAlign: \"left\",\r\n                  minWidth: \"100%\",\r\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents focus color change\r\n                  \"& .MuiSelect-select\": { paddingLeft: \"0 !important\" },\r\n                 \r\n                }}\r\n              >\r\n                <MenuItem value=\"Announcement\">{translate(\"Announcement\", { defaultValue: \"Announcement\" })}</MenuItem>\r\n                <MenuItem value=\"Banner\">{translate(\"Banner\", { defaultValue: \"Banner\" })}</MenuItem>\r\n                <MenuItem value=\"Tooltip\">{translate(\"Tooltip\", { defaultValue: \"Tooltip\" })}</MenuItem>\r\n                {(selectedStepTypeHotspot === true || editType === \"Hotspot\") && <MenuItem value=\"Hotspot\">{translate(\"Hotspot\", { defaultValue: \"Hotspot\" })}</MenuItem>}\r\n              </Select>\r\n            </div>\r\n          \r\n\r\n          </div>\r\n        )}\r\n\r\n<div style={{ marginBottom: '12px' }}>\r\n  <div style={{ textAlign: 'start', marginBottom: '4px' }}>\r\n            <span style={{ color: '#616365', lineHeight: '18px' }}>{translate(\"Description\", { defaultValue: \"Description\" })}:</span>\r\n  </div>\r\n\r\n  <div className={`step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`}>\r\n    <textarea\r\n      placeholder={\r\n                (!stepData.description && !editDescription) ? translate(\"write description\", { defaultValue: \"write description\" }) : \"\"\r\n      }\r\n      value={stepData.description || editDescription || \"\"}\r\n      onChange={(e) => {\r\n        setStepData({ ...stepData, description: e.target.value });\r\n        setDescription(e.target.value);\r\n      }}\r\n      style={{\r\n        width: \"-webkit-fill-available\",\r\n        padding: \"6px 10px\",\r\n        borderRadius: \"6px\",\r\n        background: \"#fff\",\r\n        outline: \"none\",\r\n        textAlign: \"left\",\r\n        minHeight: \"60px\",\r\n      }}\r\n    />\r\n\r\n    {descriptionTrimmed.length > 50 && (\r\n      <Typography\r\n        style={{\r\n          fontSize: \"12px\",\r\n          color: \"#e9a971\",\r\n          textAlign: \"left\",\r\n          top: \"100%\",\r\n          left: 0,\r\n          marginBottom: \"5px\",\r\n          display: \"flex\",\r\n        }}\r\n      >\r\n        <span\r\n          style={{\r\n            display: \"flex\",\r\n            fontSize: \"12px\",\r\n            alignItems: \"center\",\r\n            marginRight: \"4px\"\r\n          }}\r\n          dangerouslySetInnerHTML={{ __html: warning }}\r\n        />\r\n                {translate(\"Description must be a maximum of 50 characters.\", { defaultValue: \"Description must be a maximum of 50 characters.\" })}\r\n      </Typography>\r\n    )}\r\n  </div>\r\n</div>\r\n\r\n       \r\n      </div>\r\n      <div style={{padding: \"0 10px 10px 10px\"}}>\r\n      <button\r\n          onClick={handleCreate}\r\n          disabled={isCreateButtonDisabled}\r\n          style={{\r\n            width: '100%',\r\n            padding: '7px 10px',\r\n            backgroundColor: \"var(--primarycolor)\", // Change color when disabled\r\n            opacity:isCreateButtonDisabled?\"0.5\":\"1\",\r\n            color: '#fff',\r\n            border: 'none',\r\n            borderRadius: '7px',\r\n            cursor: 'pointer',\r\n            display:\"block\",\r\n          }}\r\n        >\r\n          {isEditStepName ? translate(\"Update\", { defaultValue: \"Update\" }) : translate(\"Create\", { defaultValue: \"Create\" })}\r\n        </button>\r\n        </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StepCreationPopup;\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAA+B,OAAO,CAClD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,OAASC,MAAM,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,eAAe,CAE5D,OAAQC,OAAO,KAAO,0BAA0B,CAChD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,iBAAmT,CAAIC,KAAK,EAAK,CACrU,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGR,cAAc,CAAC,CAAC,CACzC,KAAM,CACJS,MAAM,CAAEC,OAAO,CAAEC,YAAY,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,cAAc,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,eAAe,CAAEC,cAAc,CAAEC,yBAAyB,CAACC,gBAAgB,CAACC,oBAClM,CAAC,CAAGjB,KAAK,CACT,KAAM,CACAkB,mBAAmB,CACnBC,cAAc,CACdC,uBAAuB,CAC7BC,oBAAoB,CACpBC,gBAAgB,CAChBC,KAAK,CACLC,0BAA0B,CAC1BC,uBAAuB,CACvBC,YACE,CAAC,CAAGrC,cAAc,CAAEsC,KAAkB,EAAKA,KAAK,CAAC,CAEnD,GAAI,CAACxB,MAAM,CAAE,MAAO,KAAI,CACxB,KAAM,CAAAyB,oBAAoB,CAAIC,CAAwC,EAAK,CACzE,KAAM,CAAAC,YAAY,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CACnCzB,WAAW,CAAE0B,QAAa,GAAM,CAC9B,GAAGA,QAAQ,CACXC,IAAI,CAAEJ,YACR,CAAC,CAAC,CAAC,CACHlB,WAAW,CAACkB,YAAY,CAAC,CAAE;AAC7B,CAAC,CACD,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzBnB,gBAAgB,CAAC,KAAK,CAAC,CACvB,KAAM,CAAAoB,eAAe,CACrB,CACE,GAAG9B,QAAQ,CACX4B,IAAI,CAAE,CAAA5B,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4B,IAAI,GAAE,cAEtB,CAAC,CAEH;AACA,GAAIE,eAAe,CAACF,IAAI,GAAK,QAAQ,EAAIZ,gBAAgB,GAAK,MAAM,CAAE,CACpE;AACAI,YAAY,CAAC,WAAW,CAAC,CAC3B,CAEArB,YAAY,CAAC+B,eAAe,CAAC,CAC3BhC,OAAO,CAAC,CAAC,CACTG,WAAW,CAAC,EAAE,CAAC,CACnB,CAAC,CACD,GAAI,CAAA8B,eAAe,CAAG,KAAK,CAG3B,GAAI7B,cAAc,CAAE,CAClBQ,gBAAgB,CAAC,KAAK,CAAC,CACvBC,oBAAoB,CAAC,IAAI,CAAC,CAG1B,KAAM,CAAAqB,gBAAgB,CAAGhC,QAAQ,CAACiC,UAAU,CAE5CF,eAAe,CAAGd,KAAK,CAACiB,IAAI,CAACC,IAAI,EAC/BA,IAAI,CAACC,IAAI,GAAKjC,QAAQ,EAAIgC,IAAI,CAACC,IAAI,GAAKJ,gBAC1C,CAAC,CACH,CAAC,IAAM,CACDtB,gBAAgB,CAAC,KAAK,CAAC,CAE3BqB,eAAe,CAAGd,KAAK,CAACiB,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,GAAKjC,QAAQ,CAAC,CAC5D,CAEF,KAAM,CAAAkC,eAAe,CAAGlC,QAAQ,CAAGA,QAAQ,CAACmC,IAAI,CAAC,CAAC,CAAG,EAAE,CACvD,KAAM,CAAAC,kBAAkB,CAAGvC,QAAQ,CAACwC,WAAW,CAAGxC,QAAQ,CAACwC,WAAW,CAACC,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,CAAGlC,eAAe,CAAGA,eAAe,CAACkC,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,CAAG,EAAE,CACvJ,KAAM,CAAAC,sBAAsB,CAC1B,CAACvC,QAAQ,EAAIkC,eAAe,CAACM,MAAM,CAAG,CAAC,EAAIN,eAAe,CAACM,MAAM,CAAG,EAAE,EAAIZ,eAAe,EAAIQ,kBAAkB,CAACI,MAAM,CAAG,EAAE,EAAIN,eAAe,CAACM,MAAM,CAAG,EAAE,CAC5J,mBACEnD,KAAA,QAAKoD,KAAK,CAAE,CACVC,KAAK,CAAE,OAAO,CACdC,eAAe,CAAE,MAAM,CACvBC,YAAY,CAAE,KAAK,CACnBC,SAAS,CAAE,4BAA4B,CACvCC,MAAM,CAAE,mBAAmB,CAC3BC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAC,MACX,CAAE,CAAAC,QAAA,eACA7D,KAAA,QAAKoD,KAAK,CAAE,CACJU,OAAO,CAAE,WACf;AACA;AACF,CAAE,CAAAD,QAAA,eACA7D,KAAA,QAAA6D,QAAA,eACA/D,IAAA,QAAKsD,KAAK,CAAE,CACVW,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAC,KACf,CAAE,CAAAH,QAAA,cACA/D,IAAA,SAAMsD,KAAK,CAAE,CACXa,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,MACZ,CAAE,CAAAL,QAAA,CAAEzD,SAAS,CAAC,WAAW,CAAE,CAAE+D,YAAY,CAAE,WAAY,CAAC,CAAC,CAAO,CAAC,CAChE,CAAC,cAENrE,IAAA,QAAKsE,SAAS,CAAE,cAAc7B,eAAe,CAAG,aAAa,CAAG,EAAE,EAAG,CAAAsB,QAAA,cACnE/D,IAAA,UACEsC,IAAI,CAAC,MAAM,CACXF,KAAK,CAAEvB,QAAS,CAChB0D,QAAQ,CAAGtC,CAAC,EAAK,CACf,KAAM,CAAAuC,WAAW,CAAGvC,CAAC,CAACE,MAAM,CAACC,KAAK,CAClC;AACAtB,WAAW,CAAC0D,WAAW,CAAC,CAC1B,CAAE,CACFC,OAAO,CAAGxC,CAAC,EAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,CAAG,mBAAoB,CAC5De,MAAM,CAAGzC,CAAC,EAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,CAAG,mBAAoB,CAC3DL,KAAK,CAAE,CACLK,MAAM,CAAElB,eAAe,EAAIM,eAAe,CAACM,MAAM,CAAG,EAAE,CAAG,eAAe,CAAG,mBAC7E,CAAE,CACH,CAAC,CACC,CAAC,CAGbZ,eAAe,eACdvC,KAAA,CAACN,UAAU,EACT0D,KAAK,CAAE,CACLqB,QAAQ,CAAE,MAAM,CAChBR,KAAK,CAAE,SAAS,CAChBF,SAAS,CAAE,MAAM,CACjBW,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPX,YAAY,CAAE,KAAK,CACnBY,OAAO,CAAE,MACX,CAAE,CAAAf,QAAA,eAEF/D,IAAA,SACEsD,KAAK,CAAE,CAAEwB,OAAO,CAAE,MAAM,CAAEH,QAAQ,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAE,KAAM,CAAE,CACvFC,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,OAAQ,CAAE,CAC9C,CAAC,CACSS,SAAS,CAAC,4BAA4B,CAAE,CAAE+D,YAAY,CAAE,4BAA6B,CAAC,CAAC,EACxF,CACb,CAEQxD,QAAQ,EAAIkC,eAAe,CAACM,MAAM,CAAG,CAAC,eAC7CnD,KAAA,CAACN,UAAU,EACX0D,KAAK,CAAE,CACLqB,QAAQ,CAAE,MAAM,CAChBR,KAAK,CAAE,SAAS,CAChBF,SAAS,CAAE,MAAM,CACjBW,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPX,YAAY,CAAE,KAAK,CACnBY,OAAO,CAAE,MACX,CAAE,CAAAf,QAAA,eAEF/D,IAAA,SACEsD,KAAK,CAAE,CAAEwB,OAAO,CAAE,MAAM,CAAEH,QAAQ,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAE,KAAM,CAAE,CACvFC,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,OAAQ,CAAE,CAC9C,CAAC,CACWS,SAAS,CAAC,2CAA2C,CAAE,CAAE+D,YAAY,CAAE,2CAA4C,CAAC,CAAC,EAC5G,CACb,CACAxD,QAAQ,EAAIkC,eAAe,CAACM,MAAM,CAAG,EAAE,eAEhDnD,KAAA,CAACN,UAAU,EACX0D,KAAK,CAAE,CACLqB,QAAQ,CAAE,MAAM,CAChBR,KAAK,CAAE,SAAS,CAChBF,SAAS,CAAE,MAAM,CACjBW,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPX,YAAY,CAAE,KAAK,CACnBY,OAAO,CAAE,MACX,CAAE,CAAAf,QAAA,eAEF/D,IAAA,SACEsD,KAAK,CAAE,CAAEwB,OAAO,CAAE,MAAM,CAAEH,QAAQ,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAE,KAAM,CAAE,CACvFC,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,OAAQ,CAAE,CAC9C,CAAC,CACWS,SAAS,CAAC,4CAA4C,CAAE,CAAE+D,YAAY,CAAE,4CAA6C,CAAC,CAAC,EACxH,CACb,EACU,CAAC,CACH3C,gBAAgB,GAAK,MAAM,eAG1BxB,KAAA,QAAKoD,KAAK,CAAE,CACVY,YAAY,CAAE,MAChB,CAAE,CAAAH,QAAA,eACA/D,IAAA,QAAKsD,KAAK,CAAE,CACVW,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAChB,CAAE,CAAAH,QAAA,cACA/D,IAAA,SAAMsD,KAAK,CAAE,CACXa,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAL,QAAA,CAAEzD,SAAS,CAAC,YAAY,CAAE,CAAE+D,YAAY,CAAE,YAAa,CAAC,CAAC,CAAO,CAAC,CAChE,CAAC,cAGNrE,IAAA,QAAA+D,QAAA,cACE7D,KAAA,CAACR,MAAM,EACN0C,KAAK,CAAE1B,QAAQ,CAAC4B,IAAI,EAAIvB,QAAQ,GAAKc,uBAAuB,GAAK,IAAI,CAAG,SAAS,CAAG,cAAc,CAAE,CACnGsD,QAAQ,CAAEvE,cAAe,CACzBwE,MAAM,CAAGnD,CAAC,EAAK,CACbd,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAE,CACFoD,QAAQ,CAAGtC,CAAC,EAAK,CACftB,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAE4B,IAAI,CAAEL,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAAC,CAClDpB,WAAW,CAACiB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAC3B;AACA,GAAIH,CAAC,CAACE,MAAM,CAACC,KAAK,GAAK,QAAQ,EAAIV,gBAAgB,GAAK,MAAM,CAAE,CAC9DI,YAAY,CAAC,WAAW,CAAC,CAC3B,CACF,CACC,CACDtB,OAAO,CAAGyB,CAAC,EAAK,CACdd,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAE,CACFkE,YAAY,MACZC,SAAS,CAAE,CACTC,EAAE,CAAE,CACF3B,MAAM,CAAE,OACV,CAAC,CACD4B,cAAc,CAAE,CACdC,IAAI,CAAE,cACR,CACF,CAAE,CACFF,EAAE,CAAE,CACFhC,KAAK,CAAE,OAAO,CACdS,OAAO,CAAE,UAAU,CACnBP,YAAY,CAAE,KAAK,CACnBkB,QAAQ,CAAE,MAAM,CAChBd,MAAM,CAAE,MAAM,CACdM,KAAK,CAAE,MAAM,CACbuB,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,MAAM,CACf1B,SAAS,CAAE,MAAM,CACjB2B,QAAQ,CAAE,MAAM,CAChB,0CAA0C,CAAE,CAAEC,WAAW,CAAE,SAAU,CAAC,CAAE;AACxE,gDAAgD,CAAE,CAAEA,WAAW,CAAE,SAAU,CAAC,CAAE;AAC9E,qBAAqB,CAAE,CAAEC,WAAW,CAAE,cAAe,CAEvD,CAAE,CAAA/B,QAAA,eAEF/D,IAAA,CAACL,QAAQ,EAACyC,KAAK,CAAC,cAAc,CAAA2B,QAAA,CAAEzD,SAAS,CAAC,cAAc,CAAE,CAAE+D,YAAY,CAAE,cAAe,CAAC,CAAC,CAAW,CAAC,cACvGrE,IAAA,CAACL,QAAQ,EAACyC,KAAK,CAAC,QAAQ,CAAA2B,QAAA,CAAEzD,SAAS,CAAC,QAAQ,CAAE,CAAE+D,YAAY,CAAE,QAAS,CAAC,CAAC,CAAW,CAAC,cACrFrE,IAAA,CAACL,QAAQ,EAACyC,KAAK,CAAC,SAAS,CAAA2B,QAAA,CAAEzD,SAAS,CAAC,SAAS,CAAE,CAAE+D,YAAY,CAAE,SAAU,CAAC,CAAC,CAAW,CAAC,CACvF,CAACxC,uBAAuB,GAAK,IAAI,EAAId,QAAQ,GAAK,SAAS,gBAAKf,IAAA,CAACL,QAAQ,EAACyC,KAAK,CAAC,SAAS,CAAA2B,QAAA,CAAEzD,SAAS,CAAC,SAAS,CAAE,CAAE+D,YAAY,CAAE,SAAU,CAAC,CAAC,CAAW,CAAC,EACnJ,CAAC,CACN,CAAC,EAGH,CACN,cAETnE,KAAA,QAAKoD,KAAK,CAAE,CAAEY,YAAY,CAAE,MAAO,CAAE,CAAAH,QAAA,eACnC/D,IAAA,QAAKsD,KAAK,CAAE,CAAEW,SAAS,CAAE,OAAO,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAH,QAAA,cAC9C7D,KAAA,SAAMoD,KAAK,CAAE,CAAEa,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAL,QAAA,EAAEzD,SAAS,CAAC,aAAa,CAAE,CAAE+D,YAAY,CAAE,aAAc,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,CAC/H,CAAC,cAENnE,KAAA,QAAKoE,SAAS,CAAE,cAAcrB,kBAAkB,CAACI,MAAM,CAAG,EAAE,CAAG,aAAa,CAAG,EAAE,EAAG,CAAAU,QAAA,eAClF/D,IAAA,aACE+F,WAAW,CACA,CAACrF,QAAQ,CAACwC,WAAW,EAAI,CAACjC,eAAe,CAAIX,SAAS,CAAC,mBAAmB,CAAE,CAAE+D,YAAY,CAAE,mBAAoB,CAAC,CAAC,CAAG,EAC/H,CACDjC,KAAK,CAAE1B,QAAQ,CAACwC,WAAW,EAAIjC,eAAe,EAAI,EAAG,CACrDsD,QAAQ,CAAGtC,CAAC,EAAK,CACftB,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEwC,WAAW,CAAEjB,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAAC,CACzDlB,cAAc,CAACe,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAChC,CAAE,CACFkB,KAAK,CAAE,CACLC,KAAK,CAAE,wBAAwB,CAC/BS,OAAO,CAAE,UAAU,CACnBP,YAAY,CAAE,KAAK,CACnBiC,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,MAAM,CACf1B,SAAS,CAAE,MAAM,CACjB+B,SAAS,CAAE,MACb,CAAE,CACH,CAAC,CAED/C,kBAAkB,CAACI,MAAM,CAAG,EAAE,eAC7BnD,KAAA,CAACN,UAAU,EACT0D,KAAK,CAAE,CACLqB,QAAQ,CAAE,MAAM,CAChBR,KAAK,CAAE,SAAS,CAChBF,SAAS,CAAE,MAAM,CACjBW,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPX,YAAY,CAAE,KAAK,CACnBY,OAAO,CAAE,MACX,CAAE,CAAAf,QAAA,eAEF/D,IAAA,SACEsD,KAAK,CAAE,CACLwB,OAAO,CAAE,MAAM,CACfH,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,QAAQ,CACpBC,WAAW,CAAE,KACf,CAAE,CACFC,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,OAAQ,CAAE,CAC9C,CAAC,CACOS,SAAS,CAAC,iDAAiD,CAAE,CAAE+D,YAAY,CAAE,iDAAkD,CAAC,CAAC,EAChI,CACb,EACE,CAAC,EACH,CAAC,EAGK,CAAC,cACNrE,IAAA,QAAKsD,KAAK,CAAE,CAACU,OAAO,CAAE,kBAAkB,CAAE,CAAAD,QAAA,cAC1C/D,IAAA,WACIiG,OAAO,CAAE1D,YAAa,CACtB4C,QAAQ,CAAE/B,sBAAuB,CACjCE,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbS,OAAO,CAAE,UAAU,CACnBR,eAAe,CAAE,qBAAqB,CAAE;AACxC0C,OAAO,CAAC9C,sBAAsB,CAAC,KAAK,CAAC,GAAG,CACxCe,KAAK,CAAE,MAAM,CACbR,MAAM,CAAE,MAAM,CACdF,YAAY,CAAE,KAAK,CACnB0C,MAAM,CAAE,SAAS,CACjBrB,OAAO,CAAC,OACV,CAAE,CAAAf,QAAA,CAEDnD,cAAc,CAAGN,SAAS,CAAC,QAAQ,CAAE,CAAE+D,YAAY,CAAE,QAAS,CAAC,CAAC,CAAG/D,SAAS,CAAC,QAAQ,CAAE,CAAE+D,YAAY,CAAE,QAAS,CAAC,CAAC,CAC7G,CAAC,CACJ,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}