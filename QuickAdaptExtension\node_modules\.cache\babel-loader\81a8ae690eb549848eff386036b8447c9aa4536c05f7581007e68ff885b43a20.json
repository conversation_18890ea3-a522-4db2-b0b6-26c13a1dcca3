{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import userManager from'./UseAuth';import jwt_decode from\"jwt-decode\";import{GetUserDetailsById}from'../../services/UserService';import{getRolesByUser}from'../../services/UserRoleService';import{jsx as _jsx}from\"react/jsx-runtime\";let userLocalData={};const AuthContext=/*#__PURE__*/createContext(undefined);let initialsData;export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[userDetails,setUserDetails]=useState(null);const[loggedOut,setLoggedOut]=useState(false);const[userRoles,setUserRoles]=useState({});useEffect(()=>{const initializeUser=async()=>{if(loggedOut)return;// Skip reinitialization if user has logged out\ntry{const user=await userManager.getUser();if(!user){// Try silent login if not authenticated\ntry{await userManager.signinSilent();}catch(e){// Silent login failed, will require interactive login\n}}if(user){setUser(user);if(user.access_token){var _userResponse$data,_userResponse$data$Us,_userResponse$data2;const decodedToken=jwt_decode(user.access_token);const userResponse=await GetUserDetailsById(decodedToken.UserId);setUserDetails((_userResponse$data=userResponse===null||userResponse===void 0?void 0:userResponse.data)!==null&&_userResponse$data!==void 0?_userResponse$data:null);GetUserRoles();const firstNameInitials=userResponse!==null&&userResponse!==void 0&&userResponse.data&&userResponse!==null&&userResponse!==void 0&&userResponse.data.FirstName?userResponse===null||userResponse===void 0?void 0:userResponse.data.FirstName.substring(0,1).toUpperCase():'';const lastNameinitials=userResponse!==null&&userResponse!==void 0&&userResponse.data&&userResponse!==null&&userResponse!==void 0&&userResponse.data.LastName?userResponse===null||userResponse===void 0?void 0:userResponse.data.LastName.substring(0,1).toUpperCase():'';const finalData=firstNameInitials+lastNameinitials;initialsData=finalData;localStorage.setItem(\"userType\",(_userResponse$data$Us=userResponse===null||userResponse===void 0?void 0:(_userResponse$data2=userResponse.data)===null||_userResponse$data2===void 0?void 0:_userResponse$data2.UserType)!==null&&_userResponse$data$Us!==void 0?_userResponse$data$Us:\"\");userLocalData[\"user\"]=JSON.stringify(userResponse===null||userResponse===void 0?void 0:userResponse.data);localStorage.setItem(\"userInfo\",JSON.stringify(userLocalData));}}}catch(error){console.error('Failed to fetch user details:',error);userManager.signoutRedirect();}};initializeUser();userManager.events.addUserLoaded(async loadedUser=>{if(loggedOut)return;setUser(loadedUser);if(loadedUser.access_token){var _userResponse$data3;const decodedToken=jwt_decode(loadedUser.access_token);const userResponse=await GetUserDetailsById(decodedToken.UserId);setUserDetails((_userResponse$data3=userResponse===null||userResponse===void 0?void 0:userResponse.data)!==null&&_userResponse$data3!==void 0?_userResponse$data3:null);GetUserRoles();const firstNameInitials=userResponse!==null&&userResponse!==void 0&&userResponse.data&&userResponse!==null&&userResponse!==void 0&&userResponse.data.FirstName?userResponse===null||userResponse===void 0?void 0:userResponse.data.FirstName.substring(0,1).toUpperCase():'';const lastNameinitials=userResponse!==null&&userResponse!==void 0&&userResponse.data&&userResponse!==null&&userResponse!==void 0&&userResponse.data.LastName?userResponse===null||userResponse===void 0?void 0:userResponse.data.LastName.substring(0,1).toUpperCase():'';const finalData=firstNameInitials+lastNameinitials;initialsData=finalData;userLocalData[\"oidc-info\"]=JSON.stringify(loadedUser);userLocalData[\"user\"]=JSON.stringify(userResponse===null||userResponse===void 0?void 0:userResponse.data);localStorage.setItem(\"userInfo\",JSON.stringify(userLocalData));}});userManager.events.addUserUnloaded(()=>{setUser(null);setUserDetails(null);localStorage.clear();document.cookie.split(\";\").forEach(cookie=>{const[name]=cookie.split(\"=\");document.cookie=`${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;});setLoggedOut(true);// Indicate the user has logged out\nconst redirectPath=process.env.REACT_APP_IDS_API;// if (redirectPath) {\n//   window.location.href = redirectPath;\n// }\n});},[]);const GetUserRoles=async()=>{try{const rolesData=await getRolesByUser();// console.log(rolesData);\nconst dist=rolesData.reduce((acc,curr)=>{if(!acc[curr.AccountId]){acc[curr.AccountId]=[];}if(!acc[curr.AccountId].includes(curr.RoleName)){acc[curr.AccountId].push(curr.RoleName);}return acc;},{});setUserRoles(dist);}catch(e){}};const signOut=()=>{const logeduserType=localStorage.getItem('userType');if((logeduserType===null||logeduserType===void 0?void 0:logeduserType.toLowerCase())!==\"superadmin\"){setUser(null);setUserDetails(null);localStorage.clear();document.cookie.split(\";\").forEach(cookie=>{const[name]=cookie.split(\"=\");document.cookie=`${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;});setLoggedOut(true);localStorage.setItem('logout-event',Date.now().toString());sessionStorage.clear();userManager.signoutRedirect();}else{sessionStorage.clear();}};const signIn=()=>{setLoggedOut(false);userManager.signinRedirect();};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:{user,userDetails,signOut,loggedOut,userRoles,setUserRoles},children:children});};export const useAuth=()=>{var _context;let context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}if((_context=context)!==null&&_context!==void 0&&_context.user){const userInfo=JSON.parse(localStorage.getItem(\"userInfo\")||'{}');userLocalData[\"oidc-info\"]=JSON.stringify(context.user);if(userInfo['user']){userLocalData[\"user\"]=JSON.stringify(userInfo['user']);}}else{const userInfo=JSON.parse(localStorage.getItem(\"userInfo\")||'{}');if(userInfo['oidc-info']&&userInfo['user']){context={...context,user:JSON.parse(userInfo['oidc-info'])};context.userDetails=JSON.parse(userInfo['user']);context.loggedOut=false;}}return context;};export{initialsData};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "userManager", "jwt_decode", "GetUserDetailsById", "getRolesByUser", "jsx", "_jsx", "userLocalData", "AuthContext", "undefined", "initialsData", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "userDetails", "setUserDetails", "loggedOut", "setLoggedOut", "userRoles", "setUserRoles", "initializeUser", "getUser", "signinSilent", "e", "access_token", "_userResponse$data", "_userResponse$data$Us", "_userResponse$data2", "decodedToken", "userResponse", "UserId", "data", "GetUserRoles", "firstNameInitials", "FirstName", "substring", "toUpperCase", "lastNameinitials", "LastName", "finalData", "localStorage", "setItem", "UserType", "JSON", "stringify", "error", "console", "signoutRedirect", "events", "addUserLoaded", "loadedUser", "_userResponse$data3", "addUserUnloaded", "clear", "document", "cookie", "split", "for<PERSON>ach", "name", "redirectPath", "process", "env", "REACT_APP_IDS_API", "rolesData", "dist", "reduce", "acc", "curr", "AccountId", "includes", "RoleName", "push", "signOut", "logeduserType", "getItem", "toLowerCase", "Date", "now", "toString", "sessionStorage", "signIn", "signinRedirect", "Provider", "value", "useAuth", "_context", "context", "Error", "userInfo", "parse"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/auth/AuthProvider.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { User, UserManager } from 'oidc-client-ts';\r\nimport userManager from './UseAuth';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { User as LoginUser } from '../../models/User';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport { getRolesByUser } from '../../services/UserRoleService';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  userDetails: LoginUser | null;\r\n  signOut: () => void;\r\n  loggedOut: boolean;\r\n  userRoles: any;\r\n  setUserRoles: any;\r\n}\r\nlet userLocalData: { [key: string]: any } = {}\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\nlet initialsData: string;\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [userDetails, setUserDetails] = useState<LoginUser | null>(null);\r\n  const [loggedOut, setLoggedOut] = useState<boolean>(false);\r\n  const [userRoles, setUserRoles] = useState({});\r\n\r\n  useEffect(() => {\r\n    const initializeUser = async () => {\r\n      if (loggedOut) return;  // Skip reinitialization if user has logged out\r\n\r\n      try {\r\n        const user = await userManager.getUser();\r\n        if (!user) {\r\n          // Try silent login if not authenticated\r\n          try {\r\n            await userManager.signinSilent();\r\n          } catch (e) {\r\n            // Silent login failed, will require interactive login\r\n          }\r\n        }\r\n        if (user) {\r\n          setUser(user);\r\n  \r\n          if (user.access_token) {     \r\n            const decodedToken = jwt_decode<LoginUserInfo>(user.access_token);\r\n            const userResponse = await GetUserDetailsById(decodedToken.UserId);\r\n            setUserDetails(userResponse?.data ?? null);\r\n            GetUserRoles();\r\n            const firstNameInitials =  userResponse?.data && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n            const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n            const finalData = firstNameInitials + lastNameinitials;\r\n            initialsData = finalData;\r\n            localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");\r\n            userLocalData[\"user\"] = JSON.stringify(userResponse?.data)\r\n            localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\r\n\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to fetch user details:', error);\r\n        userManager.signoutRedirect();\r\n      }\r\n    };  \r\n    initializeUser();  \r\n    userManager.events.addUserLoaded(async (loadedUser) => {\r\n      if (loggedOut) return; \r\n      setUser(loadedUser);\r\n      if (loadedUser.access_token) {\r\n        const decodedToken = jwt_decode<LoginUserInfo>(loadedUser.access_token);\r\n        const userResponse = await GetUserDetailsById(decodedToken.UserId);\r\n        setUserDetails(userResponse?.data ?? null);\r\n        GetUserRoles();\r\n        const firstNameInitials =   userResponse?.data && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n          const lastNameinitials =  userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n          const finalData = firstNameInitials + lastNameinitials;\r\n          initialsData = finalData;\r\n        userLocalData[\"oidc-info\"] = JSON.stringify(loadedUser);    \r\n        userLocalData[\"user\"]= JSON.stringify(userResponse?.data )       \r\n        localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\r\n\r\n      }\r\n    });\r\n  \r\n    userManager.events.addUserUnloaded(() => {\r\n      setUser(null);\r\n      setUserDetails(null);\r\n      localStorage.clear();\r\n      document.cookie.split(\";\").forEach(cookie => {\r\n        const [name] = cookie.split(\"=\");\r\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n      });\r\n      setLoggedOut(true);  // Indicate the user has logged out\r\n      const redirectPath = process.env.REACT_APP_IDS_API;\r\n      // if (redirectPath) {\r\n      //   window.location.href = redirectPath;\r\n      // }\r\n    });\r\n  }, []);\r\n\r\n\r\n  const GetUserRoles = async () => {\r\n    try {\r\n      const rolesData = await getRolesByUser();\r\n      // console.log(rolesData);\r\n      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {\r\n        if (!acc[curr.AccountId]) {\r\n          acc[curr.AccountId] = [];\r\n        }\r\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\r\n          acc[curr.AccountId].push(curr.RoleName);\r\n        }\r\n        return acc;\r\n      }, {});\r\n\r\n      setUserRoles(dist);\r\n              \r\n      \r\n    } catch (e) {\r\n      \r\n    }\r\n  }\r\n\r\n  const signOut = () => {\r\n    const logeduserType = localStorage.getItem('userType');\r\n    if (logeduserType?.toLowerCase()!== \"superadmin\") {      \r\n      setUser(null);\r\n      setUserDetails(null);\r\n      localStorage.clear();\r\n      document.cookie.split(\";\").forEach(cookie => {\r\n        const [name] = cookie.split(\"=\");\r\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n      });\r\n      setLoggedOut(true);  \r\n      localStorage.setItem('logout-event', Date.now().toString());\r\n      sessionStorage.clear()\r\n      userManager.signoutRedirect();    \r\n    }\r\n    else {      \r\n      sessionStorage.clear()\r\n    }\r\n  };\r\n  \r\n  const signIn = () => {\r\n    setLoggedOut(false);  \r\n    userManager.signinRedirect();\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={{ user, userDetails, signOut,loggedOut,userRoles,setUserRoles }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  let context = useContext(AuthContext);\r\n\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  if (context?.user) {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    userLocalData[\"oidc-info\"] = JSON.stringify(context.user);    \r\n\r\n    if (userInfo['user']) {\r\n      userLocalData[\"user\"] =  JSON.stringify(userInfo['user'])\r\n    }   \r\n  } \r\n  else {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    if (userInfo['oidc-info'] && userInfo['user']) {\r\n      context = { ...context, user: JSON.parse(userInfo['oidc-info']) };\r\n      context.userDetails =  JSON.parse(userInfo['user'])\r\n      context.loggedOut = false;\r\n    }\r\n  }\r\n\r\n  return context;\r\n};\r\n\r\nexport {initialsData}"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAmB,OAAO,CAExF,MAAO,CAAAC,WAAW,KAAM,WAAW,CAEnC,MAAO,CAAAC,UAAU,KAAM,YAAY,CAGnC,OAASC,kBAAkB,KAAQ,4BAA4B,CAC/D,OAASC,cAAc,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAUhE,GAAI,CAAAC,aAAqC,CAAG,CAAC,CAAC,CAC9C,KAAM,CAAAC,WAAW,cAAGX,aAAa,CAA8BY,SAAS,CAAC,CAKzE,GAAI,CAAAC,YAAoB,CACxB,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGf,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAmB,IAAI,CAAC,CACtE,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAU,KAAK,CAAC,CAC1D,KAAM,CAACoB,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE9CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuB,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAIJ,SAAS,CAAE,OAAS;AAExB,GAAI,CACF,KAAM,CAAAJ,IAAI,CAAG,KAAM,CAAAb,WAAW,CAACsB,OAAO,CAAC,CAAC,CACxC,GAAI,CAACT,IAAI,CAAE,CACT;AACA,GAAI,CACF,KAAM,CAAAb,WAAW,CAACuB,YAAY,CAAC,CAAC,CAClC,CAAE,MAAOC,CAAC,CAAE,CACV;AAAA,CAEJ,CACA,GAAIX,IAAI,CAAE,CACRC,OAAO,CAACD,IAAI,CAAC,CAEb,GAAIA,IAAI,CAACY,YAAY,CAAE,KAAAC,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CACrB,KAAM,CAAAC,YAAY,CAAG5B,UAAU,CAAgBY,IAAI,CAACY,YAAY,CAAC,CACjE,KAAM,CAAAK,YAAY,CAAG,KAAM,CAAA5B,kBAAkB,CAAC2B,YAAY,CAACE,MAAM,CAAC,CAClEf,cAAc,EAAAU,kBAAA,CAACI,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,UAAAN,kBAAA,UAAAA,kBAAA,CAAI,IAAI,CAAC,CAC1CO,YAAY,CAAC,CAAC,CACd,KAAM,CAAAC,iBAAiB,CAAIJ,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,EAAIF,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAAGL,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,EAAE,CAC/I,KAAM,CAAAC,gBAAgB,CAAGR,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,EAAIF,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAAGT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,EAAE,CAC3I,KAAM,CAAAG,SAAS,CAAGN,iBAAiB,CAAGI,gBAAgB,CACtD7B,YAAY,CAAG+B,SAAS,CACxBC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAAf,qBAAA,CAAEG,YAAY,SAAZA,YAAY,kBAAAF,mBAAA,CAAZE,YAAY,CAAEE,IAAI,UAAAJ,mBAAA,iBAAlBA,mBAAA,CAAoBe,QAAQ,UAAAhB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACpErB,aAAa,CAAC,MAAM,CAAC,CAAGsC,IAAI,CAACC,SAAS,CAACf,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,CAAC,CAC1DS,YAAY,CAACC,OAAO,CAAC,UAAU,CAAEE,IAAI,CAACC,SAAS,CAACvC,aAAa,CAAC,CAAC,CAEjE,CACF,CACF,CAAE,MAAOwC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD9C,WAAW,CAACgD,eAAe,CAAC,CAAC,CAC/B,CACF,CAAC,CACD3B,cAAc,CAAC,CAAC,CAChBrB,WAAW,CAACiD,MAAM,CAACC,aAAa,CAAC,KAAO,CAAAC,UAAU,EAAK,CACrD,GAAIlC,SAAS,CAAE,OACfH,OAAO,CAACqC,UAAU,CAAC,CACnB,GAAIA,UAAU,CAAC1B,YAAY,CAAE,KAAA2B,mBAAA,CAC3B,KAAM,CAAAvB,YAAY,CAAG5B,UAAU,CAAgBkD,UAAU,CAAC1B,YAAY,CAAC,CACvE,KAAM,CAAAK,YAAY,CAAG,KAAM,CAAA5B,kBAAkB,CAAC2B,YAAY,CAACE,MAAM,CAAC,CAClEf,cAAc,EAAAoC,mBAAA,CAACtB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,UAAAoB,mBAAA,UAAAA,mBAAA,CAAI,IAAI,CAAC,CAC1CnB,YAAY,CAAC,CAAC,CACd,KAAM,CAAAC,iBAAiB,CAAKJ,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,EAAIF,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAAGL,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,EAAE,CAC9I,KAAM,CAAAC,gBAAgB,CAAIR,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,EAAIF,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAAGT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,EAAE,CAC5I,KAAM,CAAAG,SAAS,CAAGN,iBAAiB,CAAGI,gBAAgB,CACtD7B,YAAY,CAAG+B,SAAS,CAC1BlC,aAAa,CAAC,WAAW,CAAC,CAAGsC,IAAI,CAACC,SAAS,CAACM,UAAU,CAAC,CACvD7C,aAAa,CAAC,MAAM,CAAC,CAAEsC,IAAI,CAACC,SAAS,CAACf,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEE,IAAK,CAAC,CAC1DS,YAAY,CAACC,OAAO,CAAC,UAAU,CAAEE,IAAI,CAACC,SAAS,CAACvC,aAAa,CAAC,CAAC,CAEjE,CACF,CAAC,CAAC,CAEFN,WAAW,CAACiD,MAAM,CAACI,eAAe,CAAC,IAAM,CACvCvC,OAAO,CAAC,IAAI,CAAC,CACbE,cAAc,CAAC,IAAI,CAAC,CACpByB,YAAY,CAACa,KAAK,CAAC,CAAC,CACpBC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACF,MAAM,EAAI,CAC3C,KAAM,CAACG,IAAI,CAAC,CAAGH,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAChCF,QAAQ,CAACC,MAAM,CAAG,GAAGG,IAAI,mDAAmD,CAC9E,CAAC,CAAC,CACFzC,YAAY,CAAC,IAAI,CAAC,CAAG;AACrB,KAAM,CAAA0C,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAClD;AACA;AACA;AACF,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAGN,KAAM,CAAA9B,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAA+B,SAAS,CAAG,KAAM,CAAA7D,cAAc,CAAC,CAAC,CACxC;AACA,KAAM,CAAA8D,IAAI,CAAGD,SAAS,CAACE,MAAM,CAAC,CAACC,GAA4B,CAAEC,IAAoD,GAAK,CACpH,GAAI,CAACD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAAE,CACxBF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAAG,EAAE,CAC1B,CACA,GAAI,CAACF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,CAAE,CAChDJ,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC,CACzC,CACA,MAAO,CAAAJ,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN/C,YAAY,CAAC6C,IAAI,CAAC,CAGpB,CAAE,MAAOzC,CAAC,CAAE,CAEZ,CACF,CAAC,CAED,KAAM,CAAAiD,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAAC,aAAa,CAAGjC,YAAY,CAACkC,OAAO,CAAC,UAAU,CAAC,CACtD,GAAI,CAAAD,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEE,WAAW,CAAC,CAAC,IAAI,YAAY,CAAE,CAChD9D,OAAO,CAAC,IAAI,CAAC,CACbE,cAAc,CAAC,IAAI,CAAC,CACpByB,YAAY,CAACa,KAAK,CAAC,CAAC,CACpBC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACF,MAAM,EAAI,CAC3C,KAAM,CAACG,IAAI,CAAC,CAAGH,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAChCF,QAAQ,CAACC,MAAM,CAAG,GAAGG,IAAI,mDAAmD,CAC9E,CAAC,CAAC,CACFzC,YAAY,CAAC,IAAI,CAAC,CAClBuB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAEmC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAC3DC,cAAc,CAAC1B,KAAK,CAAC,CAAC,CACtBtD,WAAW,CAACgD,eAAe,CAAC,CAAC,CAC/B,CAAC,IACI,CACHgC,cAAc,CAAC1B,KAAK,CAAC,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA2B,MAAM,CAAGA,CAAA,GAAM,CACnB/D,YAAY,CAAC,KAAK,CAAC,CACnBlB,WAAW,CAACkF,cAAc,CAAC,CAAC,CAC9B,CAAC,CAED,mBACE7E,IAAA,CAACE,WAAW,CAAC4E,QAAQ,EAACC,KAAK,CAAE,CAAEvE,IAAI,CAAEE,WAAW,CAAE0D,OAAO,CAACxD,SAAS,CAACE,SAAS,CAACC,YAAa,CAAE,CAAAR,QAAA,CAC1FA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,MAAO,MAAM,CAAAyE,OAAO,CAAGA,CAAA,GAAuB,KAAAC,QAAA,CAC5C,GAAI,CAAAC,OAAO,CAAG1F,UAAU,CAACU,WAAW,CAAC,CAErC,GAAIgF,OAAO,GAAK/E,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAgF,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,IAAAF,QAAA,CAAIC,OAAO,UAAAD,QAAA,WAAPA,QAAA,CAASzE,IAAI,CAAE,CACjB,KAAM,CAAA4E,QAAQ,CAAG7C,IAAI,CAAC8C,KAAK,CAACjD,YAAY,CAACkC,OAAO,CAAC,UAAU,CAAC,EAAI,IAAI,CAAC,CACrErE,aAAa,CAAC,WAAW,CAAC,CAAGsC,IAAI,CAACC,SAAS,CAAC0C,OAAO,CAAC1E,IAAI,CAAC,CAEzD,GAAI4E,QAAQ,CAAC,MAAM,CAAC,CAAE,CACpBnF,aAAa,CAAC,MAAM,CAAC,CAAIsC,IAAI,CAACC,SAAS,CAAC4C,QAAQ,CAAC,MAAM,CAAC,CAAC,CAC3D,CACF,CAAC,IACI,CACH,KAAM,CAAAA,QAAQ,CAAG7C,IAAI,CAAC8C,KAAK,CAACjD,YAAY,CAACkC,OAAO,CAAC,UAAU,CAAC,EAAI,IAAI,CAAC,CACrE,GAAIc,QAAQ,CAAC,WAAW,CAAC,EAAIA,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC7CF,OAAO,CAAG,CAAE,GAAGA,OAAO,CAAE1E,IAAI,CAAE+B,IAAI,CAAC8C,KAAK,CAACD,QAAQ,CAAC,WAAW,CAAC,CAAE,CAAC,CACjEF,OAAO,CAACxE,WAAW,CAAI6B,IAAI,CAAC8C,KAAK,CAACD,QAAQ,CAAC,MAAM,CAAC,CAAC,CACnDF,OAAO,CAACtE,SAAS,CAAG,KAAK,CAC3B,CACF,CAEA,MAAO,CAAAsE,OAAO,CAChB,CAAC,CAED,OAAQ9E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}