{"ast": null, "code": "import{create}from\"zustand\";import{devtools,persist}from\"zustand/middleware\";import{immer}from\"zustand/middleware/immer\";// Define the UserInfo interface for state\n// Zustand store with typed state\nconst useInfoStore=create()(devtools(persist(immer(set=>({accessToken:\"\",oidcInfo:{},user:{},orgDetails:{},userType:\"\",orgLanguages:[],// <-- add this\nuserRoles:{},setAccessToken:token=>{set(state=>{state.accessToken=token;});},clearAccessToken:()=>{set(state=>{state.accessToken=\"\";});},setOidcInfo:info=>{set(state=>{state.oidcInfo=info;});},setUser:user=>{set(state=>{state.user=user;});},setOrgDetails:orgDetails=>{set(state=>{state.orgDetails=orgDetails;});},clearAll:()=>{// Reset all fields to initial values\nset(state=>{state.accessToken=\"\";state.oidcInfo={};state.user=null;state.orgDetails={};state.userType=\"\";state.orgLanguages=[];// <-- add this\nstate.userRoles={};});},setUserType:userType=>{set(state=>{state.userType=userType;});},setOrgLanguages:languages=>{set(state=>{state.orgLanguages=languages;});},setUserRoles:userRoles=>{set(state=>{state.userRoles=userRoles;});}})),{name:\"user-info-storage\",// unique name for localStorage\npartialize:state=>({accessToken:state.accessToken,oidcInfo:state.oidcInfo,user:state.user,orgDetails:state.orgDetails,userType:state.userType,orgLanguages:state.orgLanguages,// <-- add this\nuserRoles:state.userRoles})// Persist these fields\n})));export default useInfoStore;", "map": {"version": 3, "names": ["create", "devtools", "persist", "immer", "useInfoStore", "set", "accessToken", "oidcInfo", "user", "orgDetails", "userType", "orgLanguages", "userRoles", "setAccessToken", "token", "state", "clearAccessToken", "setOidcInfo", "info", "setUser", "setOrgDetails", "clearAll", "setUserType", "setOrgLanguages", "languages", "setUserRoles", "name", "partialize"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/store/UserInfoStore.ts"], "sourcesContent": ["import { LoginUserInfo } from './../models/LoginUserInfo';\r\nimport { create } from \"zustand\";\r\nimport { devtools, persist } from \"zustand/middleware\";\r\nimport { immer } from \"zustand/middleware/immer\";\r\nimport { User } from '../models/User';\r\nimport { Organization } from '../models/Organization';\r\n\r\n// Define the UserInfo interface for state\r\ninterface UserInfo {\r\n  accessToken: string;\r\n  oidcInfo: any;\r\n  user: User | null;\r\n  orgDetails: Organization;\r\n  userType: string; // added userType here\r\n  orgLanguages: string[]; // <-- add this\r\n  userRoles: any;\r\n  setAccessToken: (token: string) => void;\r\n  clearAccessToken: () => void;\r\n  setOidcInfo: (info: any) => void;\r\n  setUser: (user: User) => void;\r\n  setOrgDetails: (orgDetails: Organization) => void;\r\n  setUserType: (userType: string) => void;\r\n  setOrgLanguages: (languages: string[]) => void; // <-- add this\r\n  clearAll: () => void; \r\n  setUserRoles: (userRoles: any) => void;\r\n}\r\n\r\n// Zustand store with typed state\r\nconst useInfoStore = create<UserInfo>()(\r\n  devtools(\r\n    persist(\r\n      immer((set) => ({\r\n        accessToken: \"\",\r\n        oidcInfo: {},\r\n        user: {} as User,\r\n        orgDetails: {} as Organization,\r\n        userType: \"\",\r\n        orgLanguages: [], // <-- add this\r\n        userRoles: {} as any,\r\n\r\n        setAccessToken: (token: string) => {\r\n          set((state) => {\r\n            state.accessToken = token;\r\n          });\r\n        },\r\n\r\n        clearAccessToken: () => {\r\n          set((state) => {\r\n            state.accessToken = \"\";\r\n          });\r\n        },\r\n            \r\n        setOidcInfo: (info: any) => {\r\n          set((state) => {\r\n            state.oidcInfo = info;\r\n          });\r\n        },\r\n\r\n        setUser: (user: User | null) => {\r\n          set((state) => {\r\n            state.user = user;\r\n          });\r\n        },\r\n\r\n        setOrgDetails: (orgDetails: Organization) => {\r\n          set((state) => {\r\n            state.orgDetails = orgDetails;\r\n          });\r\n        },\r\n        clearAll: () => {  // Reset all fields to initial values\r\n            set((state) => {\r\n              state.accessToken = \"\";\r\n              state.oidcInfo = {};\r\n              state.user = null;\r\n              state.orgDetails = {} as Organization;\r\n              state.userType = \"\";\r\n              state.orgLanguages = []; // <-- add this\r\n              state.userRoles = {};\r\n            });\r\n          },\r\n        setUserType: (userType: string) => {\r\n          set((state) => {\r\n            state.userType = userType;\r\n          });\r\n        },\r\n        setOrgLanguages: (languages: string[]) => {\r\n          set((state) => {\r\n            state.orgLanguages = languages;\r\n          });\r\n        },\r\n        setUserRoles: (userRoles: any) => {\r\n          set((state) => {\r\n            state.userRoles = userRoles;\r\n          })\r\n        }\r\n      })),\r\n      {\r\n        name: \"user-info-storage\", // unique name for localStorage\r\n        partialize: (state) => ({\r\n          accessToken: state.accessToken,\r\n          oidcInfo: state.oidcInfo,\r\n          user: state.user,\r\n          orgDetails: state.orgDetails,\r\n          userType: state.userType,\r\n          orgLanguages: state.orgLanguages, // <-- add this\r\n          userRoles: state.userRoles,\r\n        }), // Persist these fields\r\n      }\r\n    )\r\n  )\r\n);\r\n\r\nexport default useInfoStore;\r\n"], "mappings": "AACA,OAASA,MAAM,KAAQ,SAAS,CAChC,OAASC,QAAQ,CAAEC,OAAO,KAAQ,oBAAoB,CACtD,OAASC,KAAK,KAAQ,0BAA0B,CAIhD;AAoBA;AACA,KAAM,CAAAC,YAAY,CAAGJ,MAAM,CAAW,CAAC,CACrCC,QAAQ,CACNC,OAAO,CACLC,KAAK,CAAEE,GAAG,GAAM,CACdC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,CAAC,CAAC,CACZC,IAAI,CAAE,CAAC,CAAS,CAChBC,UAAU,CAAE,CAAC,CAAiB,CAC9BC,QAAQ,CAAE,EAAE,CACZC,YAAY,CAAE,EAAE,CAAE;AAClBC,SAAS,CAAE,CAAC,CAAQ,CAEpBC,cAAc,CAAGC,KAAa,EAAK,CACjCT,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACT,WAAW,CAAGQ,KAAK,CAC3B,CAAC,CAAC,CACJ,CAAC,CAEDE,gBAAgB,CAAEA,CAAA,GAAM,CACtBX,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACT,WAAW,CAAG,EAAE,CACxB,CAAC,CAAC,CACJ,CAAC,CAEDW,WAAW,CAAGC,IAAS,EAAK,CAC1Bb,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACR,QAAQ,CAAGW,IAAI,CACvB,CAAC,CAAC,CACJ,CAAC,CAEDC,OAAO,CAAGX,IAAiB,EAAK,CAC9BH,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACP,IAAI,CAAGA,IAAI,CACnB,CAAC,CAAC,CACJ,CAAC,CAEDY,aAAa,CAAGX,UAAwB,EAAK,CAC3CJ,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACN,UAAU,CAAGA,UAAU,CAC/B,CAAC,CAAC,CACJ,CAAC,CACDY,QAAQ,CAAEA,CAAA,GAAM,CAAG;AACfhB,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACT,WAAW,CAAG,EAAE,CACtBS,KAAK,CAACR,QAAQ,CAAG,CAAC,CAAC,CACnBQ,KAAK,CAACP,IAAI,CAAG,IAAI,CACjBO,KAAK,CAACN,UAAU,CAAG,CAAC,CAAiB,CACrCM,KAAK,CAACL,QAAQ,CAAG,EAAE,CACnBK,KAAK,CAACJ,YAAY,CAAG,EAAE,CAAE;AACzBI,KAAK,CAACH,SAAS,CAAG,CAAC,CAAC,CACtB,CAAC,CAAC,CACJ,CAAC,CACHU,WAAW,CAAGZ,QAAgB,EAAK,CACjCL,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACL,QAAQ,CAAGA,QAAQ,CAC3B,CAAC,CAAC,CACJ,CAAC,CACDa,eAAe,CAAGC,SAAmB,EAAK,CACxCnB,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACJ,YAAY,CAAGa,SAAS,CAChC,CAAC,CAAC,CACJ,CAAC,CACDC,YAAY,CAAGb,SAAc,EAAK,CAChCP,GAAG,CAAEU,KAAK,EAAK,CACbA,KAAK,CAACH,SAAS,CAAGA,SAAS,CAC7B,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAAC,CACH,CACEc,IAAI,CAAE,mBAAmB,CAAE;AAC3BC,UAAU,CAAGZ,KAAK,GAAM,CACtBT,WAAW,CAAES,KAAK,CAACT,WAAW,CAC9BC,QAAQ,CAAEQ,KAAK,CAACR,QAAQ,CACxBC,IAAI,CAAEO,KAAK,CAACP,IAAI,CAChBC,UAAU,CAAEM,KAAK,CAACN,UAAU,CAC5BC,QAAQ,CAAEK,KAAK,CAACL,QAAQ,CACxBC,YAAY,CAAEI,KAAK,CAACJ,YAAY,CAAE;AAClCC,SAAS,CAAEG,KAAK,CAACH,SACnB,CAAC,CAAG;AACN,CACF,CACF,CACF,CAAC,CAED,cAAe,CAAAR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}