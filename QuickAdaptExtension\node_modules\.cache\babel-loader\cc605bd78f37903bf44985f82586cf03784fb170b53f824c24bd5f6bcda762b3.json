{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AIAgent\\\\CreateWithAIButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from 'react';\nimport { ai } from '../../assets/icons/icons';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { useTranslation } from 'react-i18next';\nimport { AccountContext } from '../login/AccountContext';\nimport useInfoStore from '../../store/UserInfoStore';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreateWithAIButton = ({\n  onClick\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    isCollapsed,\n    setIsCollapsed\n  } = useDrawerStore(state => state);\n  const [isSelected, setIsSelected] = useState(false);\n  const {\n    accountId,\n    roles\n  } = useContext(AccountContext);\n  const userType = useInfoStore(state => state.userType);\n  const handleClick = () => {\n    setIsSelected(!isSelected); // toggle active state\n    onClick(); // your original click logic\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isCollapsed ? /*#__PURE__*/_jsxDEV(\"button\", {\n      className: `qadpt-ai-button ${isCollapsed && isSelected ? 'active' : ''}`,\n      onClick: onClick,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"qadpt-aiicon\",\n        dangerouslySetInnerHTML: {\n          __html: ai\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-ai-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"beta\",\n        children: translate(\"BETA\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-ai-title\",\n        children: translate(\"Create interactions using the latest AI technology.\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"qadpt-button\",\n        onClick: onClick,\n        disabled: userType.toLocaleLowerCase() != \"admin\" ? roles == null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)) : false,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"qadpt-icon\",\n          dangerouslySetInnerHTML: {\n            __html: ai\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"qadpt-text\",\n          children: translate(\"Create with AI\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(CreateWithAIButton, \"PUdn0v9tMYq8tA8zCAUlUkc34kk=\", false, function () {\n  return [useTranslation, useDrawerStore, useInfoStore];\n});\n_c = CreateWithAIButton;\nexport default CreateWithAIButton;\nvar _c;\n$RefreshReg$(_c, \"CreateWithAIButton\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "ai", "useDrawerStore", "useTranslation", "AccountContext", "useInfoStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateWithAIButton", "onClick", "_s", "t", "translate", "isCollapsed", "setIsCollapsed", "state", "isSelected", "setIsSelected", "accountId", "roles", "userType", "handleClick", "children", "className", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "toLocaleLowerCase", "some", "role", "includes", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/AIAgent/CreateWithAIButton.tsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { ai, beta } from '../../assets/icons/icons';\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport { AccountContext } from '../login/AccountContext';\r\nimport useInfoStore from '../../store/UserInfoStore';\r\n\r\ninterface CreateWithAIButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst CreateWithAIButton: React.FC<CreateWithAIButtonProps> = ({ onClick }) => {\r\n  const { t: translate } = useTranslation();\r\n  const {\r\n    isCollapsed,\r\n    setIsCollapsed,\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [isSelected, setIsSelected] = useState(false);\r\n  const { accountId, roles } = useContext(AccountContext);\r\n  const userType = useInfoStore((state) => state.userType); \r\n\r\n  const handleClick = () => {\r\n    setIsSelected(!isSelected); // toggle active state\r\n    onClick(); // your original click logic\r\n  };\r\n  return (\r\n    <>\r\n      {isCollapsed ? (\r\n        <button\r\n          className={`qadpt-ai-button ${isCollapsed && isSelected ? 'active' : ''}`}\r\n          onClick={onClick}\r\n        >\r\n          <span className=\"qadpt-aiicon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n        </button>\r\n     \r\n      ) : (\r\n     \r\n        <div className=\"qadpt-ai-container\">\r\n            <div className=\"beta\">{translate(\"BETA\")}</div>\r\n          <div className=\"qadpt-ai-title\">\r\n              {translate(\"Create interactions using the latest AI technology.\")}\r\n          </div>\r\n          <button className=\"qadpt-button\" onClick={onClick} disabled={userType.toLocaleLowerCase()!=\"admin\" ? roles==null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)): false}>\r\n            <span className=\"qadpt-icon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n              <span className=\"qadpt-text\">{translate(\"Create with AI\")}</span>\r\n          </button>\r\n        </div>\r\n       \r\n      )}\r\n       \r\n    </>\r\n  );\r\n  \r\n};\r\n\r\nexport default CreateWithAIButton;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,EAAE,QAAc,0BAA0B;AACnD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMrD,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;EACzC,MAAM;IACJY,WAAW;IACXC;EACF,CAAC,GAAGd,cAAc,CAAEe,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IAAEoB,SAAS;IAAEC;EAAM,CAAC,GAAGtB,UAAU,CAACK,cAAc,CAAC;EACvD,MAAMkB,QAAQ,GAAGjB,YAAY,CAAEY,KAAK,IAAKA,KAAK,CAACK,QAAQ,CAAC;EAExD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBJ,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC;IAC5BP,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,oBACEJ,OAAA,CAAAE,SAAA;IAAAe,QAAA,EACGT,WAAW,gBACVR,OAAA;MACEkB,SAAS,EAAE,mBAAmBV,WAAW,IAAIG,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;MAC1EP,OAAO,EAAEA,OAAQ;MAAAa,QAAA,eAEjBjB,OAAA;QAAMkB,SAAS,EAAC,cAAc;QAACC,uBAAuB,EAAE;UAAEC,MAAM,EAAE1B;QAAG;MAAE;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,gBAITxB,OAAA;MAAKkB,SAAS,EAAC,oBAAoB;MAAAD,QAAA,gBAC/BjB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAD,QAAA,EAAEV,SAAS,CAAC,MAAM;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjDxB,OAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAC1BV,SAAS,CAAC,qDAAqD;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNxB,OAAA;QAAQkB,SAAS,EAAC,cAAc;QAACd,OAAO,EAAEA,OAAQ;QAACqB,QAAQ,EAAEV,QAAQ,CAACW,iBAAiB,CAAC,CAAC,IAAE,OAAO,GAAGZ,KAAK,IAAE,IAAI,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,CAACa,IAAI,CAACC,IAAI,IAAId,KAAK,CAACe,QAAQ,CAACD,IAAI,CAAC,CAAC,GAAE,KAAM;QAAAX,QAAA,gBACnMjB,OAAA;UAAMkB,SAAS,EAAC,YAAY;UAACC,uBAAuB,EAAE;YAAEC,MAAM,EAAE1B;UAAG;QAAE;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtExB,OAAA;UAAMkB,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAEV,SAAS,CAAC,gBAAgB;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAEN,gBAED,CAAC;AAGP,CAAC;AAACnB,EAAA,CA1CIF,kBAAqD;EAAA,QAChCP,cAAc,EAInCD,cAAc,EAGDG,YAAY;AAAA;AAAAgC,EAAA,GARzB3B,kBAAqD;AA4C3D,eAAeA,kBAAkB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}