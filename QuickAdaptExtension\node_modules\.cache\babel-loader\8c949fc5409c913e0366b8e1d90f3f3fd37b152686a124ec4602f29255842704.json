{"ast": null, "code": "import React,{useEffect,useState}from'react';import\"./App.scss\";import Drawer from\"./components/drawer/Drawer\";import{AuthProvider}from\"./components/auth/AuthProvider\";import{AccountProvider}from\"./components/login/AccountContext\";import{SnackbarProvider}from\"./components/guideSetting/guideList/SnackbarContext\";import{TranslationProvider}from\"./contexts/TranslationContext\";import jwtDecode from\"jwt-decode\";import useInfoStore from\"./store/UserInfoStore\";import{initializeI18n}from\"./multilinguial/i18n\";import{jsx as _jsx}from\"react/jsx-runtime\";function App(){const[isI18nReady,setIsI18nReady]=useState(false);const[isAppReady,setIsAppReady]=useState(false);const accessToken=useInfoStore(state=>state.accessToken);const{clearAll,clearAccessToken}=useInfoStore.getState();// Initialize i18n once when app starts\nuseEffect(()=>{const setupI18n=async()=>{try{await initializeI18n();console.log('✅ i18n ready for use');setIsI18nReady(true);}catch(error){console.error('❌ Failed to initialize i18n:',error);// Set ready anyway to prevent infinite loading\nsetIsI18nReady(true);}};setupI18n();},[]);// Signal when app is fully ready\nuseEffect(()=>{if(isI18nReady){// Add a small delay to ensure the Drawer component has started rendering\nconst timer=setTimeout(()=>{setIsAppReady(true);// Dispatch custom event to signal the content script that the app is ready\nwindow.dispatchEvent(new CustomEvent('quickadapt-app-ready'));},100);return()=>clearTimeout(timer);}},[isI18nReady]);// Check token validity\nuseEffect(()=>{if(accessToken){try{const decodedToken=jwtDecode(accessToken);const currentTime=Math.floor(Date.now()/1000);if(decodedToken.exp<currentTime){console.log('🔐 Token expired, clearing session');clearAll();clearAccessToken();}}catch(error){console.error('❌ Invalid token, clearing session:',error);clearAll();clearAccessToken();}}},[accessToken,clearAll,clearAccessToken]);// Show loading until i18n is ready\nif(!isI18nReady){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",style:{display:'flex',justifyContent:'center',alignItems:'center',height:'100vh'},children:/*#__PURE__*/_jsx(\"div\",{children:\"Loading...\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(TranslationProvider,{children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AccountProvider,{children:/*#__PURE__*/_jsx(SnackbarProvider,{children:/*#__PURE__*/_jsx(Drawer,{})})})})})});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Drawer", "<PERSON>th<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>", "SnackbarProvider", "TranslationProvider", "jwtDecode", "useInfoStore", "initializeI18n", "jsx", "_jsx", "App", "isI18nReady", "setIsI18nReady", "isAppReady", "setIsAppReady", "accessToken", "state", "clearAll", "clearAccessToken", "getState", "setupI18n", "console", "log", "error", "timer", "setTimeout", "window", "dispatchEvent", "CustomEvent", "clearTimeout", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "className", "style", "display", "justifyContent", "alignItems", "height", "children"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport \"./App.scss\";\r\nimport GuidePopup from \"./components/guideSetting/GuidePopUp\";\r\nimport Drawer from \"./components/drawer/Drawer\";\r\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\r\nimport { AccountProvider } from \"./components/login/AccountContext\";\r\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\r\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\r\nimport Rte from \"./components/guideSetting/RTE\";\r\nimport jwtDecode from \"jwt-decode\";\r\nimport useInfoStore from \"./store/UserInfoStore\";\r\nimport { initializeI18n } from \"./multilinguial/i18n\";\r\n\r\nfunction App() {\r\n\tconst [isI18nReady, setIsI18nReady] = useState(false);\r\n\tconst [isAppReady, setIsAppReady] = useState(false);\r\n\tconst accessToken = useInfoStore((state) => state.accessToken);\r\n\tconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\n\r\n\t// Initialize i18n once when app starts\r\n\tuseEffect(() => {\r\n\t\tconst setupI18n = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait initializeI18n();\r\n\t\t\t\tconsole.log('✅ i18n ready for use');\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Failed to initialize i18n:', error);\r\n\t\t\t\t// Set ready anyway to prevent infinite loading\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tsetupI18n();\r\n\t}, []);\r\n\r\n\t// Signal when app is fully ready\r\n\tuseEffect(() => {\r\n\t\tif (isI18nReady) {\r\n\t\t\t// Add a small delay to ensure the Drawer component has started rendering\r\n\t\t\tconst timer = setTimeout(() => {\r\n\t\t\t\tsetIsAppReady(true);\r\n\t\t\t\t// Dispatch custom event to signal the content script that the app is ready\r\n\t\t\t\twindow.dispatchEvent(new CustomEvent('quickadapt-app-ready'));\r\n\t\t\t}, 100);\r\n\r\n\t\t\treturn () => clearTimeout(timer);\r\n\t\t}\r\n\t}, [isI18nReady]);\r\n\r\n\t// Check token validity\r\n\tuseEffect(() => {\r\n\t\tif (accessToken) {\r\n\t\t\ttry {\r\n\t\t\t\tconst decodedToken: any = jwtDecode(accessToken);\r\n\t\t\t\tconst currentTime = Math.floor(Date.now() / 1000);\r\n\t\t\t\tif (decodedToken.exp < currentTime) {\r\n\t\t\t\t\tconsole.log('🔐 Token expired, clearing session');\r\n\t\t\t\t\tclearAll();\r\n\t\t\t\t\tclearAccessToken();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Invalid token, clearing session:', error);\r\n\t\t\t\tclearAll();\r\n\t\t\t\tclearAccessToken();\r\n\t\t\t}\r\n\t\t}\r\n\t}, [accessToken, clearAll, clearAccessToken]);\r\n\r\n\t// Show loading until i18n is ready\r\n\tif (!isI18nReady) {\r\n\t\treturn (\r\n\t\t\t<div className=\"App\" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\r\n\t\t\t\t<div>Loading...</div>\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\treturn (\r\n\t\t<div className=\"App\">\r\n\t\t\t<TranslationProvider>\r\n\t\t\t\t<AuthProvider>\r\n\t\t\t\t\t<AccountProvider>\r\n\t\t\t\t\t\t<SnackbarProvider>\r\n\t\t\t\t\t\t\t<Drawer />\r\n\t\t\t\t\t\t</SnackbarProvider>\r\n\t\t\t\t\t</AccountProvider>\r\n\t\t\t\t</AuthProvider>\r\n\t\t\t</TranslationProvider>\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,YAAY,CAEnB,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,OAASC,YAAY,KAAQ,gCAAgC,CAC7D,OAASC,eAAe,KAAQ,mCAAmC,CACnE,OAASC,gBAAgB,KAAQ,qDAAqD,CACtF,OAASC,mBAAmB,KAAQ,+BAA+B,CAEnE,MAAO,CAAAC,SAAS,KAAM,YAAY,CAClC,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,OAASC,cAAc,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEtD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACd,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAAgB,WAAW,CAAGT,YAAY,CAAEU,KAAK,EAAKA,KAAK,CAACD,WAAW,CAAC,CAC9D,KAAM,CAAEE,QAAQ,CAAEC,gBAAiB,CAAC,CAAGZ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAE9D;AACArB,SAAS,CAAC,IAAM,CACf,KAAM,CAAAsB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACH,KAAM,CAAAb,cAAc,CAAC,CAAC,CACtBc,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnCV,cAAc,CAAC,IAAI,CAAC,CACrB,CAAE,MAAOW,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD;AACAX,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAEDQ,SAAS,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN;AACAtB,SAAS,CAAC,IAAM,CACf,GAAIa,WAAW,CAAE,CAChB;AACA,KAAM,CAAAa,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC9BX,aAAa,CAAC,IAAI,CAAC,CACnB;AACAY,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAC9D,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMC,YAAY,CAACL,KAAK,CAAC,CACjC,CACD,CAAC,CAAE,CAACb,WAAW,CAAC,CAAC,CAEjB;AACAb,SAAS,CAAC,IAAM,CACf,GAAIiB,WAAW,CAAE,CAChB,GAAI,CACH,KAAM,CAAAe,YAAiB,CAAGzB,SAAS,CAACU,WAAW,CAAC,CAChD,KAAM,CAAAgB,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CACjD,GAAIL,YAAY,CAACM,GAAG,CAAGL,WAAW,CAAE,CACnCV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDL,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CAAE,MAAOK,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1DN,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CACD,CAAC,CAAE,CAACH,WAAW,CAAEE,QAAQ,CAAEC,gBAAgB,CAAC,CAAC,CAE7C;AACA,GAAI,CAACP,WAAW,CAAE,CACjB,mBACCF,IAAA,QAAK4B,SAAS,CAAC,KAAK,CAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAC,QAAA,cAChHlC,IAAA,QAAAkC,QAAA,CAAK,YAAU,CAAK,CAAC,CACjB,CAAC,CAER,CAEA,mBACClC,IAAA,QAAK4B,SAAS,CAAC,KAAK,CAAAM,QAAA,cACnBlC,IAAA,CAACL,mBAAmB,EAAAuC,QAAA,cACnBlC,IAAA,CAACR,YAAY,EAAA0C,QAAA,cACZlC,IAAA,CAACP,eAAe,EAAAyC,QAAA,cACflC,IAAA,CAACN,gBAAgB,EAAAwC,QAAA,cAChBlC,IAAA,CAACT,MAAM,GAAE,CAAC,CACO,CAAC,CACH,CAAC,CACL,CAAC,CACK,CAAC,CAClB,CAAC,CAER,CAEA,cAAe,CAAAU,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}