{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\CheckpointEditPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useContext } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button, FormControl, Select, MenuItem, Tooltip } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { deleteicon, chkicn1, chkicn2, chkicn3, chkicn4, chkicn5, chkicn6, deletestep, redirect, warning } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\nimport { AccountContext } from \"../login/AccountContext\";\nimport { getAllGuides } from \"../../services/GuideListServices\";\nimport { useTranslation } from 'react-i18next';\nimport '../../styles/rtl_styles.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckPointEditPopup = ({\n  editInteractionName,\n  checkpointsEditPopup\n}) => {\n  _s();\n  var _checklistGuideMetaDa, _checklistGuideMetaDa2, _checklistGuideMetaDa3, _checklistCheckpointP2;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setCheckPointsEditPopup,\n    titlePopup,\n    setTitlePopup,\n    setDesignPopup,\n    titleColor,\n    setTitleColor,\n    checkpointsPopup,\n    setCheckPointsPopup,\n    checkpointTitleColor,\n    setCheckpointTitleColor,\n    checkpointTitleDescription,\n    setCheckpointTitleDescription,\n    checkpointIconColor,\n    setCheckpointIconColor,\n    setUnlockCheckPointInOrder,\n    unlockCheckPointInOrder,\n    checkPointMessage,\n    setCheckPointMessage,\n    checklistGuideMetaData,\n    updateChecklistCheckPointItem,\n    setIsUnSavedChanges,\n    isUnSavedChanges\n  } = useDrawerStore(state => state);\n  const data = checklistGuideMetaData[0].checkpoints.checkpointsList.find(k => k.id === editInteractionName);\n  const encodeToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  const [icons, setIcons] = useState(() => {\n    return [{\n      id: 1,\n      base64: encodeToBase64(chkicn1),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn1\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }, {\n      id: 2,\n      base64: encodeToBase64(chkicn2),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn2\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }, {\n      id: 3,\n      base64: encodeToBase64(chkicn3),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn3\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }, {\n      id: 4,\n      base64: encodeToBase64(chkicn4),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn4\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }, {\n      id: 5,\n      base64: encodeToBase64(chkicn5),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn5\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }, {\n      id: 6,\n      base64: encodeToBase64(chkicn6),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn6\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 7\n      }, this),\n      selected: false\n    }];\n  });\n  const [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState(() => {\n    const initialchecklistCheckpointProperties = {\n      interaction: data === null || data === void 0 ? void 0 : data.interaction,\n      title: data === null || data === void 0 ? void 0 : data.title,\n      description: data === null || data === void 0 ? void 0 : data.description,\n      redirectURL: data === null || data === void 0 ? void 0 : data.redirectURL,\n      icon: (data === null || data === void 0 ? void 0 : data.icon) || icons[0].component,\n      supportingMedia: (data === null || data === void 0 ? void 0 : data.supportingMedia) || [],\n      mediaTitle: data === null || data === void 0 ? void 0 : data.mediaTitle,\n      mediaDescription: data === null || data === void 0 ? void 0 : data.mediaDescription,\n      id: data === null || data === void 0 ? void 0 : data.id\n    };\n    return initialchecklistCheckpointProperties;\n  });\n  const handleCheckPointIconColorChange = e => setCheckpointIconColor(e.target.value);\n  const handleCheckPointTitleColorChange = e => setCheckpointTitleColor(e.target.value);\n  const handleCheckPointDescriptionColorChange = e => setCheckpointTitleColor(e.target.value);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (checkpointsEditPopup && checklistCheckpointProperties.icon) {\n      setIcons(prevIcons => prevIcons.map(icon => ({\n        ...icon,\n        selected: icon.base64 === checklistCheckpointProperties.icon // Compare Base64 strings directly\n      })));\n    }\n  }, [checkpointsEditPopup, checklistCheckpointProperties.icon]);\n  const handleIconClick = id => {\n    setIcons(prevIcons => prevIcons.map(icon => ({\n      ...icon,\n      selected: icon.id === id\n    })));\n    const selectedIcon = icons.find(icon => icon.id === id);\n    if (selectedIcon) {\n      setChecklistCheckpointProperties(prev => ({\n        ...prev,\n        icon: selectedIcon.base64 // Save Base64 instead of component\n      }));\n    }\n  };\n  const handleFileUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (!file) return;\n    const isIco = file.name.endsWith(\".ico\");\n\n    // Validate the file type and size\n    const img = new Image();\n    img.src = URL.createObjectURL(file);\n    img.onload = () => {\n      if (!isIco || img.width > 64 || img.height > 64) {\n        setError(\"Please upload an .ico file less than 64x64px\");\n      } else {\n        setError(null);\n        setIcons(prevIcons => [...prevIcons, {\n          id: prevIcons.length + 1,\n          component: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: img.src,\n            alt: \"Custom Icon\",\n            width: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 9\n          }, this),\n          selected: false\n        }]);\n      }\n    };\n  };\n  const handleClose = () => {\n    setCheckPointsEditPopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleSizeChange = value => {\n    const sizeInPx = 16 + (value - 1) * 4;\n    onPropertyChange(\"Size\", sizeInPx);\n  };\n  const onReselectElement = () => {};\n  const onPropertyChange = (key, value) => {\n    setChecklistCheckpointProperties(prevState => ({\n      ...prevState,\n      [key]: value\n    }));\n  };\n  const handleApplyChanges = () => {\n    setFileError(null);\n    updateChecklistCheckPointItem(checklistCheckpointProperties);\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  const handleEditClick = () => {\n    setCheckPointsEditPopup(true);\n  };\n  const [interactions, setInteractions] = useState([]);\n  const [skip, setSkip] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [hasMore, setHasMore] = useState(true);\n  const dropdownRef = useRef(null);\n  const {\n    accountId\n  } = useContext(AccountContext);\n\n  // Set number of items to fetch per request to 10\n  const top = 10;\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchData(0); // Load first 10 guides\n  }, []);\n  const fetchData = async newSkip => {\n    if (loading || !hasMore) return; // Prevent duplicate calls or if no more data\n\n    setLoading(true);\n    const filters = [{\n      FieldName: \"AccountId\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: accountId,\n      IsCustomField: false\n    }];\n    try {\n      const data = await getAllGuides(newSkip, top, filters, \"\");\n      const newInteractions = (data === null || data === void 0 ? void 0 : data.results) || [];\n      if (newInteractions.length === 0) {\n        setHasMore(false);\n      } else {\n        setInteractions(prev => [...prev, ...newInteractions]);\n        setSkip(newSkip + top);\n      }\n    } catch (error) {\n      console.error(\"Error fetching guides:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle scroll event for the dropdown menu\n  const handleMenuScroll = event => {\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = event.currentTarget;\n\n    // If scrolled to bottom (with a small buffer)\n    if (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {\n      fetchData(skip);\n    }\n  };\n  const [files, setFiles] = useState([]);\n  const [gifFile, setGifFile] = useState(null);\n  const [videoFile, setVideoFile] = useState(null);\n  const [fileError, setFileError] = useState(null);\n  const convertFileToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const convertBase64ToFile = (base64, fileName, fileType) => {\n    if (!base64) {\n      console.error(\"Error: Base64 string is undefined or empty.\");\n      return null; // Return null if invalid input\n    }\n\n    // If the base64 string doesn't contain a comma, it's probably missing the \"data:image/png;base64,\" prefix\n    const base64Data = base64.includes(\",\") ? base64.split(\",\")[1] : base64;\n    try {\n      const byteCharacters = atob(base64Data);\n      const byteNumbers = new Array(byteCharacters.length).fill(null).map((_, i) => byteCharacters.charCodeAt(i));\n      const byteArray = new Uint8Array(byteNumbers);\n      return new File([byteArray], fileName, {\n        type: fileType\n      });\n    } catch (error) {\n      console.error(\"Error converting Base64 to File:\", error);\n      return null;\n    }\n  };\n  useEffect(() => {\n    var _checklistCheckpointP;\n    if (checkpointsEditPopup && ((_checklistCheckpointP = checklistCheckpointProperties.supportingMedia) === null || _checklistCheckpointP === void 0 ? void 0 : _checklistCheckpointP.length) > 0) {\n      const mediaFiles = checklistCheckpointProperties.supportingMedia.map(media => {\n        if (typeof media === \"string\") {\n          return null; // Skip if media is a plain string (unexpected case)\n        } else if (typeof media === \"object\" && media.Base64) {\n          return convertBase64ToFile(media.Base64, media.Name, media.Type); // ✅ Use actual file name\n        }\n        return null;\n      }).filter(file => file !== null); // ✅ Remove null values\n\n      // ✅ Separate files correctly\n      const imageFiles = mediaFiles.filter(file => [\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type));\n      const gif = mediaFiles.find(file => file.type === \"image/gif\") || null;\n      const video = mediaFiles.find(file => file.type === \"video/mp4\") || null;\n      setFiles(imageFiles);\n      setGifFile(gif);\n      setVideoFile(video);\n    }\n  }, [checkpointsEditPopup, checklistCheckpointProperties.supportingMedia]);\n  const handleFileChange = async event => {\n    setFileError(null);\n    if (!event.target.files) return;\n    const newFiles = Array.from(event.target.files);\n    const base64Files = await Promise.all(newFiles.map(async file => ({\n      Name: file.name,\n      Type: file.type,\n      Base64: await convertFileToBase64(file)\n    })));\n    const fileObjects = base64Files.map(fileData => convertBase64ToFile(fileData.Base64, fileData.Name, fileData.Type)).filter(file => file !== null);\n    const newGifs = fileObjects.filter(file => file.name.toLowerCase().endsWith(\".gif\"));\n    const newVideos = fileObjects.filter(file => file.name.toLowerCase().endsWith(\".mp4\"));\n    const newImages = fileObjects.filter(file => [\".png\", \".jpg\", \".jpeg\"].some(ext => file.name.toLowerCase().endsWith(ext)));\n\n    // Validate types\n    const allTypes = Array.from(new Set(fileObjects.map(file => file.type)));\n    if (allTypes.length > 1) {\n      setFileError(translate(\"Mixed file formats are not allowed.\"));\n      return;\n    }\n\n    // Case 1: Check if a GIF already exists\n    if (gifFile && newGifs.length > 0) {\n      setFileError(translate(\"Only one GIF allowed\"));\n      return;\n    }\n\n    // Case 2: Check if a video already exists\n    if (videoFile && newVideos.length > 0) {\n      setFileError(translate(\"Only one Video allowed\"));\n      return;\n    }\n\n    // Case 3: If a GIF exists, prevent uploading any other type\n    if (gifFile && (newVideos.length > 0 || newImages.length > 0)) {\n      setFileError(translate(\"Mixed file formats are not allowed.\"));\n      return;\n    }\n\n    // Case 4: If a video exists, prevent uploading any other type\n    if (videoFile && (newGifs.length > 0 || newImages.length > 0)) {\n      setFileError(translate(\"Mixed file formats are not allowed.\"));\n      return;\n    }\n\n    // Case 5: If images exist, ensure new images are same type\n    if (files.length > 0 && newImages.length > 0) {\n      const existingType = files[0].type;\n      const newImageType = newImages[0].type;\n      const isSameType = newImages.every(img => img.type === existingType);\n      if (!isSameType || newImageType !== existingType) {\n        setFileError(translate(\"Mixed file formats are not allowed.\"));\n        return;\n      }\n    }\n\n    // Case 6: If images exist and GIF/MP4 is being added\n    if (files.length > 0 && (newGifs.length > 0 || newVideos.length > 0)) {\n      setFileError(\"Mixed file formats are not allowed.\");\n      return;\n    }\n\n    // Set accepted files\n    if (newGifs.length > 0) {\n      setGifFile(newGifs[0]);\n    }\n    if (newVideos.length > 0) {\n      setVideoFile(newVideos[0]);\n    }\n    if (newImages.length > 0) {\n      setFiles(prevFiles => {\n        const updated = [...prevFiles, ...newImages];\n        updated.sort((a, b) => {\n          const aNum = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)[0], 10) : 0;\n          const bNum = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)[0], 10) : 0;\n          return aNum - bNum;\n        });\n        return updated;\n      });\n    }\n\n    // Update media\n    setChecklistCheckpointProperties(prevState => {\n      const updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\n      updatedMedia.sort((a, b) => {\n        const aNum = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)[0], 10) : 0;\n        const bNum = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)[0], 10) : 0;\n        return aNum - bNum;\n      });\n      return {\n        ...prevState,\n        supportingMedia: updatedMedia\n      };\n    });\n  };\n  const handleDeleteFile = index => {\n    setFileError(null);\n    setFiles(prevFiles => {\n      const updated = prevFiles.filter((_, i) => i !== index);\n      setChecklistCheckpointProperties(prev => {\n        var _prev$supportingMedia;\n        return {\n          ...prev,\n          supportingMedia: ((_prev$supportingMedia = prev.supportingMedia) === null || _prev$supportingMedia === void 0 ? void 0 : _prev$supportingMedia.filter((_, i) => i !== index)) || []\n        };\n      });\n      return updated;\n    });\n  };\n  const handleDeleteGif = () => {\n    setGifFile(null);\n    setChecklistCheckpointProperties(prev => {\n      var _prev$supportingMedia2;\n      return {\n        ...prev,\n        supportingMedia: ((_prev$supportingMedia2 = prev.supportingMedia) === null || _prev$supportingMedia2 === void 0 ? void 0 : _prev$supportingMedia2.filter(file => {\n          var _file$Name;\n          return !((_file$Name = file.Name) !== null && _file$Name !== void 0 && _file$Name.toLowerCase().endsWith(\".gif\"));\n        })) || []\n      };\n    });\n  };\n  const handleDeleteVideo = () => {\n    setVideoFile(null);\n    setChecklistCheckpointProperties(prev => {\n      var _prev$supportingMedia3;\n      return {\n        ...prev,\n        supportingMedia: ((_prev$supportingMedia3 = prev.supportingMedia) === null || _prev$supportingMedia3 === void 0 ? void 0 : _prev$supportingMedia3.filter(file => {\n          var _file$Name2;\n          return !((_file$Name2 = file.Name) !== null && _file$Name2 !== void 0 && _file$Name2.toLowerCase().endsWith(\".mp4\"));\n        })) || []\n      };\n    });\n  };\n  const index = (_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : (_checklistGuideMetaDa2 = _checklistGuideMetaDa.checkpoints) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : (_checklistGuideMetaDa3 = _checklistGuideMetaDa2.checkpointsList) === null || _checklistGuideMetaDa3 === void 0 ? void 0 : _checklistGuideMetaDa3.findIndex(i => i.id === editInteractionName);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: [translate(\"Step\"), \" \", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 8px 0 !important\"\n              },\n              children: translate(\"Interaction\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              variant: \"outlined\",\n              fullWidth: true,\n              className: \"qadpt-control-input\",\n              sx: {\n                width: \"calc(100% - 13px) !important\",\n                borderRadius: \"12px\",\n                padding: \"0 8px 8px 8px\",\n                margin: \"0 !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                displayEmpty: true,\n                disabled: true,\n                value: checklistCheckpointProperties.interaction || \"\",\n                sx: {\n                  width: \"100% !important\",\n                  borderRadius: \"12px\",\n                  backgroundColor: \"#f5f5f5\",\n                  padding: \"10px\",\n                  color: \"#333\",\n                  \".MuiSelect-icon\": {\n                    display: \"none\" // Hide the dropdown arrow to keep the read-only feel\n                  },\n                  \"& .MuiOutlinedInput-root\": {\n                    \"&:hover\": {\n                      borderColor: \"none !important\"\n                    },\n                    \"&.Mui-focused\": {\n                      borderColor: \"none !important\"\n                    }\n                  },\n                  \"& .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"35px !important\"\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: checklistCheckpointProperties.interaction,\n                  children: checklistCheckpointProperties.interaction || translate(\"No Interaction Selected\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 !important\",\n                marginBottom: \"8px !important\"\n              },\n              children: translate(\"Title\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              size: \"small\",\n              placeholder: translate(\"Step Title\"),\n              className: \"qadpt-control-input\",\n              style: {\n                width: \"100%\"\n              },\n              value: checklistCheckpointProperties.title,\n              onChange: e => onPropertyChange(\"title\", e.target.value),\n              InputProps: {\n                endAdornment: \"\",\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  },\n                  \"& input\": {\n                    textAlign: \"left !important\",\n                    paddingLeft: \"10px !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"auto !important\"\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 !important\",\n                marginBottom: \"8px !important\"\n              },\n              children: translate(\"Description\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              size: \"small\",\n              placeholder: translate(\"Step Desc\"),\n              className: \"qadpt-control-input\",\n              multiline: true,\n              minRows: 3,\n              style: {\n                width: \"100%\"\n              },\n              value: checklistCheckpointProperties.description,\n              onChange: e => onPropertyChange(\"description\", e.target.value),\n              InputProps: {\n                endAdornment: \"\",\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  },\n                  \"& input\": {\n                    textAlign: \"left !important\",\n                    paddingLeft: \"10px !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"auto !important\"\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              style: {\n                display: \"flex\",\n                flexDirection: \"row\",\n                alignItems: \"center\",\n                gap: \"5px\",\n                padding: \"0\",\n                marginBottom: \"10px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: \"#444444\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Redirect URL\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: redirect\n                },\n                style: {\n                  display: \"flex\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 10\n              }, this), \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"11px\",\n                color: \"#8d8d8d\",\n                textAlign: \"left\",\n                padding: \"0\",\n                marginBottom: \"10px\"\n              },\n              children: translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              size: \"small\",\n              placeholder: translate(\"Redirection URL\"),\n              className: \"qadpt-control-input\",\n              style: {\n                width: \"100%\"\n              },\n              value: checklistCheckpointProperties === null || checklistCheckpointProperties === void 0 ? void 0 : checklistCheckpointProperties.redirectURL,\n              onChange: e => onPropertyChange(\"redirectURL\", e.target.value),\n              InputProps: {\n                endAdornment: \"\",\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  },\n                  \"& input\": {\n                    textAlign: \"left !important\",\n                    paddingLeft: \"10px !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"auto !important\"\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 8px 0 !important\"\n              },\n              children: translate(\"Icon\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                gap: 1,\n                alignItems: \"center\",\n                width: \"-webkit-fill-available\",\n                flexWrap: \"wrap\"\n              },\n              children: icons.map(icon => /*#__PURE__*/_jsxDEV(Tooltip, {\n                arrow: true,\n                title: translate(\"Select Icon\"),\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleIconClick(icon.id),\n                  sx: {\n                    border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\n                    borderRadius: \"8px\",\n                    padding: \"8px\",\n                    background: \"#F1ECEC\"\n                  },\n                  children: icon.component\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 12\n                }, this)\n              }, icon.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 11\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 8px 0 !important\"\n              },\n              children: translate(\"Supporting Media\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: \"165px\",\n                height: \"auto\",\n                margin: \"0 8px 8px 8px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                border: \"1px dashed var(--primarycolor)\",\n                borderRadius: \"12px\",\n                padding: \"8px\",\n                background: \"#F1ECEC\",\n                textAlign: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                className: \"qadpt-upload-button\",\n                style: {\n                  height: \"auto\",\n                  padding: \"0\",\n                  width: \"100%\",\n                  display: \"flex\",\n                  flexDirection: \"row\",\n                  // Ensures icon & text are in one line\n                  alignItems: \"center\",\n                  justifyContent: \"center\",\n                  gap: \"6px\",\n                  color: \"#000\",\n                  backgroundColor: \"#F1ECEC\",\n                  textTransform: \"capitalize\",\n                  boxShadow: \"none\"\n                },\n                variant: \"contained\",\n                component: \"label\",\n                children: [/*#__PURE__*/_jsxDEV(CloudUploadOutlinedIcon, {\n                  sx: {\n                    zoom: \"1.6\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 11\n                }, this), translate(\"Upload File\"), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"file-input\",\n                  type: \"file\",\n                  multiple: true,\n                  accept: \".jpeg, .jpg, .png, .gif, .mp4\" // ✅ Added MP4 support\n                  ,\n                  onChange: handleFileChange,\n                  style: {\n                    display: \"none\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                style: {\n                  fontSize: \"12px\",\n                  color: \"#A3A3A3\"\n                },\n                children: \".png, .jpg, .gif, .mp4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 9\n            }, this), fileError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                color: \"#e6a957\",\n                width: \"-webkit-fill-available\",\n                padding: \"0 8px\",\n                textAlign: \"left\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginRight: \"4px\",\n                  display: \"flex\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"12px\"\n                },\n                children: fileError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: \"-webkit-fill-available\"\n              },\n              children: [\" \", files.map((file, index) => /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                sx: {\n                  borderRadius: \"12px\",\n                  padding: \"8px\",\n                  margin: \"8px\",\n                  backgroundColor: \"#e5dada\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(file),\n                  alt: `uploaded-${index}`,\n                  style: {\n                    width: \"20px\",\n                    height: \"20px\",\n                    borderRadius: \"5px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    flex: 1,\n                    ml: 2,\n                    fontSize: \"14px\",\n                    wordBreak: \"break-word\"\n                  },\n                  children: file.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleDeleteFile(index),\n                  size: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: deletestep\n                    },\n                    style: {\n                      zoom: \"1\",\n                      display: \"flex\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 13\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 12\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 11\n              }, this)), gifFile && /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                sx: {\n                  borderRadius: \"12px\",\n                  padding: \"8px\",\n                  margin: \"5px\",\n                  backgroundColor: \"#e5dada\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(gifFile),\n                  alt: \"uploaded-gif\",\n                  style: {\n                    width: \"20px\",\n                    height: \"20px\",\n                    borderRadius: \"5px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    flex: 1,\n                    fontSize: \"14px\",\n                    wordBreak: \"break-word\"\n                  },\n                  children: gifFile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleDeleteGif,\n                  size: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: deletestep\n                    },\n                    style: {\n                      zoom: \"1\",\n                      display: \"flex\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 13\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 11\n              }, this), videoFile && /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                sx: {\n                  border: \"1px solid #0a6\",\n                  borderRadius: \"5px\",\n                  padding: \"8px\",\n                  marginBottom: \"5px\",\n                  width: \"196px\",\n                  backgroundColor: \"#e6ffe6\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  width: \"40\",\n                  height: \"40\",\n                  controls: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: URL.createObjectURL(videoFile),\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 13\n                  }, this), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    flex: 1,\n                    ml: 2,\n                    fontSize: \"14px\",\n                    wordBreak: \"break-word\"\n                  },\n                  children: videoFile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleDeleteVideo,\n                  size: \"small\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: deleteicon\n                    },\n                    style: {\n                      zoom: 0.7\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 !important\",\n                marginBottom: \"8px !important\"\n              },\n              children: translate(\"Media Title\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              size: \"small\",\n              placeholder: translate(\"Media Title\"),\n              className: \"qadpt-control-input\",\n              style: {\n                width: \"100%\"\n              },\n              value: checklistCheckpointProperties.mediaTitle,\n              onChange: e => onPropertyChange(\"mediaTitle\", e.target.value)\n              //onChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\n              ,\n              InputProps: {\n                endAdornment: \"\",\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  },\n                  \"& input\": {\n                    textAlign: \"left !important\",\n                    paddingLeft: \"10px !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"auto !important\"\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box qadpt-chkcontrol-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 !important\",\n                marginBottom: \"8px !important\"\n              },\n              children: translate(\"Media Description\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              size: \"small\",\n              placeholder: translate(\"Media Desc\"),\n              className: \"qadpt-control-input\",\n              multiline: true,\n              minRows: 3,\n              style: {\n                width: \"100%\"\n              },\n              value: checklistCheckpointProperties.mediaDescription,\n              onChange: e => {\n                let value = e.target.value;\n                if (value.length > 200) {\n                  value = value.slice(0, 200);\n                }\n                onPropertyChange(\"mediaDescription\", value);\n              },\n              helperText: `${((_checklistCheckpointP2 = checklistCheckpointProperties.mediaDescription) === null || _checklistCheckpointP2 === void 0 ? void 0 : _checklistCheckpointP2.length) || 0}/200`,\n              InputProps: {\n                endAdornment: \"\",\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  },\n                  \"& input\": {\n                    textAlign: \"left !important\",\n                    paddingLeft: \"10px !important\"\n                  },\n                  \"&.MuiInputBase-root\": {\n                    height: \"auto !important\"\n                  }\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: \"qadpt-btn\",\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1014,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 4\n  }, this);\n};\n_s(CheckPointEditPopup, \"1YGWxjx2SCBfuWrRQo5ccu5sGJ0=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = CheckPointEditPopup;\nexport default CheckPointEditPopup;\nvar _c;\n$RefreshReg$(_c, \"CheckPointEditPopup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "<PERSON><PERSON><PERSON>", "CloseIcon", "useDrawerStore", "deleteicon", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "deletestep", "redirect", "warning", "ArrowBackIosNewOutlinedIcon", "CloudUploadOutlinedIcon", "AccountContext", "getAllGuides", "useTranslation", "jsxDEV", "_jsxDEV", "CheckPointEditPopup", "editInteractionName", "checkpointsEditPopup", "_s", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checklistCheckpointP2", "t", "translate", "setCheckPointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "checklistGuideMetaData", "updateChecklistCheckPointItem", "setIsUnSavedChanges", "isUnSavedChanges", "state", "data", "checkpoints", "checkpointsList", "find", "k", "id", "encodeToBase64", "svgString", "btoa", "icons", "setIcons", "base64", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "initialchecklistCheckpointProperties", "interaction", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "handleCheckPointIconColorChange", "e", "target", "value", "handleCheckPointTitleColorChange", "handleCheckPointDescriptionColorChange", "error", "setError", "prevIcons", "map", "handleIconClick", "selectedIcon", "prev", "handleFileUpload", "event", "_event$target$files", "file", "files", "isIco", "name", "endsWith", "img", "Image", "src", "URL", "createObjectURL", "onload", "width", "height", "length", "alt", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "prevState", "handleApplyChanges", "setFileError", "handleEditClick", "interactions", "setInteractions", "skip", "setSkip", "loading", "setLoading", "hasMore", "setHasMore", "dropdownRef", "accountId", "top", "fetchData", "newSkip", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "newInteractions", "results", "console", "handleMenuScroll", "scrollTop", "scrollHeight", "clientHeight", "currentTarget", "setFiles", "gifFile", "setGifFile", "videoFile", "setVideoFile", "fileError", "convertFileToBase64", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "result", "onerror", "convertBase64ToFile", "fileType", "base64Data", "includes", "split", "byteCharacters", "atob", "byteNumbers", "Array", "fill", "_", "i", "charCodeAt", "byteArray", "Uint8Array", "File", "type", "_checklistCheckpointP", "mediaFiles", "media", "Base64", "Name", "Type", "filter", "imageFiles", "gif", "video", "handleFileChange", "newFiles", "from", "base64Files", "all", "fileObjects", "fileData", "newGifs", "toLowerCase", "newVideos", "newImages", "some", "ext", "allTypes", "Set", "existingType", "newImageType", "isSameType", "every", "prevFiles", "updated", "sort", "a", "b", "aNum", "match", "parseInt", "bNum", "updatedMedia", "handleDeleteFile", "index", "_prev$supportingMedia", "handleDeleteGif", "_prev$supportingMedia2", "_file$Name", "handleDeleteVideo", "_prev$supportingMedia3", "_file$Name2", "findIndex", "className", "children", "onClick", "size", "sx", "padding", "variant", "fullWidth", "borderRadius", "margin", "displayEmpty", "disabled", "backgroundColor", "color", "borderColor", "border", "marginBottom", "placeholder", "onChange", "InputProps", "endAdornment", "textAlign", "paddingLeft", "multiline", "minRows", "flexDirection", "alignItems", "gap", "fontWeight", "fontSize", "flexWrap", "arrow", "background", "justifyContent", "textTransform", "boxShadow", "multiple", "accept", "marginRight", "flex", "ml", "wordBreak", "controls", "slice", "helperText", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/CheckpointEditPopup.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useRef, useContext } from \"react\";\r\nimport { Box, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n\tdeleteicon,\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tdeletestep,\r\n\tredirect,\r\n\twarning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\nconst CheckPointEditPopup = ({ editInteractionName, checkpointsEditPopup }: { editInteractionName: string; checkpointsEditPopup:any}) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\tsetCheckPointsEditPopup,\r\n\t\t\ttitlePopup,\r\n\t\t\tsetTitlePopup,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttitleColor,\r\n\t\t\tsetTitleColor,\r\n\t\t\tcheckpointsPopup,\r\n\t\t\tsetCheckPointsPopup,\r\n\t\t\tcheckpointTitleColor,\r\n\t\t\tsetCheckpointTitleColor,\r\n\t\t\tcheckpointTitleDescription,\r\n\t\t\tsetCheckpointTitleDescription,\r\n\t\t\tcheckpointIconColor,\r\n\t\t\tsetCheckpointIconColor,\r\n\t\t\tsetUnlockCheckPointInOrder,\r\n\t\t\tunlockCheckPointInOrder,\r\n\t\t\tcheckPointMessage,\r\n\t\t\tsetCheckPointMessage,\r\n\t\t\tchecklistGuideMetaData,\r\n\t\t\tupdateChecklistCheckPointItem,\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tisUnSavedChanges,\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst data = checklistGuideMetaData[0].checkpoints.checkpointsList.find((k: any) => k.id === editInteractionName);\r\n\t\tconst encodeToBase64 = (svgString: string) => {\r\n\t\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t\t};\r\n\r\n\t\tconst [icons, setIcons] = useState<any[]>(() => {\r\n\t\t\treturn [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn1),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn2),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn2 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn3),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn3 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn4),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn4 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 5,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn5),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn5 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 6,\r\n\t\t\t\t\tbase64: encodeToBase64(chkicn6),\r\n\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn6 }}\r\n\t\t\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t\tselected: false,\r\n\t\t\t\t},\r\n\t\t\t];\r\n\t\t});\r\n\r\n\t\tconst [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {\r\n\t\t\tconst initialchecklistCheckpointProperties = {\r\n\t\t\t\tinteraction: data?.interaction,\r\n\t\t\t\ttitle: data?.title,\r\n\t\t\t\tdescription: data?.description,\r\n\t\t\t\tredirectURL: data?.redirectURL,\r\n\t\t\t\ticon: data?.icon || icons[0].component,\r\n\t\t\t\tsupportingMedia: data?.supportingMedia || [],\r\n\t\t\t\tmediaTitle: data?.mediaTitle,\r\n\t\t\t\tmediaDescription: data?.mediaDescription,\r\n\t\t\t\tid: data?.id,\r\n\t\t\t};\r\n\t\t\treturn initialchecklistCheckpointProperties;\r\n\t\t});\r\n\r\n\t\tconst handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);\r\n\t\tconst handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\t\tconst handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\r\n\t\tconst [error, setError] = useState<string | null>(null);\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointsEditPopup && checklistCheckpointProperties.icon) {\r\n\t\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t\t...icon,\r\n\t\t\t\t\t\tselected: icon.base64 === checklistCheckpointProperties.icon, // Compare Base64 strings directly\r\n\t\t\t\t\t}))\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}, [checkpointsEditPopup, checklistCheckpointProperties.icon]);\r\n\r\n\t\tconst handleIconClick = (id: number) => {\r\n\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t...icon,\r\n\t\t\t\t\tselected: icon.id === id,\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\r\n\t\t\tconst selectedIcon = icons.find((icon) => icon.id === id);\r\n\t\t\tif (selectedIcon) {\r\n\t\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t\t...prev,\r\n\t\t\t\t\ticon: selectedIcon.base64, // Save Base64 instead of component\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\t\tconst file = event.target.files?.[0];\r\n\t\t\tif (!file) return;\r\n\r\n\t\t\tconst isIco = file.name.endsWith(\".ico\");\r\n\r\n\t\t\t// Validate the file type and size\r\n\t\t\tconst img = new Image();\r\n\t\t\timg.src = URL.createObjectURL(file);\r\n\t\t\timg.onload = () => {\r\n\t\t\t\tif (!isIco || img.width > 64 || img.height > 64) {\r\n\t\t\t\t\tsetError(\"Please upload an .ico file less than 64x64px\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetError(null);\r\n\t\t\t\t\tsetIcons((prevIcons) => [\r\n\t\t\t\t\t\t...prevIcons,\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: prevIcons.length + 1,\r\n\t\t\t\t\t\t\tcomponent: (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={img.src}\r\n\t\t\t\t\t\t\t\t\talt=\"Custom Icon\"\r\n\t\t\t\t\t\t\t\t\twidth={24}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\tselected: false,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t]);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetCheckPointsEditPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tsetFileError(null);\r\n\r\n\t\t\tupdateChecklistCheckPointItem(checklistCheckpointProperties);\r\n\t\t\thandleClose();\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst handleEditClick = () => {\r\n\t\t\tsetCheckPointsEditPopup(true);\r\n\t\t};\r\n\r\n\t\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\r\n\t\tconst [skip, setSkip] = useState(0);\r\n\t\tconst [loading, setLoading] = useState(false);\r\n\t\tconst [hasMore, setHasMore] = useState(true);\r\n\t\tconst dropdownRef = useRef<HTMLDivElement>(null);\r\n\t\tconst { accountId } = useContext(AccountContext);\r\n\r\n\t\t// Set number of items to fetch per request to 10\r\n\t\tconst top = 10;\r\n\r\n\t\t// Initial data fetch\r\n\t\tuseEffect(() => {\r\n\t\t\tfetchData(0); // Load first 10 guides\r\n\t\t}, []);\r\n\r\n\t\tconst fetchData = async (newSkip: number) => {\r\n\t\t\tif (loading || !hasMore) return; // Prevent duplicate calls or if no more data\r\n\r\n\t\t\tsetLoading(true);\r\n\r\n\t\t\tconst filters = [\r\n\t\t\t\t{\r\n\t\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\t\tElementType: \"string\",\r\n\t\t\t\t\tCondition: \"contains\",\r\n\t\t\t\t\tValue: accountId,\r\n\t\t\t\t\tIsCustomField: false,\r\n\t\t\t\t},\r\n\t\t\t];\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst data = await getAllGuides(newSkip, top, filters, \"\");\r\n\t\t\t\tconst newInteractions = data?.results || [];\r\n\r\n\t\t\t\tif (newInteractions.length === 0) {\r\n\t\t\t\t\tsetHasMore(false);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetInteractions((prev) => [...prev, ...newInteractions]);\r\n\t\t\t\t\tsetSkip(newSkip + top);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error fetching guides:\", error);\r\n\t\t\t} finally {\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Handle scroll event for the dropdown menu\r\n\t\tconst handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {\r\n\t\t\tconst { scrollTop, scrollHeight, clientHeight } = event.currentTarget;\r\n\r\n\t\t\t// If scrolled to bottom (with a small buffer)\r\n\t\t\tif (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {\r\n\t\t\t\tfetchData(skip);\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst [files, setFiles] = useState<File[]>([]);\r\n\t\tconst [gifFile, setGifFile] = useState<File | null>(null);\r\n\t\tconst [videoFile, setVideoFile] = useState<File | null>(null);\r\n\t\tconst [fileError, setFileError] = useState<string | null>(null);\r\n\r\n\t\tconst convertFileToBase64 = (file: File): Promise<string> => {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tconst reader = new FileReader();\r\n\t\t\t\treader.readAsDataURL(file);\r\n\t\t\t\treader.onload = () => resolve(reader.result as string);\r\n\t\t\t\treader.onerror = (error) => reject(error);\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst convertBase64ToFile = (base64: string, fileName: string, fileType: string) => {\r\n\t\t\tif (!base64) {\r\n\t\t\t\tconsole.error(\"Error: Base64 string is undefined or empty.\");\r\n\t\t\t\treturn null; // Return null if invalid input\r\n\t\t\t}\r\n\r\n\t\t\t// If the base64 string doesn't contain a comma, it's probably missing the \"data:image/png;base64,\" prefix\r\n\t\t\tconst base64Data = base64.includes(\",\") ? base64.split(\",\")[1] : base64;\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst byteCharacters = atob(base64Data);\r\n\t\t\t\tconst byteNumbers = new Array(byteCharacters.length).fill(null).map((_, i) => byteCharacters.charCodeAt(i));\r\n\t\t\t\tconst byteArray = new Uint8Array(byteNumbers);\r\n\r\n\t\t\t\treturn new File([byteArray], fileName, { type: fileType });\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error converting Base64 to File:\", error);\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t};\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointsEditPopup && checklistCheckpointProperties.supportingMedia?.length > 0) {\r\n\t\t\t\tconst mediaFiles = checklistCheckpointProperties.supportingMedia\r\n\t\t\t\t\t.map((media: any) => {\r\n\t\t\t\t\t\tif (typeof media === \"string\") {\r\n\t\t\t\t\t\t\treturn null; // Skip if media is a plain string (unexpected case)\r\n\t\t\t\t\t\t} else if (typeof media === \"object\" && media.Base64) {\r\n\t\t\t\t\t\t\treturn convertBase64ToFile(media.Base64, media.Name, media.Type); // ✅ Use actual file name\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn null;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.filter((file: any): file is File => file !== null); // ✅ Remove null values\r\n\r\n\t\t\t\t// ✅ Separate files correctly\r\n\t\t\t\tconst imageFiles = mediaFiles.filter((file: any) =>\r\n\t\t\t\t\t[\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)\r\n\t\t\t\t);\r\n\t\t\t\tconst gif = mediaFiles.find((file: any) => file.type === \"image/gif\") || null;\r\n\t\t\t\tconst video = mediaFiles.find((file: any) => file.type === \"video/mp4\") || null;\r\n\r\n\t\t\t\tsetFiles(imageFiles);\r\n\t\t\t\tsetGifFile(gif);\r\n\t\t\t\tsetVideoFile(video);\r\n\t\t\t}\r\n\t\t}, [checkpointsEditPopup, checklistCheckpointProperties.supportingMedia]);\r\n\r\n\t\tconst handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\t\tsetFileError(null);\r\n\t\t\tif (!event.target.files) return;\r\n\r\n\t\t\tconst newFiles = Array.from(event.target.files);\r\n\r\n\t\t\tconst base64Files = await Promise.all(\r\n\t\t\t\tnewFiles.map(async (file) => ({\r\n\t\t\t\t\tName: file.name,\r\n\t\t\t\t\tType: file.type,\r\n\t\t\t\t\tBase64: await convertFileToBase64(file),\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\r\n\t\t\tconst fileObjects = base64Files\r\n\t\t\t\t.map((fileData) => convertBase64ToFile(fileData.Base64, fileData.Name, fileData.Type))\r\n\t\t\t\t.filter((file): file is File => file !== null);\r\n\r\n\t\t\tconst newGifs = fileObjects.filter((file) => file.name.toLowerCase().endsWith(\".gif\"));\r\n\t\t\tconst newVideos = fileObjects.filter((file) => file.name.toLowerCase().endsWith(\".mp4\"));\r\n\t\t\tconst newImages = fileObjects.filter((file) =>\r\n\t\t\t\t[\".png\", \".jpg\", \".jpeg\"].some((ext) => file.name.toLowerCase().endsWith(ext))\r\n\t\t\t);\r\n\r\n\t\t\t// Validate types\r\n\t\t\tconst allTypes = Array.from(new Set(fileObjects.map((file) => file.type)));\r\n\t\t\tif (allTypes.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 1: Check if a GIF already exists\r\n\t\t\tif (gifFile && newGifs.length > 0) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF allowed\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 2: Check if a video already exists\r\n\t\t\tif (videoFile && newVideos.length > 0) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video allowed\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 3: If a GIF exists, prevent uploading any other type\r\n\t\t\tif (gifFile && (newVideos.length > 0 || newImages.length > 0)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 4: If a video exists, prevent uploading any other type\r\n\t\t\tif (videoFile && (newGifs.length > 0 || newImages.length > 0)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Case 5: If images exist, ensure new images are same type\r\n\t\t\tif (files.length > 0 && newImages.length > 0) {\r\n\t\t\t\tconst existingType = files[0].type;\r\n\t\t\t\tconst newImageType = newImages[0].type;\r\n\r\n\t\t\t\tconst isSameType = newImages.every((img) => img.type === existingType);\r\n\t\t\t\tif (!isSameType || newImageType !== existingType) {\r\n\t\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Case 6: If images exist and GIF/MP4 is being added\r\n\t\t\tif (files.length > 0 && (newGifs.length > 0 || newVideos.length > 0)) {\r\n\t\t\t\tsetFileError(\"Mixed file formats are not allowed.\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Set accepted files\r\n\t\t\tif (newGifs.length > 0) {\r\n\t\t\t\tsetGifFile(newGifs[0]);\r\n\t\t\t}\r\n\t\t\tif (newVideos.length > 0) {\r\n\t\t\t\tsetVideoFile(newVideos[0]);\r\n\t\t\t}\r\n\t\t\tif (newImages.length > 0) {\r\n\t\t\t\tsetFiles((prevFiles) => {\r\n\t\t\t\t\tconst updated = [...prevFiles, ...newImages];\r\n\t\t\t\t\tupdated.sort((a, b) => {\r\n\t\t\t\t\t\tconst aNum = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\t\tconst bNum = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\t\treturn aNum - bNum;\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn updated;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// Update media\r\n\t\t\tsetChecklistCheckpointProperties((prevState: any) => {\r\n\t\t\t\tconst updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\r\n\t\t\t\tupdatedMedia.sort((a, b) => {\r\n\t\t\t\t\tconst aNum = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\tconst bNum = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\treturn aNum - bNum;\r\n\t\t\t\t});\r\n\t\t\t\treturn { ...prevState, supportingMedia: updatedMedia };\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleDeleteFile = (index: number) => {\r\n\t\t\tsetFileError(null);\r\n\t\t\tsetFiles((prevFiles) => {\r\n\t\t\t\tconst updated = prevFiles.filter((_, i) => i !== index);\r\n\t\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t\t...prev,\r\n\t\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((_: any, i: any) => i !== index) || [],\r\n\t\t\t\t}));\r\n\t\t\t\treturn updated;\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleDeleteGif = () => {\r\n\t\t\tsetGifFile(null);\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(\".gif\")) || [],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleDeleteVideo = () => {\r\n\t\t\tsetVideoFile(null);\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tsupportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(\".mp4\")) || [],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst index = checklistGuideMetaData[0]?.checkpoints?.checkpointsList?.findIndex(\r\n\t\t\t(i: any) => i.id === editInteractionName\r\n\t\t);\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Step\")} {index + 1}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t <Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interaction\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t{/* Disabled Select-Like Display */}\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 13px) !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 8px 8px 8px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.interaction || \"\"}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\".MuiSelect-icon\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"none\", // Hide the dropdown arrow to keep the read-only feel\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder : \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"35px !important\" }\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem value={checklistCheckpointProperties.interaction}>\r\n\t\t\t\t\t\t\t\t\t\t\t{checklistCheckpointProperties.interaction || translate(\"No Interaction Selected\")}\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Title\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Title\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.title}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"title\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Description\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.description}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"description\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0\",\r\n\t\t\t\t\t\t\t\t\t\tmarginBottom: \"10px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Redirect URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: redirect }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: \"11px\", color: \"#8d8d8d\", textAlign: \"left\", padding: \"0\", marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Redirection URL\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.redirectURL}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"redirectURL\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Icon\")}</Typography>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tgap: 1,\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"-webkit-fill-available\",\r\n\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{icons.map((icon) => (\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\t\t\t\t\t\tkey={icon.id}\r\n\t\t\t\t\t\t\t\t\t\t\ttitle={translate(\"Select Icon\")}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t{/* <Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Icon Background Color</Typography>\r\n\t\t\t\t\t\t\t\t<Box sx={{ padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={\r\n\t\t\t\t\t\t\t\t\t\t\tchecklistCheckpointProperties.iconBackgroundColor ||\r\n\t\t\t\t\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsIcons ||\r\n\t\t\t\t\t\t\t\t\t\t\t\"#333\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"iconBackgroundColor\", e.target.value);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", height: \"30px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Supporting Media\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\twidth: \"165px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 8px 8px 8px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tborder: \"1px dashed var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tbackground: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-upload-button\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\", // Ensures icon & text are in one line\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\tcomponent=\"label\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloudUploadOutlinedIcon sx={{ zoom: \"1.6\" }} />\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Upload File\")}\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"file-input\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\tmultiple\r\n\t\t\t\t\t\t\t\t\t\t\taccept=\".jpeg, .jpg, .png, .gif, .mp4\" // ✅ Added MP4 support\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleFileChange}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t<Typography style={{ fontSize: \"12px\", color: \"#A3A3A3\" }}>.png, .jpg, .gif, .mp4</Typography>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t{fileError && (\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#e6a957\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"-webkit-fill-available\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ fontSize: \"12px\" }}>{fileError}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t{/* Display uploaded images */}\r\n\t\t\t\t\t\t\t\t<Box sx={{ width: \"-webkit-fill-available\" }}>\r\n\t\t\t\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t\t\t\t{files.map((file, index) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(file)}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt={`uploaded-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{file.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDeleteFile(index)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deletestep }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: \"1\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t{/* Display uploaded GIF separately */}\r\n\t\t\t\t\t\t\t\t\t{gifFile && (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(gifFile)}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"uploaded-gif\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{gifFile.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleDeleteGif}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deletestep }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: \"1\", display: \"flex\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{videoFile && (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #0a6\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"196px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e6ffe6\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<video\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth=\"40\"\r\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"40\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<source\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={URL.createObjectURL(videoFile)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"video/mp4\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\tYour browser does not support the video tag.\r\n\t\t\t\t\t\t\t\t\t\t\t</video>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{videoFile.name}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleDeleteVideo}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Media Title\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Title\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.mediaTitle}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"mediaTitle\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t//onChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Media Description\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.mediaDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\tif (value.length > 200) {\r\n\t\t\t\t\t\t\t\t\t\t\tvalue = value.slice(0, 200);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"mediaDescription\", value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\thelperText={`${checklistCheckpointProperties.mediaDescription?.length || 0}/200`}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\" },\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default CheckPointEditPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAACC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACjF,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,EAAkBC,WAAW,EAAcC,MAAM,EAAEC,QAAQ,EAA+CC,OAAO,QAA0B,eAAe;AACvN,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,SAMCC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,OAAO,QACD,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,QAAQ,kCAAkC;AAK/D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,mBAAmB;EAAEC;AAA+E,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACxI,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGZ,cAAc,CAAC,CAAC;EACtC,MAAM;IACPa,uBAAuB;IACvBC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,uBAAuB;IACvBC,0BAA0B;IAC1BC,6BAA6B;IAC7BC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,uBAAuB;IACvBC,iBAAiB;IACjBC,oBAAoB;IACpBC,sBAAsB;IACtBC,6BAA6B;IAC7BC,mBAAmB;IACnBC;EACD,CAAC,GAAGjD,cAAc,CAAEkD,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAMC,IAAI,GAAGL,sBAAsB,CAAC,CAAC,CAAC,CAACM,WAAW,CAACC,eAAe,CAACC,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKrC,mBAAmB,CAAC;EACjH,MAAMsC,cAAc,GAAIC,SAAiB,IAAK;IAC7C,OAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE;EACtD,CAAC;EAED,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAQ,MAAM;IAC/C,OAAO,CACN;MACCsE,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAACvD,OAAO,CAAC;MAC/B6D,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE/D;QAAQ,CAAE;QAC7CgE,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,EACD;MACCjB,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAACtD,OAAO,CAAC;MAC/B4D,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE9D;QAAQ,CAAE;QAC7C+D,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,EACD;MACCjB,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAACrD,OAAO,CAAC;MAC/B2D,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE7D;QAAQ,CAAE;QAC7C8D,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,EACD;MACCjB,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAACpD,OAAO,CAAC;MAC/B0D,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE5D;QAAQ,CAAE;QAC7C6D,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,EACD;MACCjB,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAACnD,OAAO,CAAC;MAC/ByD,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE3D;QAAQ,CAAE;QAC7C4D,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,EACD;MACCjB,EAAE,EAAE,CAAC;MACLM,MAAM,EAAEL,cAAc,CAAClD,OAAO,CAAC;MAC/BwD,SAAS,eACR9C,OAAA;QACC+C,uBAAuB,EAAE;UAAEC,MAAM,EAAE1D;QAAQ,CAAE;QAC7C2D,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACD;MACDC,QAAQ,EAAE;IACX,CAAC,CACD;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,6BAA6B,EAAEC,gCAAgC,CAAC,GAAGzF,QAAQ,CAAM,MAAM;IAC7F,MAAM0F,oCAAoC,GAAG;MAC5CC,WAAW,EAAE1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,WAAW;MAC9BC,KAAK,EAAE3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK;MAClBC,WAAW,EAAE5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,WAAW;MAC9BC,WAAW,EAAE7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,WAAW;MAC9BC,IAAI,EAAE,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,KAAIrB,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS;MACtCmB,eAAe,EAAE,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,eAAe,KAAI,EAAE;MAC5CC,UAAU,EAAEhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,UAAU;MAC5BC,gBAAgB,EAAEjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,gBAAgB;MACxC5B,EAAE,EAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK;IACX,CAAC;IACD,OAAOoB,oCAAoC;EAC5C,CAAC,CAAC;EAEF,MAAMS,+BAA+B,GAAIC,CAAM,IAAK7C,sBAAsB,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1F,MAAMC,gCAAgC,GAAIH,CAAM,IAAKjD,uBAAuB,CAACiD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC5F,MAAME,sCAAsC,GAAIJ,CAAM,IAAKjD,uBAAuB,CAACiD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAElG,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAG1G,QAAQ,CAAgB,IAAI,CAAC;EACvDC,SAAS,CAAC,MAAM;IACf,IAAIiC,oBAAoB,IAAIsD,6BAA6B,CAACO,IAAI,EAAE;MAC/DpB,QAAQ,CAAEgC,SAAS,IAClBA,SAAS,CAACC,GAAG,CAAEb,IAAI,KAAM;QACxB,GAAGA,IAAI;QACPR,QAAQ,EAAEQ,IAAI,CAACnB,MAAM,KAAKY,6BAA6B,CAACO,IAAI,CAAE;MAC/D,CAAC,CAAC,CACH,CAAC;IACF;EACD,CAAC,EAAE,CAAC7D,oBAAoB,EAAEsD,6BAA6B,CAACO,IAAI,CAAC,CAAC;EAE9D,MAAMc,eAAe,GAAIvC,EAAU,IAAK;IACvCK,QAAQ,CAAEgC,SAAS,IAClBA,SAAS,CAACC,GAAG,CAAEb,IAAI,KAAM;MACxB,GAAGA,IAAI;MACPR,QAAQ,EAAEQ,IAAI,CAACzB,EAAE,KAAKA;IACvB,CAAC,CAAC,CACH,CAAC;IAED,MAAMwC,YAAY,GAAGpC,KAAK,CAACN,IAAI,CAAE2B,IAAI,IAAKA,IAAI,CAACzB,EAAE,KAAKA,EAAE,CAAC;IACzD,IAAIwC,YAAY,EAAE;MACjBrB,gCAAgC,CAAEsB,IAAS,KAAM;QAChD,GAAGA,IAAI;QACPhB,IAAI,EAAEe,YAAY,CAAClC,MAAM,CAAE;MAC5B,CAAC,CAAC,CAAC;IACJ;EACD,CAAC;EAED,MAAMoC,gBAAgB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACxE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACZ,MAAM,CAACe,KAAK,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;IAEX,MAAME,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC;;IAExC;IACA,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC;IACnCK,GAAG,CAACK,MAAM,GAAG,MAAM;MAClB,IAAI,CAACR,KAAK,IAAIG,GAAG,CAACM,KAAK,GAAG,EAAE,IAAIN,GAAG,CAACO,MAAM,GAAG,EAAE,EAAE;QAChDrB,QAAQ,CAAC,8CAA8C,CAAC;MACzD,CAAC,MAAM;QACNA,QAAQ,CAAC,IAAI,CAAC;QACd/B,QAAQ,CAAEgC,SAAS,IAAK,CACvB,GAAGA,SAAS,EACZ;UACCrC,EAAE,EAAEqC,SAAS,CAACqB,MAAM,GAAG,CAAC;UACxBnD,SAAS,eACR9C,OAAA;YACC2F,GAAG,EAAEF,GAAG,CAACE,GAAI;YACbO,GAAG,EAAC,aAAa;YACjBH,KAAK,EAAE;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACD;UACDC,QAAQ,EAAE;QACX,CAAC,CACD,CAAC;MACH;IACD,CAAC;EACF,CAAC;EAED,MAAM2C,WAAW,GAAGA,CAAA,KAAM;IACzBxF,uBAAuB,CAAC,KAAK,CAAC;EAC/B,CAAC;EACD,MAAMyF,iBAAiB,GAAGA,CAAA,KAAM;IAC/BtF,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMuF,gBAAgB,GAAI9B,KAAa,IAAK;IAC3C,MAAM+B,QAAQ,GAAG,EAAE,GAAG,CAAC/B,KAAK,GAAG,CAAC,IAAI,CAAC;IACrCgC,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAAC;EACnC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAElC,MAAMD,gBAAgB,GAAGA,CAACE,GAAQ,EAAElC,KAAU,KAAK;IAClDb,gCAAgC,CAAEgD,SAAc,KAAM;MACrD,GAAGA,SAAS;MACZ,CAACD,GAAG,GAAGlC;IACR,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAChCC,YAAY,CAAC,IAAI,CAAC;IAElB9E,6BAA6B,CAAC2B,6BAA6B,CAAC;IAC5D0C,WAAW,CAAC,CAAC;IACbpE,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8E,eAAe,GAAGA,CAAA,KAAM;IAC7BlG,uBAAuB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAG9I,QAAQ,CAAQ,EAAE,CAAC;EAE3D,MAAM,CAAC+I,IAAI,EAAEC,OAAO,CAAC,GAAGhJ,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACiJ,OAAO,EAAEC,UAAU,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmJ,OAAO,EAAEC,UAAU,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMqJ,WAAW,GAAGnJ,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAM;IAAEoJ;EAAU,CAAC,GAAGnJ,UAAU,CAACwB,cAAc,CAAC;;EAEhD;EACA,MAAM4H,GAAG,GAAG,EAAE;;EAEd;EACAtJ,SAAS,CAAC,MAAM;IACfuJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAOC,OAAe,IAAK;IAC5C,IAAIR,OAAO,IAAI,CAACE,OAAO,EAAE,OAAO,CAAC;;IAEjCD,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMQ,OAAO,GAAG,CACf;MACCC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAER,SAAS;MAChBS,aAAa,EAAE;IAChB,CAAC,CACD;IAED,IAAI;MACH,MAAM9F,IAAI,GAAG,MAAMrC,YAAY,CAAC6H,OAAO,EAAEF,GAAG,EAAEG,OAAO,EAAE,EAAE,CAAC;MAC1D,MAAMM,eAAe,GAAG,CAAA/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,OAAO,KAAI,EAAE;MAE3C,IAAID,eAAe,CAAChC,MAAM,KAAK,CAAC,EAAE;QACjCoB,UAAU,CAAC,KAAK,CAAC;MAClB,CAAC,MAAM;QACNN,eAAe,CAAE/B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGiD,eAAe,CAAC,CAAC;QACxDhB,OAAO,CAACS,OAAO,GAAGF,GAAG,CAAC;MACvB;IACD,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACfyD,OAAO,CAACzD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACTyC,UAAU,CAAC,KAAK,CAAC;IAClB;EACD,CAAC;;EAED;EACA,MAAMiB,gBAAgB,GAAIlD,KAAoC,IAAK;IAClE,MAAM;MAAEmD,SAAS;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGrD,KAAK,CAACsD,aAAa;;IAErE;IACA,IAAIF,YAAY,GAAGD,SAAS,GAAGE,YAAY,GAAG,EAAE,IAAI,CAACrB,OAAO,IAAIE,OAAO,EAAE;MACxEK,SAAS,CAACT,IAAI,CAAC;IAChB;EACD,CAAC;EACD,MAAM,CAAC3B,KAAK,EAAEoD,QAAQ,CAAC,GAAGxK,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyK,OAAO,EAAEC,UAAU,CAAC,GAAG1K,QAAQ,CAAc,IAAI,CAAC;EACzD,MAAM,CAAC2K,SAAS,EAAEC,YAAY,CAAC,GAAG5K,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAAC6K,SAAS,EAAElC,YAAY,CAAC,GAAG3I,QAAQ,CAAgB,IAAI,CAAC;EAE/D,MAAM8K,mBAAmB,GAAI3D,IAAU,IAAsB;IAC5D,OAAO,IAAI4D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACvC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACjE,IAAI,CAAC;MAC1B+D,MAAM,CAACrD,MAAM,GAAG,MAAMmD,OAAO,CAACE,MAAM,CAACG,MAAgB,CAAC;MACtDH,MAAM,CAACI,OAAO,GAAI7E,KAAK,IAAKwE,MAAM,CAACxE,KAAK,CAAC;IAC1C,CAAC,CAAC;EACH,CAAC;EAED,MAAM8E,mBAAmB,GAAGA,CAAC3G,MAAc,EAAEO,QAAgB,EAAEqG,QAAgB,KAAK;IACnF,IAAI,CAAC5G,MAAM,EAAE;MACZsF,OAAO,CAACzD,KAAK,CAAC,6CAA6C,CAAC;MAC5D,OAAO,IAAI,CAAC,CAAC;IACd;;IAEA;IACA,MAAMgF,UAAU,GAAG7G,MAAM,CAAC8G,QAAQ,CAAC,GAAG,CAAC,GAAG9G,MAAM,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG/G,MAAM;IAEvE,IAAI;MACH,MAAMgH,cAAc,GAAGC,IAAI,CAACJ,UAAU,CAAC;MACvC,MAAMK,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAAC5D,MAAM,CAAC,CAACgE,IAAI,CAAC,IAAI,CAAC,CAACpF,GAAG,CAAC,CAACqF,CAAC,EAAEC,CAAC,KAAKN,cAAc,CAACO,UAAU,CAACD,CAAC,CAAC,CAAC;MAC3G,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACP,WAAW,CAAC;MAE7C,OAAO,IAAIQ,IAAI,CAAC,CAACF,SAAS,CAAC,EAAEjH,QAAQ,EAAE;QAAEoH,IAAI,EAAEf;MAAS,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAO/E,KAAK,EAAE;MACfyD,OAAO,CAACzD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,IAAI;IACZ;EACD,CAAC;EACDxG,SAAS,CAAC,MAAM;IAAA,IAAAuM,qBAAA;IACf,IAAItK,oBAAoB,IAAI,EAAAsK,qBAAA,GAAAhH,6BAA6B,CAACQ,eAAe,cAAAwG,qBAAA,uBAA7CA,qBAAA,CAA+CxE,MAAM,IAAG,CAAC,EAAE;MACtF,MAAMyE,UAAU,GAAGjH,6BAA6B,CAACQ,eAAe,CAC9DY,GAAG,CAAE8F,KAAU,IAAK;QACpB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC9B,OAAO,IAAI,CAAC,CAAC;QACd,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,MAAM,EAAE;UACrD,OAAOpB,mBAAmB,CAACmB,KAAK,CAACC,MAAM,EAAED,KAAK,CAACE,IAAI,EAAEF,KAAK,CAACG,IAAI,CAAC,CAAC,CAAC;QACnE;QACA,OAAO,IAAI;MACZ,CAAC,CAAC,CACDC,MAAM,CAAE3F,IAAS,IAAmBA,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;;MAEtD;MACA,MAAM4F,UAAU,GAAGN,UAAU,CAACK,MAAM,CAAE3F,IAAS,IAC9C,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAACuE,QAAQ,CAACvE,IAAI,CAACoF,IAAI,CAC5D,CAAC;MACD,MAAMS,GAAG,GAAGP,UAAU,CAACrI,IAAI,CAAE+C,IAAS,IAAKA,IAAI,CAACoF,IAAI,KAAK,WAAW,CAAC,IAAI,IAAI;MAC7E,MAAMU,KAAK,GAAGR,UAAU,CAACrI,IAAI,CAAE+C,IAAS,IAAKA,IAAI,CAACoF,IAAI,KAAK,WAAW,CAAC,IAAI,IAAI;MAE/E/B,QAAQ,CAACuC,UAAU,CAAC;MACpBrC,UAAU,CAACsC,GAAG,CAAC;MACfpC,YAAY,CAACqC,KAAK,CAAC;IACpB;EACD,CAAC,EAAE,CAAC/K,oBAAoB,EAAEsD,6BAA6B,CAACQ,eAAe,CAAC,CAAC;EAEzE,MAAMkH,gBAAgB,GAAG,MAAOjG,KAA0C,IAAK;IAC9E0B,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI,CAAC1B,KAAK,CAACZ,MAAM,CAACe,KAAK,EAAE;IAEzB,MAAM+F,QAAQ,GAAGpB,KAAK,CAACqB,IAAI,CAACnG,KAAK,CAACZ,MAAM,CAACe,KAAK,CAAC;IAE/C,MAAMiG,WAAW,GAAG,MAAMtC,OAAO,CAACuC,GAAG,CACpCH,QAAQ,CAACvG,GAAG,CAAC,MAAOO,IAAI,KAAM;MAC7ByF,IAAI,EAAEzF,IAAI,CAACG,IAAI;MACfuF,IAAI,EAAE1F,IAAI,CAACoF,IAAI;MACfI,MAAM,EAAE,MAAM7B,mBAAmB,CAAC3D,IAAI;IACvC,CAAC,CAAC,CACH,CAAC;IAED,MAAMoG,WAAW,GAAGF,WAAW,CAC7BzG,GAAG,CAAE4G,QAAQ,IAAKjC,mBAAmB,CAACiC,QAAQ,CAACb,MAAM,EAAEa,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACX,IAAI,CAAC,CAAC,CACrFC,MAAM,CAAE3F,IAAI,IAAmBA,IAAI,KAAK,IAAI,CAAC;IAE/C,MAAMsG,OAAO,GAAGF,WAAW,CAACT,MAAM,CAAE3F,IAAI,IAAKA,IAAI,CAACG,IAAI,CAACoG,WAAW,CAAC,CAAC,CAACnG,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtF,MAAMoG,SAAS,GAAGJ,WAAW,CAACT,MAAM,CAAE3F,IAAI,IAAKA,IAAI,CAACG,IAAI,CAACoG,WAAW,CAAC,CAAC,CAACnG,QAAQ,CAAC,MAAM,CAAC,CAAC;IACxF,MAAMqG,SAAS,GAAGL,WAAW,CAACT,MAAM,CAAE3F,IAAI,IACzC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC0G,IAAI,CAAEC,GAAG,IAAK3G,IAAI,CAACG,IAAI,CAACoG,WAAW,CAAC,CAAC,CAACnG,QAAQ,CAACuG,GAAG,CAAC,CAC9E,CAAC;;IAED;IACA,MAAMC,QAAQ,GAAGhC,KAAK,CAACqB,IAAI,CAAC,IAAIY,GAAG,CAACT,WAAW,CAAC3G,GAAG,CAAEO,IAAI,IAAKA,IAAI,CAACoF,IAAI,CAAC,CAAC,CAAC;IAC1E,IAAIwB,QAAQ,CAAC/F,MAAM,GAAG,CAAC,EAAE;MACxBW,YAAY,CAAClG,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9D;IACD;;IAEA;IACA,IAAIgI,OAAO,IAAIgD,OAAO,CAACzF,MAAM,GAAG,CAAC,EAAE;MAClCW,YAAY,CAAClG,SAAS,CAAC,sBAAsB,CAAC,CAAC;MAC/C;IACD;;IAEA;IACA,IAAIkI,SAAS,IAAIgD,SAAS,CAAC3F,MAAM,GAAG,CAAC,EAAE;MACtCW,YAAY,CAAClG,SAAS,CAAC,wBAAwB,CAAC,CAAC;MACjD;IACD;;IAEA;IACA,IAAIgI,OAAO,KAAKkD,SAAS,CAAC3F,MAAM,GAAG,CAAC,IAAI4F,SAAS,CAAC5F,MAAM,GAAG,CAAC,CAAC,EAAE;MAC9DW,YAAY,CAAClG,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9D;IACD;;IAEA;IACA,IAAIkI,SAAS,KAAK8C,OAAO,CAACzF,MAAM,GAAG,CAAC,IAAI4F,SAAS,CAAC5F,MAAM,GAAG,CAAC,CAAC,EAAE;MAC9DW,YAAY,CAAClG,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9D;IACD;;IAEA;IACA,IAAI2E,KAAK,CAACY,MAAM,GAAG,CAAC,IAAI4F,SAAS,CAAC5F,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMiG,YAAY,GAAG7G,KAAK,CAAC,CAAC,CAAC,CAACmF,IAAI;MAClC,MAAM2B,YAAY,GAAGN,SAAS,CAAC,CAAC,CAAC,CAACrB,IAAI;MAEtC,MAAM4B,UAAU,GAAGP,SAAS,CAACQ,KAAK,CAAE5G,GAAG,IAAKA,GAAG,CAAC+E,IAAI,KAAK0B,YAAY,CAAC;MACtE,IAAI,CAACE,UAAU,IAAID,YAAY,KAAKD,YAAY,EAAE;QACjDtF,YAAY,CAAClG,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC9D;MACD;IACD;;IAEA;IACA,IAAI2E,KAAK,CAACY,MAAM,GAAG,CAAC,KAAKyF,OAAO,CAACzF,MAAM,GAAG,CAAC,IAAI2F,SAAS,CAAC3F,MAAM,GAAG,CAAC,CAAC,EAAE;MACrEW,YAAY,CAAC,qCAAqC,CAAC;MACnD;IACD;;IAEA;IACA,IAAI8E,OAAO,CAACzF,MAAM,GAAG,CAAC,EAAE;MACvB0C,UAAU,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,IAAIE,SAAS,CAAC3F,MAAM,GAAG,CAAC,EAAE;MACzB4C,YAAY,CAAC+C,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,IAAIC,SAAS,CAAC5F,MAAM,GAAG,CAAC,EAAE;MACzBwC,QAAQ,CAAE6D,SAAS,IAAK;QACvB,MAAMC,OAAO,GAAG,CAAC,GAAGD,SAAS,EAAE,GAAGT,SAAS,CAAC;QAC5CU,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACtB,MAAMC,IAAI,GAAGF,CAAC,CAAClH,IAAI,CAACqH,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACJ,CAAC,CAAClH,IAAI,CAACqH,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UAC5E,MAAME,IAAI,GAAGJ,CAAC,CAACnH,IAAI,CAACqH,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACH,CAAC,CAACnH,IAAI,CAACqH,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UAC5E,OAAOD,IAAI,GAAGG,IAAI;QACnB,CAAC,CAAC;QACF,OAAOP,OAAO;MACf,CAAC,CAAC;IACH;;IAEA;IACA7I,gCAAgC,CAAEgD,SAAc,IAAK;MACpD,MAAMqG,YAAY,GAAG,CAAC,IAAIrG,SAAS,CAACzC,eAAe,IAAI,EAAE,CAAC,EAAE,GAAGqH,WAAW,CAAC;MAC3EyB,YAAY,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC3B,MAAMC,IAAI,GAAGF,CAAC,CAAC5B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACJ,CAAC,CAAC5B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC5E,MAAME,IAAI,GAAGJ,CAAC,CAAC7B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACH,CAAC,CAAC7B,IAAI,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC5E,OAAOD,IAAI,GAAGG,IAAI;MACnB,CAAC,CAAC;MACF,OAAO;QAAE,GAAGpG,SAAS;QAAEzC,eAAe,EAAE8I;MAAa,CAAC;IACvD,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IAC3CrG,YAAY,CAAC,IAAI,CAAC;IAClB6B,QAAQ,CAAE6D,SAAS,IAAK;MACvB,MAAMC,OAAO,GAAGD,SAAS,CAACvB,MAAM,CAAC,CAACb,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAK8C,KAAK,CAAC;MACvDvJ,gCAAgC,CAAEsB,IAAS;QAAA,IAAAkI,qBAAA;QAAA,OAAM;UAChD,GAAGlI,IAAI;UACPf,eAAe,EAAE,EAAAiJ,qBAAA,GAAAlI,IAAI,CAACf,eAAe,cAAAiJ,qBAAA,uBAApBA,qBAAA,CAAsBnC,MAAM,CAAC,CAACb,CAAM,EAAEC,CAAM,KAAKA,CAAC,KAAK8C,KAAK,CAAC,KAAI;QACnF,CAAC;MAAA,CAAC,CAAC;MACH,OAAOV,OAAO;IACf,CAAC,CAAC;EACH,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC7BxE,UAAU,CAAC,IAAI,CAAC;IAChBjF,gCAAgC,CAAEsB,IAAS;MAAA,IAAAoI,sBAAA;MAAA,OAAM;QAChD,GAAGpI,IAAI;QACPf,eAAe,EAAE,EAAAmJ,sBAAA,GAAApI,IAAI,CAACf,eAAe,cAAAmJ,sBAAA,uBAApBA,sBAAA,CAAsBrC,MAAM,CAAE3F,IAAS;UAAA,IAAAiI,UAAA;UAAA,OAAK,GAAAA,UAAA,GAACjI,IAAI,CAACyF,IAAI,cAAAwC,UAAA,eAATA,UAAA,CAAW1B,WAAW,CAAC,CAAC,CAACnG,QAAQ,CAAC,MAAM,CAAC;QAAA,EAAC,KAAI;MAC7G,CAAC;IAAA,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8H,iBAAiB,GAAGA,CAAA,KAAM;IAC/BzE,YAAY,CAAC,IAAI,CAAC;IAClBnF,gCAAgC,CAAEsB,IAAS;MAAA,IAAAuI,sBAAA;MAAA,OAAM;QAChD,GAAGvI,IAAI;QACPf,eAAe,EAAE,EAAAsJ,sBAAA,GAAAvI,IAAI,CAACf,eAAe,cAAAsJ,sBAAA,uBAApBA,sBAAA,CAAsBxC,MAAM,CAAE3F,IAAS;UAAA,IAAAoI,WAAA;UAAA,OAAK,GAAAA,WAAA,GAACpI,IAAI,CAACyF,IAAI,cAAA2C,WAAA,eAATA,WAAA,CAAW7B,WAAW,CAAC,CAAC,CAACnG,QAAQ,CAAC,MAAM,CAAC;QAAA,EAAC,KAAI;MAC7G,CAAC;IAAA,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyH,KAAK,IAAA5M,qBAAA,GAAGwB,sBAAsB,CAAC,CAAC,CAAC,cAAAxB,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B8B,WAAW,cAAA7B,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwC8B,eAAe,cAAA7B,sBAAA,uBAAvDA,sBAAA,CAAyDkN,SAAS,CAC9EtD,CAAM,IAAKA,CAAC,CAAC5H,EAAE,KAAKrC,mBACtB,CAAC;EAED,oBACCF,OAAA;IACCuC,EAAE,EAAC,mBAAmB;IACtBmL,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7B3N,OAAA;MAAK0N,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B3N,OAAA;QAAK0N,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC3N,OAAA,CAACxB,UAAU;UACV,cAAW,MAAM;UACjBoP,OAAO,EAAEzH,WAAY;UAAAwH,QAAA,eAErB3N,OAAA,CAACN,2BAA2B;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbvD,OAAA;UAAK0N,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAEjN,SAAS,CAAC,MAAM,CAAC,EAAC,GAAC,EAACuM,KAAK,GAAG,CAAC;QAAA;UAAA7J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClEvD,OAAA,CAACxB,UAAU;UACVqP,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBD,OAAO,EAAEzH,WAAY;UAAAwH,QAAA,eAErB3N,OAAA,CAAClB,SAAS;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENvD,OAAA;QAAK0N,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9B3N,OAAA;UAAK0N,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9B3N,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAEjD3N,OAAA,CAAC1B,UAAU;cAACoP,SAAS,EAAC,qBAAqB;cAACI,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAuB,CAAE;cAAAJ,QAAA,EACnFjN,SAAS,CAAC,aAAa;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGdvD,OAAA,CAACtB,WAAW;cACXsP,OAAO,EAAC,UAAU;cAClBC,SAAS;cACTP,SAAS,EAAC,qBAAqB;cAC/BI,EAAE,EAAE;gBACH/H,KAAK,EAAE,8BAA8B;gBACrCmI,YAAY,EAAE,MAAM;gBACpBH,OAAO,EAAE,eAAe;gBACxBI,MAAM,EAAE;cACT,CAAE;cAAAR,QAAA,eAEF3N,OAAA,CAACrB,MAAM;gBACNyP,YAAY;gBACZC,QAAQ;gBACR9J,KAAK,EAAEd,6BAA6B,CAACG,WAAW,IAAI,EAAG;gBACvDkK,EAAE,EAAE;kBACH/H,KAAK,EAAE,iBAAiB;kBACxBmI,YAAY,EAAE,MAAM;kBACpBI,eAAe,EAAE,SAAS;kBAC1BP,OAAO,EAAE,MAAM;kBACfQ,KAAK,EAAE,MAAM;kBACb,iBAAiB,EAAE;oBAClBpL,OAAO,EAAE,MAAM,CAAE;kBAClB,CAAC;kBACD,0BAA0B,EAAE;oBAC3B,SAAS,EAAE;sBACVqL,WAAW,EAAE;oBACd,CAAC;oBACD,eAAe,EAAE;sBAChBA,WAAW,EAAE;oBACd;kBACD,CAAC;kBACD,oCAAoC,EAAE;oBACrCC,MAAM,EAAG;kBACV,CAAC;kBACD,qBAAqB,EAAE;oBAAEzI,MAAM,EAAE;kBAAkB;gBACpD,CAAE;gBAAA2H,QAAA,eAEF3N,OAAA,CAACpB,QAAQ;kBAAC2F,KAAK,EAAEd,6BAA6B,CAACG,WAAY;kBAAA+J,QAAA,EACzDlK,6BAA6B,CAACG,WAAW,IAAIlD,SAAS,CAAC,yBAAyB;gBAAC;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA,CAAC1B,UAAU;cACVoP,SAAS,EAAC,qBAAqB;cAC/BI,EAAE,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEW,YAAY,EAAE;cAAiB,CAAE;cAAAf,QAAA,EAE/DjN,SAAS,CAAC,OAAO;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEbvD,OAAA,CAACzB,SAAS;cACTyP,OAAO,EAAC,UAAU;cAClBH,IAAI,EAAC,OAAO;cACZc,WAAW,EAAEjO,SAAS,CAAC,YAAY,CAAE;cACrCgN,SAAS,EAAC,qBAAqB;cAC/BzK,KAAK,EAAE;gBAAE8C,KAAK,EAAE;cAAO,CAAE;cACzBxB,KAAK,EAAEd,6BAA6B,CAACI,KAAM;cAC3C+K,QAAQ,EAAGvK,CAAC,IAAKkC,gBAAgB,CAAC,OAAO,EAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC3DsK,UAAU,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEW,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBAChC,SAAS,EAAE;oBAAEM,SAAS,EAAE,iBAAiB;oBAAEC,WAAW,EAAE;kBAAkB,CAAC;kBAC3E,qBAAqB,EAAE;oBAAEhJ,MAAM,EAAE;kBAAkB;gBACpD;cACD;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA,CAAC1B,UAAU;cACVoP,SAAS,EAAC,qBAAqB;cAC/BI,EAAE,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEW,YAAY,EAAE;cAAiB,CAAE;cAAAf,QAAA,EAE/DjN,SAAS,CAAC,aAAa;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAEbvD,OAAA,CAACzB,SAAS;cACTyP,OAAO,EAAC,UAAU;cAClBH,IAAI,EAAC,OAAO;cACZc,WAAW,EAAEjO,SAAS,CAAC,WAAW,CAAE;cACpCgN,SAAS,EAAC,qBAAqB;cAC/BuB,SAAS;cACTC,OAAO,EAAE,CAAE;cACXjM,KAAK,EAAE;gBAAE8C,KAAK,EAAE;cAAO,CAAE;cACzBxB,KAAK,EAAEd,6BAA6B,CAACK,WAAY;cACjD8K,QAAQ,EAAGvK,CAAC,IAAKkC,gBAAgB,CAAC,aAAa,EAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACjEsK,UAAU,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEW,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBAChC,SAAS,EAAE;oBAAEM,SAAS,EAAE,iBAAiB;oBAAEC,WAAW,EAAE;kBAAkB,CAAC;kBAC3E,qBAAqB,EAAE;oBAAEhJ,MAAM,EAAE;kBAAkB;gBACpD;cACD;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA;cACC0N,SAAS,EAAC,qBAAqB;cAC/BzK,KAAK,EAAE;gBACNE,OAAO,EAAE,MAAM;gBACfgM,aAAa,EAAE,KAAK;gBACpBC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,KAAK;gBACVtB,OAAO,EAAE,GAAG;gBACZW,YAAY,EAAE;cACf,CAAE;cAAAf,QAAA,gBAEF3N,OAAA,CAAC1B,UAAU;gBAACwP,EAAE,EAAE;kBAAES,KAAK,EAAE,SAAS;kBAAEe,UAAU,EAAE;gBAAM,CAAE;gBAAA3B,QAAA,EAAEjN,SAAS,CAAC,cAAc;cAAC;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjGvD,OAAA;gBACC+C,uBAAuB,EAAE;kBAAEC,MAAM,EAAExD;gBAAS,CAAE;gBAC9CyD,KAAK,EAAE;kBAAEE,OAAO,EAAE;gBAAO;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EAAC,GAAG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENvD,OAAA,CAAC1B,UAAU;cACV2E,KAAK,EAAE;gBAAEsM,QAAQ,EAAE,MAAM;gBAAEhB,KAAK,EAAE,SAAS;gBAAEQ,SAAS,EAAE,MAAM;gBAAEhB,OAAO,EAAE,GAAG;gBAAEW,YAAY,EAAE;cAAO,CAAE;cAAAf,QAAA,EAEpGjN,SAAS,CAAC,+FAA+F;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACbvD,OAAA,CAACzB,SAAS;cACTyP,OAAO,EAAC,UAAU;cAClBH,IAAI,EAAC,OAAO;cACZc,WAAW,EAAEjO,SAAS,CAAC,iBAAiB,CAAE;cAC1CgN,SAAS,EAAC,qBAAqB;cAC/BzK,KAAK,EAAE;gBAAE8C,KAAK,EAAE;cAAO,CAAE;cACzBxB,KAAK,EAAEd,6BAA6B,aAA7BA,6BAA6B,uBAA7BA,6BAA6B,CAAEM,WAAY;cAClD6K,QAAQ,EAAGvK,CAAC,IAAKkC,gBAAgB,CAAC,aAAa,EAAElC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACjEsK,UAAU,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEW,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBAChC,SAAS,EAAE;oBAAEM,SAAS,EAAE,iBAAiB;oBAAEC,WAAW,EAAE;kBAAkB,CAAC;kBAC3E,qBAAqB,EAAE;oBAAEhJ,MAAM,EAAE;kBAAkB;gBACpD;cACD;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA,CAAC1B,UAAU;cAACoP,SAAS,EAAC,qBAAqB;cAACI,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAuB,CAAE;cAAAJ,QAAA,EAAEjN,SAAS,CAAC,MAAM;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrHvD,OAAA,CAAC3B,GAAG;cACHyP,EAAE,EAAE;gBACH3K,OAAO,EAAE,MAAM;gBACfkM,GAAG,EAAE,CAAC;gBACND,UAAU,EAAE,QAAQ;gBACpBrJ,KAAK,EAAE,wBAAwB;gBAC/ByJ,QAAQ,EAAE;cACX,CAAE;cAAA7B,QAAA,EAEDhL,KAAK,CAACkC,GAAG,CAAEb,IAAI,iBACfhE,OAAA,CAACnB,OAAO;gBACP4Q,KAAK;gBAEL5L,KAAK,EAAEnD,SAAS,CAAC,aAAa,CAAE;gBAAAiN,QAAA,eAEhC3N,OAAA,CAACxB,UAAU;kBACVoP,OAAO,EAAEA,CAAA,KAAM9I,eAAe,CAACd,IAAI,CAACzB,EAAE,CAAE;kBACxCuL,EAAE,EAAE;oBACHW,MAAM,EAAEzK,IAAI,CAACR,QAAQ,GAAG,+BAA+B,GAAG,MAAM;oBAChE0K,YAAY,EAAE,KAAK;oBACnBH,OAAO,EAAE,KAAK;oBACd2B,UAAU,EAAE;kBACb,CAAE;kBAAA/B,QAAA,EAED3J,IAAI,CAAClB;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC,GAbRS,IAAI,CAACzB,EAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAyBNvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA,CAAC1B,UAAU;cAACoP,SAAS,EAAC,qBAAqB;cAACI,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAuB,CAAE;cAAAJ,QAAA,EAAEjN,SAAS,CAAC,kBAAkB;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAEjIvD,OAAA;cACCiD,KAAK,EAAE;gBACN8C,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,MAAM;gBACdmI,MAAM,EAAE,eAAe;gBACvBhL,OAAO,EAAE,MAAM;gBACfgM,aAAa,EAAE,QAAQ;gBACvBC,UAAU,EAAE,QAAQ;gBACpBO,cAAc,EAAE,QAAQ;gBACxBlB,MAAM,EAAE,gCAAgC;gBACxCP,YAAY,EAAE,MAAM;gBACpBH,OAAO,EAAE,KAAK;gBACd2B,UAAU,EAAE,SAAS;gBACrBX,SAAS,EAAE;cACZ,CAAE;cAAApB,QAAA,gBAEF3N,OAAA,CAACvB,MAAM;gBACNiP,SAAS,EAAC,qBAAqB;gBAC/BzK,KAAK,EAAE;kBACN+C,MAAM,EAAE,MAAM;kBACd+H,OAAO,EAAE,GAAG;kBACZhI,KAAK,EAAE,MAAM;kBACb5C,OAAO,EAAE,MAAM;kBACfgM,aAAa,EAAE,KAAK;kBAAE;kBACtBC,UAAU,EAAE,QAAQ;kBACpBO,cAAc,EAAE,QAAQ;kBACxBN,GAAG,EAAE,KAAK;kBACVd,KAAK,EAAE,MAAM;kBACbD,eAAe,EAAE,SAAS;kBAC1BsB,aAAa,EAAE,YAAY;kBAC3BC,SAAS,EAAE;gBACZ,CAAE;gBACF7B,OAAO,EAAC,WAAW;gBACnBlL,SAAS,EAAC,OAAO;gBAAA6K,QAAA,gBAEjB3N,OAAA,CAACL,uBAAuB;kBAACmO,EAAE,EAAE;oBAAE5K,IAAI,EAAE;kBAAM;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/C7C,SAAS,CAAC,aAAa,CAAC,eACzBV,OAAA;kBACCuC,EAAE,EAAC,YAAY;kBACfiI,IAAI,EAAC,MAAM;kBACXsF,QAAQ;kBACRC,MAAM,EAAC,+BAA+B,CAAC;kBAAA;kBACvCnB,QAAQ,EAAEzD,gBAAiB;kBAC3BlI,KAAK,EAAE;oBAAEE,OAAO,EAAE;kBAAO;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACTvD,OAAA,CAAC1B,UAAU;gBAAC2E,KAAK,EAAE;kBAAEsM,QAAQ,EAAE,MAAM;kBAAEhB,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAAsB;gBAAAvK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC,EACLuF,SAAS,iBACT9I,OAAA;cACCiD,KAAK,EAAE;gBACNE,OAAO,EAAE,MAAM;gBACfiM,UAAU,EAAE,QAAQ;gBACpBb,KAAK,EAAE,SAAS;gBAChBxI,KAAK,EAAE,wBAAwB;gBAC/BgI,OAAO,EAAE,OAAO;gBAChBgB,SAAS,EAAE;cACZ,CAAE;cAAApB,QAAA,gBAEF3N,OAAA;gBACCiD,KAAK,EAAE;kBAAE+M,WAAW,EAAE,KAAK;kBAAE7M,OAAO,EAAE;gBAAO,CAAE;gBAC/CJ,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvD;gBAAQ;cAAE;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACFvD,OAAA;gBAAKiD,KAAK,EAAE;kBAAEsM,QAAQ,EAAE;gBAAO,CAAE;gBAAA5B,QAAA,EAAE7E;cAAS;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CACL,eAGDvD,OAAA,CAAC3B,GAAG;cAACyP,EAAE,EAAE;gBAAE/H,KAAK,EAAE;cAAyB,CAAE;cAAA4H,QAAA,GAC3C,GAAG,EACHtI,KAAK,CAACR,GAAG,CAAC,CAACO,IAAI,EAAE6H,KAAK,kBACtBjN,OAAA,CAAC3B,GAAG;gBAEH8E,OAAO,EAAC,MAAM;gBACdiM,UAAU,EAAC,QAAQ;gBACnBO,cAAc,EAAC,eAAe;gBAC9B7B,EAAE,EAAE;kBACHI,YAAY,EAAE,MAAM;kBACpBH,OAAO,EAAE,KAAK;kBACdI,MAAM,EAAE,KAAK;kBACbG,eAAe,EAAE;gBAClB,CAAE;gBAAAX,QAAA,gBAEF3N,OAAA;kBACC2F,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAE;kBAC/Bc,GAAG,EAAE,YAAY+G,KAAK,EAAG;kBACzBhK,KAAK,EAAE;oBAAE8C,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEkI,YAAY,EAAE;kBAAM;gBAAE;kBAAA9K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eAEFvD,OAAA,CAAC1B,UAAU;kBAACwP,EAAE,EAAE;oBAAEmC,IAAI,EAAE,CAAC;oBAAEC,EAAE,EAAE,CAAC;oBAAEX,QAAQ,EAAE,MAAM;oBAAEY,SAAS,EAAE;kBAAa,CAAE;kBAAAxC,QAAA,EAC5EvI,IAAI,CAACG;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACbvD,OAAA,CAACxB,UAAU;kBACVoP,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAACC,KAAK,CAAE;kBACvCY,IAAI,EAAC,OAAO;kBAAAF,QAAA,gBAEZ3N,OAAA;oBACC+C,uBAAuB,EAAE;sBAAEC,MAAM,EAAEzD;oBAAW,CAAE;oBAChD0D,KAAK,EAAE;sBAAEC,IAAI,EAAE,GAAG;sBAAEC,OAAO,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA,GA5BR0J,KAAK;gBAAA7J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BN,CACL,CAAC,EAEDmF,OAAO,iBACP1I,OAAA,CAAC3B,GAAG;gBACH8E,OAAO,EAAC,MAAM;gBACdiM,UAAU,EAAC,QAAQ;gBACnBO,cAAc,EAAC,eAAe;gBAC9B7B,EAAE,EAAE;kBACHI,YAAY,EAAE,MAAM;kBACpBH,OAAO,EAAE,KAAK;kBACdI,MAAM,EAAE,KAAK;kBACbG,eAAe,EAAE;gBAClB,CAAE;gBAAAX,QAAA,gBAEF3N,OAAA;kBACC2F,GAAG,EAAEC,GAAG,CAACC,eAAe,CAAC6C,OAAO,CAAE;kBAClCxC,GAAG,EAAC,cAAc;kBAClBjD,KAAK,EAAE;oBAAE8C,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEkI,YAAY,EAAE;kBAAM;gBAAE;kBAAA9K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACFvD,OAAA,CAAC1B,UAAU;kBAACwP,EAAE,EAAE;oBAAEmC,IAAI,EAAE,CAAC;oBAAEV,QAAQ,EAAE,MAAM;oBAAEY,SAAS,EAAE;kBAAa,CAAE;kBAAAxC,QAAA,EACrEjF,OAAO,CAACnD;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbvD,OAAA,CAACxB,UAAU;kBACVoP,OAAO,EAAET,eAAgB;kBACzBU,IAAI,EAAC,OAAO;kBAAAF,QAAA,gBAEZ3N,OAAA;oBACC+C,uBAAuB,EAAE;sBAAEC,MAAM,EAAEzD;oBAAW,CAAE;oBAChD0D,KAAK,EAAE;sBAAEC,IAAI,EAAE,GAAG;sBAAEC,OAAO,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACL,EACAqF,SAAS,iBACT5I,OAAA,CAAC3B,GAAG;gBACH8E,OAAO,EAAC,MAAM;gBACdiM,UAAU,EAAC,QAAQ;gBACnBO,cAAc,EAAC,eAAe;gBAC9B7B,EAAE,EAAE;kBACHW,MAAM,EAAE,gBAAgB;kBACxBP,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE,KAAK;kBACdW,YAAY,EAAE,KAAK;kBACnB3I,KAAK,EAAE,OAAO;kBACduI,eAAe,EAAE;gBAClB,CAAE;gBAAAX,QAAA,gBAEF3N,OAAA;kBACC+F,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXoK,QAAQ;kBAAAzC,QAAA,gBAER3N,OAAA;oBACC2F,GAAG,EAAEC,GAAG,CAACC,eAAe,CAAC+C,SAAS,CAAE;oBACpC4B,IAAI,EAAC;kBAAW;oBAAApH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gDAEH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvD,OAAA,CAAC1B,UAAU;kBAACwP,EAAE,EAAE;oBAAEmC,IAAI,EAAE,CAAC;oBAAEC,EAAE,EAAE,CAAC;oBAAEX,QAAQ,EAAE,MAAM;oBAAEY,SAAS,EAAE;kBAAa,CAAE;kBAAAxC,QAAA,EAC5E/E,SAAS,CAACrD;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbvD,OAAA,CAACxB,UAAU;kBACVoP,OAAO,EAAEN,iBAAkB;kBAC3BO,IAAI,EAAC,OAAO;kBAAAF,QAAA,eAEZ3N,OAAA;oBACC+C,uBAAuB,EAAE;sBAAEC,MAAM,EAAEhE;oBAAW,CAAE;oBAChDiE,KAAK,EAAE;sBAAEC,IAAI,EAAE;oBAAI;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAElD3N,OAAA,CAAC1B,UAAU;cACVoP,SAAS,EAAC,qBAAqB;cAC/BI,EAAE,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEW,YAAY,EAAE;cAAiB,CAAE;cAAAf,QAAA,EAE/DjN,SAAS,CAAC,aAAa;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAEbvD,OAAA,CAACzB,SAAS;cACTyP,OAAO,EAAC,UAAU;cAClBH,IAAI,EAAC,OAAO;cACZc,WAAW,EAAEjO,SAAS,CAAC,aAAa,CAAE;cACtCgN,SAAS,EAAC,qBAAqB;cAC/BzK,KAAK,EAAE;gBAAE8C,KAAK,EAAE;cAAO,CAAE;cACzBxB,KAAK,EAAEd,6BAA6B,CAACS,UAAW;cAChD0K,QAAQ,EAAGvK,CAAC,IAAKkC,gBAAgB,CAAC,YAAY,EAAElC,CAAC,CAACC,MAAM,CAACC,KAAK;cAC9D;cAAA;cACAsK,UAAU,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEW,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBAChC,SAAS,EAAE;oBAAEM,SAAS,EAAE,iBAAiB;oBAAEC,WAAW,EAAE;kBAAkB,CAAC;kBAC3E,qBAAqB,EAAE;oBAAEhJ,MAAM,EAAE;kBAAkB;gBACpD;cACD;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENvD,OAAA,CAAC3B,GAAG;YACHkE,EAAE,EAAC,mBAAmB;YACtBmL,SAAS,EAAC,wCAAwC;YAClDI,EAAE,EAAE;cAAEqB,aAAa,EAAE,QAAQ;cAAEnJ,MAAM,EAAE,iBAAiB;cAAE+H,OAAO,EAAE;YAAiB,CAAE;YAAAJ,QAAA,gBAEtF3N,OAAA,CAAC1B,UAAU;cACVoP,SAAS,EAAC,qBAAqB;cAC/BI,EAAE,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEW,YAAY,EAAE;cAAiB,CAAE;cAAAf,QAAA,EAE/DjN,SAAS,CAAC,mBAAmB;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEbvD,OAAA,CAACzB,SAAS;cACTyP,OAAO,EAAC,UAAU;cAClBH,IAAI,EAAC,OAAO;cACZc,WAAW,EAAEjO,SAAS,CAAC,YAAY,CAAE;cACrCgN,SAAS,EAAC,qBAAqB;cAC/BuB,SAAS;cACTC,OAAO,EAAE,CAAE;cACXjM,KAAK,EAAE;gBAAE8C,KAAK,EAAE;cAAO,CAAE;cACzBxB,KAAK,EAAEd,6BAA6B,CAACU,gBAAiB;cACtDyK,QAAQ,EAAGvK,CAAC,IAAK;gBAChB,IAAIE,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;gBAC1B,IAAIA,KAAK,CAAC0B,MAAM,GAAG,GAAG,EAAE;kBACvB1B,KAAK,GAAGA,KAAK,CAAC8L,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC5B;gBACA9J,gBAAgB,CAAC,kBAAkB,EAAEhC,KAAK,CAAC;cAC5C,CAAE;cACF+L,UAAU,EAAE,GAAG,EAAA9P,sBAAA,GAAAiD,6BAA6B,CAACU,gBAAgB,cAAA3D,sBAAA,uBAA9CA,sBAAA,CAAgDyF,MAAM,KAAI,CAAC,MAAO;cACjF4I,UAAU,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEW,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBAChC,SAAS,EAAE;oBAAEM,SAAS,EAAE,iBAAiB;oBAAEC,WAAW,EAAE;kBAAkB,CAAC;kBAC3E,qBAAqB,EAAE;oBAAEhJ,MAAM,EAAE;kBAAkB;gBACpD;cACD;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENvD,OAAA;QAAK0N,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC3N,OAAA,CAACvB,MAAM;UACNuP,OAAO,EAAC,WAAW;UACnBJ,OAAO,EAAEjH,kBAAmB;UAC5B+G,SAAS,EAAC,WAAW;UAAAC,QAAA,EAEpBjN,SAAS,CAAC,OAAO;QAAC;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET,CAAC;AAACnD,EAAA,CAh+BIH,mBAAmB;EAAA,QACCH,cAAc,EAwBlCf,cAAc;AAAA;AAAAwR,EAAA,GAzBdtQ,mBAAmB;AAk+BzB,eAAeA,mBAAmB;AAAC,IAAAsQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}