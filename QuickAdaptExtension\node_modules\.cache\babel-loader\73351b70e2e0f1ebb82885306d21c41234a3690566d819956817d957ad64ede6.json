{"ast": null, "code": "import { adminApiService } from \"./APIService\";\nexport const getLoginUserRoles = async userId => {\n  try {\n    const response = await adminApiService.get(`/User/GetLoginUserRoles`); // LoginUserRoles \n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching user roles\", error);\n    throw error;\n  }\n};\nexport const getRolesByUser = async () => {\n  try {\n    const response = await adminApiService.get(`/User/GetUserRoles`);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching user roles\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["adminApiService", "getLoginUserRoles", "userId", "response", "get", "data", "error", "console", "getRolesByUser"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/services/UserRoleService.ts"], "sourcesContent": ["import { adminApiService, userApiService } from \"./APIService\";\r\n\r\nexport const getLoginUserRoles = async (userId:string) => {\r\n    try {\r\n        const response = await adminApiService.get(`/User/GetLoginUserRoles`); // LoginUserRoles \r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching user roles\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const getRolesByUser = async () => {\r\n    try {\r\n        const response = await adminApiService.get(`/User/GetUserRoles`);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching user roles\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAwB,cAAc;AAE9D,OAAO,MAAMC,iBAAiB,GAAG,MAAOC,MAAa,IAAK;EACtD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMH,eAAe,CAACI,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC;IACvE,OAAOD,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK;EACf;AACJ,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACA,MAAML,QAAQ,GAAG,MAAMH,eAAe,CAACI,GAAG,CAAC,oBAAoB,CAAC;IAChE,OAAOD,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK;EACf;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}