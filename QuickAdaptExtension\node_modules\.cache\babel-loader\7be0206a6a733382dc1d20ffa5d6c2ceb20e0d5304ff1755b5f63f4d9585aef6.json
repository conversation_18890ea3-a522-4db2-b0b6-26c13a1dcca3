{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\ExtensionPopupLoader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\nimport LottieSpinner from './LottieSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExtensionPopupLoader = ({\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + 100 / (duration / 50); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popup-loader-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"popup-close-btn-minimal\",\n        onClick: () => setIsVisible(false),\n        \"aria-label\": \"Close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup-lottie-content\",\n        children: /*#__PURE__*/_jsxDEV(LottieSpinner, {\n          size: window.innerWidth <= 480 ? 45 : 55,\n          backgroundColor: \"#ff8c00\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(ExtensionPopupLoader, \"B3Heh1vmVMOVYZF5HYm7udhWEsk=\");\n_c = ExtensionPopupLoader;\nexport default ExtensionPopupLoader;\nvar _c;\n$RefreshReg$(_c, \"ExtensionPopupLoader\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ExtensionPopup<PERSON><PERSON>der", "duration", "onComplete", "position", "_s", "isVisible", "setIsVisible", "progress", "setProgress", "progressInterval", "setInterval", "prev", "clearInterval", "hide<PERSON><PERSON>r", "setTimeout", "clearTimeout", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "window", "innerWidth", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionPopupLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport './ExtensionPopupLoader.css';\r\nimport LottieSpinner from './LottieSpinner';\r\n\r\ninterface ExtensionPopupLoaderProps {\r\n  duration?: number;\r\n  onComplete?: () => void;\r\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\r\n}\r\n\r\nconst ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({\r\n  duration = 3000,\r\n  onComplete,\r\n  position = 'top-right'\r\n}) => {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [progress, setProgress] = useState(0);\r\n\r\n  useEffect(() => {\r\n    // Animate progress bar\r\n    const progressInterval = setInterval(() => {\r\n      setProgress(prev => {\r\n        if (prev >= 100) {\r\n          clearInterval(progressInterval);\r\n          return 100;\r\n        }\r\n        return prev + (100 / (duration / 50)); // Update every 50ms\r\n      });\r\n    }, 50);\r\n\r\n    // Auto hide after duration\r\n    const hideTimer = setTimeout(() => {\r\n      setIsVisible(false);\r\n      if (onComplete) {\r\n        setTimeout(onComplete, 300); // Wait for fade out animation\r\n      }\r\n    }, duration);\r\n\r\n    return () => {\r\n      clearInterval(progressInterval);\r\n      clearTimeout(hideTimer);\r\n    };\r\n  }, [duration, onComplete]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>\r\n      <div className=\"popup-loader-container\">\r\n        {/* Close button */}\r\n        <button\r\n          className=\"popup-close-btn-minimal\"\r\n          onClick={() => setIsVisible(false)}\r\n          aria-label=\"Close\"\r\n        >\r\n          ×\r\n        </button>\r\n\r\n        {/* Lottie Spinner Content */}\r\n        <div className=\"popup-lottie-content\">\r\n          <LottieSpinner\r\n            size={window.innerWidth <= 480 ? 45 : 55}\r\n            backgroundColor=\"#ff8c00\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExtensionPopupLoader;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,4BAA4B;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5C,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,QAAQ,GAAG,IAAI;EACfC,UAAU;EACVC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAE3CD,SAAS,CAAC,MAAM;IACd;IACA,MAAMc,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACzCF,WAAW,CAACG,IAAI,IAAI;QAClB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfC,aAAa,CAACH,gBAAgB,CAAC;UAC/B,OAAO,GAAG;QACZ;QACA,OAAOE,IAAI,GAAI,GAAG,IAAIV,QAAQ,GAAG,EAAE,CAAE,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,MAAMY,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCR,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIJ,UAAU,EAAE;QACdY,UAAU,CAACZ,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,EAAED,QAAQ,CAAC;IAEZ,OAAO,MAAM;MACXW,aAAa,CAACH,gBAAgB,CAAC;MAC/BM,YAAY,CAACF,SAAS,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACZ,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAE1B,IAAI,CAACG,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEN,OAAA;IAAKiB,SAAS,EAAE,0BAA0Bb,QAAQ,IAAIE,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAG;IAAAY,QAAA,eACvFlB,OAAA;MAAKiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErClB,OAAA;QACEiB,SAAS,EAAC,yBAAyB;QACnCE,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAC,KAAK,CAAE;QACnC,cAAW,OAAO;QAAAW,QAAA,EACnB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTvB,OAAA;QAAKiB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnClB,OAAA,CAACF,aAAa;UACZ0B,IAAI,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,EAAE,GAAG,EAAG;UACzCC,eAAe,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA1DIJ,oBAAyD;AAAA2B,EAAA,GAAzD3B,oBAAyD;AA4D/D,eAAeA,oBAAoB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}