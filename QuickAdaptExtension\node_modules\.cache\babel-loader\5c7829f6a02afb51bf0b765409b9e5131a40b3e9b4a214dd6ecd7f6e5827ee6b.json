{"ast": null, "code": "import React,{useEffect,useState}from'react';import ChecklistCircle from\"./ChecklistCheckIcon\";import useDrawerStore from'../../store/drawerStore';import LauncherSettings from'./LauncherSettings';import ImageCarousel from\"./ImageCarousel\";import VideoPlayer from\"./VideoPlayer\";import{editInteractionName}from'./Chekpoints';import{closepluginicon,maximize,chkicn1,chkdefault}from'../../assets/icons/icons';import AlertPopup from'../drawer/AlertPopup';import'../../styles/rtl_styles.scss';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Function to modify the color of an SVG icon\nconst modifySVGColor=(base64SVG,color)=>{if(!base64SVG){return\"\";}try{// Check if the string is a valid base64 SVG\nif(!base64SVG.includes(\"data:image/svg+xml;base64,\")){return base64SVG;// Return the original if it's not an SVG\n}const decodedSVG=atob(base64SVG.split(\",\")[1]);// Check if this is primarily a stroke-based or fill-based icon\nconst hasStroke=decodedSVG.includes('stroke=\"');const hasColoredFill=/fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);let modifiedSVG=decodedSVG;if(hasStroke&&!hasColoredFill){// This is a stroke-based icon (like chkicn2-6) - only change stroke color\nmodifiedSVG=modifiedSVG.replace(/stroke=\"[^\"]+\"/g,`stroke=\"${color}\"`);}else if(hasColoredFill){// This is a fill-based icon (like chkicn1) - only change fill color\nmodifiedSVG=modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g,`fill=\"${color}\"`);}else{// No existing fill or stroke, add fill to make it visible\nmodifiedSVG=modifiedSVG.replace(/<path(?![^>]*fill=)/g,`<path fill=\"${color}\"`);modifiedSVG=modifiedSVG.replace(/<svg(?![^>]*fill=)/g,`<svg fill=\"${color}\"`);}const modifiedBase64=`data:image/svg+xml;base64,${btoa(modifiedSVG)}`;return modifiedBase64;}catch(error){console.error(\"Error modifying SVG color:\",error);return base64SVG;// Return the original if there's an error\n}};const ChecklistPopup=_ref=>{var _checklistGuideMetaDa,_checklistGuideMetaDa2,_checklistGuideMetaDa3,_checkpointslistData$,_checklistGuideMetaDa4,_checklistGuideMetaDa5,_checklistGuideMetaDa6,_checklistGuideMetaDa7,_checklistGuideMetaDa8,_checklistGuideMetaDa9,_checklistGuideMetaDa10,_checklistGuideMetaDa11,_checklistGuideMetaDa12,_checklistGuideMetaDa13,_checklistGuideMetaDa14,_checklistGuideMetaDa15,_checklistGuideMetaDa16,_checklistGuideMetaDa17,_checklistGuideMetaDa18,_checklistGuideMetaDa19,_checklistGuideMetaDa20,_checklistGuideMetaDa21,_checklistGuideMetaDa22,_checklistGuideMetaDa23,_checklistGuideMetaDa24,_checklistGuideMetaDa25,_checklistGuideMetaDa26,_checklistGuideMetaDa27,_checklistGuideMetaDa28,_checklistGuideMetaDa29,_checklistGuideMetaDa30,_checklistGuideMetaDa31,_checklistGuideMetaDa32,_checklistGuideMetaDa33,_checklistGuideMetaDa34,_checklistGuideMetaDa35,_checklistGuideMetaDa36,_checklistGuideMetaDa37,_checklistGuideMetaDa38,_checklistGuideMetaDa39,_checklistGuideMetaDa40,_checklistGuideMetaDa41,_checklistGuideMetaDa42,_selectedItem$support,_selectedItem$support2,_checklistGuideMetaDa49,_checklistGuideMetaDa50,_selectedItem$support3,_checklistGuideMetaDa51,_checklistGuideMetaDa52;let{isOpen,onClose,onRemainingCountUpdate,data,guideDetails,setopenWarning,handleLeave}=_ref;const{t:translate}=useTranslation();const{checklistGuideMetaData,checkpointsEditPopup,isUnSavedChanges,openWarning}=useDrawerStore(state=>state);const initialCompletedStatus={};// State to track which steps are completed\nconst[completedStatus,setCompletedStatus]=useState({});// Map checkpoints and set the first one as completed by default\nconst checkpointslistData=((_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:(_checklistGuideMetaDa2=_checklistGuideMetaDa.checkpoints)===null||_checklistGuideMetaDa2===void 0?void 0:(_checklistGuideMetaDa3=_checklistGuideMetaDa2.checkpointsList)===null||_checklistGuideMetaDa3===void 0?void 0:_checklistGuideMetaDa3.map((checkpoint,index)=>({...checkpoint,completed:index===0?true:false})))||[];const[checklistItems,setChecklistItems]=useState(checkpointslistData);const[activeItem,setActiveItem]=useState(checkpointslistData.length>0&&!checkpointsEditPopup?(_checkpointslistData$=checkpointslistData[0])===null||_checkpointslistData$===void 0?void 0:_checkpointslistData$.id:\"default-placeholder\");const checklistColor=(_checklistGuideMetaDa4=checklistGuideMetaData[0])===null||_checklistGuideMetaDa4===void 0?void 0:(_checklistGuideMetaDa5=_checklistGuideMetaDa4.canvas)===null||_checklistGuideMetaDa5===void 0?void 0:_checklistGuideMetaDa5.primaryColor;function getLocalizedTitleSubTitle(ts,t){const defaultTitle=\"Checklist Title\";const defaultSubTitle=\"Context about the tasks in the checklist below users should prioritize completing.\";return{title:!(ts!==null&&ts!==void 0&&ts.title)||ts.title===defaultTitle?t(defaultTitle,{defaultValue:defaultTitle}):ts.title,subTitle:!(ts!==null&&ts!==void 0&&ts.subTitle)||ts.subTitle===defaultSubTitle?t(defaultSubTitle,{defaultValue:defaultSubTitle}):ts.subTitle};}const localizedTitleSubTitle=getLocalizedTitleSubTitle((_checklistGuideMetaDa6=checklistGuideMetaData[0])===null||_checklistGuideMetaDa6===void 0?void 0:_checklistGuideMetaDa6.TitleSubTitle,translate);{/*this is here what i did is i am getting the title and subtitle from the checklistGuideMetaData and then i am using it for modifying inital translation of the title and subtitle so if you want to modify data such as color and all go to store and look for checklistGuideMetaData */}// Initialize completedStatus when checkpointslistData changes\nuseEffect(()=>{if(checkpointslistData.length>0){const initialCompletedStatus={};checkpointslistData.forEach((item,index)=>{// Set the first item as completed by default, others as not completed\ninitialCompletedStatus[item.id]=index===0;});// console.log(\"Initializing completedStatus:\", initialCompletedStatus);\nsetCompletedStatus(initialCompletedStatus);// Calculate and update the initial remaining count\nconst remainingItems=checkpointslistData.length-1;// All items except the first one\nconst formattedCount=remainingItems.toString().padStart(2,\"0\");// console.log(\"Initial remaining count set to:\", formattedCount);\n// Update localStorage directly\nif(window.localStorage){window.localStorage.setItem(\"remainingCount\",formattedCount);}// Call the callback\nif(onRemainingCountUpdate){onRemainingCountUpdate(formattedCount);}}},[checkpointslistData,onRemainingCountUpdate,completedStatus]);useEffect(()=>{document.documentElement.style.setProperty(\"--chkcolor\",checklistColor);},[checklistColor]);useEffect(()=>{if(checkpointslistData.length===0){setActiveItem(\"default-placeholder\");}else{var _checkpointslistData$2;setActiveItem((_checkpointslistData$2=checkpointslistData[0])===null||_checkpointslistData$2===void 0?void 0:_checkpointslistData$2.id);}},[checkpointslistData.length==1]);const[icons,setIcons]=useState([{id:1,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn1},style:{zoom:1,display:\"flex\"}}),selected:true}]);const encodeToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};let base64Icon;const initialSelectedIcon=icons.find(icon=>icon.selected);if(initialSelectedIcon&&checkpointslistData.length==0){var _initialSelectedIcon$;const svgElement=(_initialSelectedIcon$=initialSelectedIcon.component.props.dangerouslySetInnerHTML)===null||_initialSelectedIcon$===void 0?void 0:_initialSelectedIcon$.__html;if(svgElement){base64Icon=encodeToBase64(svgElement);}}const iconColor=((_checklistGuideMetaDa7=checklistGuideMetaData[0])===null||_checklistGuideMetaDa7===void 0?void 0:(_checklistGuideMetaDa8=_checklistGuideMetaDa7.launcher)===null||_checklistGuideMetaDa8===void 0?void 0:_checklistGuideMetaDa8.iconColor)||\"#fff\";// Default to black if no color\nconst base64IconFinal=((_checklistGuideMetaDa9=checklistGuideMetaData[0])===null||_checklistGuideMetaDa9===void 0?void 0:(_checklistGuideMetaDa10=_checklistGuideMetaDa9.launcher)===null||_checklistGuideMetaDa10===void 0?void 0:_checklistGuideMetaDa10.icon)||base64Icon;const totalItems=checkpointslistData.length||1;const progress=1;// Update the remaining count whenever completedStatus or checkpointslistData changes\nuseEffect(()=>{if(checkpointslistData.length>0&&Object.keys(completedStatus).length>0){// Count the number of incomplete steps\nconst remainingItems=checkpointslistData.length-Object.values(completedStatus).filter(status=>status).length;// Format with leading zero (01, 02, 03, etc.)\nconst formattedCount=remainingItems.toString().padStart(2,\"0\");// console.log(\"ChecklistPopup updating count to:\", formattedCount, \"from completedStatus:\", completedStatus);\n// Make sure the callback is called with the updated count\nif(onRemainingCountUpdate){onRemainingCountUpdate(formattedCount);}// Also update the count in the drawerStore to ensure it's available to all components\nif(window.localStorage){window.localStorage.setItem(\"remainingCount\",formattedCount);}}},[completedStatus,checkpointslistData,onRemainingCountUpdate]);const toggleItemCompletion=id=>{setCompletedStatus(prevStatus=>{const newStatus={...prevStatus,[id]:!prevStatus[id]};// Update the remaining count immediately\nif(checkpointslistData.length>0){const remainingItems=checkpointslistData.length-Object.values(newStatus).filter(status=>status).length;const formattedCount=remainingItems.toString().padStart(2,\"0\");console.log(\"toggleItemCompletion updating count to:\",formattedCount);// Update localStorage directly\nif(window.localStorage){window.localStorage.setItem(\"remainingCount\",formattedCount);}// Call the callback\nif(onRemainingCountUpdate){onRemainingCountUpdate(formattedCount);}}return newStatus;});};// Mark the active item as completed\nconst handleMarkAsCompleted=()=>{if(activeItem&&activeItem!==\"default-placeholder\"){setCompletedStatus(prevStatus=>{const newStatus={...prevStatus,[activeItem]:true};// Update the remaining count immediately\nif(checkpointslistData.length>0){const remainingItems=checkpointslistData.length-Object.values(newStatus).filter(status=>status).length;const formattedCount=remainingItems.toString().padStart(2,\"0\");console.log(\"handleMarkAsCompleted updating count to:\",formattedCount);// Update localStorage directly\nif(window.localStorage){window.localStorage.setItem(\"remainingCount\",formattedCount);}// Call the callback\nif(onRemainingCountUpdate){onRemainingCountUpdate(formattedCount);}}console.log(\"Marked item as completed:\",activeItem);return newStatus;});}};const handleSelect=id=>{setActiveItem(id);};const handleClose=()=>{var _checkpointslistData$3;onClose();setActiveItem((_checkpointslistData$3=checkpointslistData[0])===null||_checkpointslistData$3===void 0?void 0:_checkpointslistData$3.id);};if(!isOpen)return null;if(!isOpen)return null;const selectedItem=checkpointslistData.length>0?checkpointslistData.find(item=>checkpointsEditPopup?item.id===editInteractionName:item.id===activeItem)||{id:\"default-placeholder\",title:translate(\"Step Title\",{defaultValue:\"Step Title\"}),description:translate(\"Step Description\",{defaultValue:\"Step Description\"}),icon:base64Icon,mediaTitle:translate(\"Media Title\",{defaultValue:\"Media Title\"}),mediaDescription:translate(\"Media Description\",{defaultValue:\"Media Description\"}),supportingMedia:[]}:{id:\"default-placeholder\",title:translate(\"Step Title\",{defaultValue:\"Step Title\"}),description:translate(\"Step Description\",{defaultValue:\"Step Description\"}),icon:base64Icon,mediaTitle:translate(\"Media Title\",{defaultValue:\"Media Title\"}),mediaDescription:translate(\"Media Description\",{defaultValue:\"Media Description\"}),supportingMedia:[]};const handleNavigate=()=>{// window.open(\"http://localhost:3000/\", '_blank');\n};const isRTL=document.documentElement.getAttribute('dir')==='rtl'||document.body.getAttribute('dir')==='rtl';return/*#__PURE__*/_jsxs(_Fragment,{children:[isUnSavedChanges&&openWarning&&/*#__PURE__*/_jsx(AlertPopup,{openWarning:openWarning,setopenWarning:setopenWarning,handleLeave:handleLeave}),/*#__PURE__*/_jsxs(\"div\",{style:{position:\"fixed\",inset:0,display:\"flex\",alignItems:\"center\",// justifyContent: 'center',\nzIndex:99999},children:[/*#__PURE__*/_jsx(\"div\",{style:{position:\"absolute\",inset:0,backgroundColor:\"rgba(0, 0, 0, 0.3)\"},onClick:handleClose}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-chkpopup\",children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:(_checklistGuideMetaDa11=checklistGuideMetaData[0])===null||_checklistGuideMetaDa11===void 0?void 0:(_checklistGuideMetaDa12=_checklistGuideMetaDa11.canvas)===null||_checklistGuideMetaDa12===void 0?void 0:_checklistGuideMetaDa12.backgroundColor,border:`${(_checklistGuideMetaDa13=checklistGuideMetaData[0])===null||_checklistGuideMetaDa13===void 0?void 0:(_checklistGuideMetaDa14=_checklistGuideMetaDa13.canvas)===null||_checklistGuideMetaDa14===void 0?void 0:_checklistGuideMetaDa14.borderWidth}px solid ${(_checklistGuideMetaDa15=checklistGuideMetaData[0])===null||_checklistGuideMetaDa15===void 0?void 0:(_checklistGuideMetaDa16=_checklistGuideMetaDa15.canvas)===null||_checklistGuideMetaDa16===void 0?void 0:_checklistGuideMetaDa16.borderColor}`,borderRadius:`${(_checklistGuideMetaDa17=checklistGuideMetaData[0])===null||_checklistGuideMetaDa17===void 0?void 0:(_checklistGuideMetaDa18=_checklistGuideMetaDa17.canvas)===null||_checklistGuideMetaDa18===void 0?void 0:_checklistGuideMetaDa18.cornerRadius}px`,width:`${((_checklistGuideMetaDa19=checklistGuideMetaData[0])===null||_checklistGuideMetaDa19===void 0?void 0:(_checklistGuideMetaDa20=_checklistGuideMetaDa19.canvas)===null||_checklistGuideMetaDa20===void 0?void 0:_checklistGuideMetaDa20.width)||930}px`,height:`${((_checklistGuideMetaDa21=checklistGuideMetaData[0])===null||_checklistGuideMetaDa21===void 0?void 0:(_checklistGuideMetaDa22=_checklistGuideMetaDa21.canvas)===null||_checklistGuideMetaDa22===void 0?void 0:_checklistGuideMetaDa22.height)||500}px`},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",height:\"100%\",width:\"100%\",overflow:\"auto hidden\"},className:\"qadpt-chkcontent\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chkrgt\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",flexDirection:\"column\",gap:\"16px\",borderBottom:\"1px solid #E8E8E8\",padding:\"24px 24px 16px 24px\",textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",flexDirection:\"column\",gap:\"6px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"20px\",fontWeight:(_checklistGuideMetaDa23=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa23!==void 0&&(_checklistGuideMetaDa24=_checklistGuideMetaDa23.TitleSubTitle)!==null&&_checklistGuideMetaDa24!==void 0&&_checklistGuideMetaDa24.titleBold?\"bold\":\"normal\",fontStyle:(_checklistGuideMetaDa25=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa25!==void 0&&(_checklistGuideMetaDa26=_checklistGuideMetaDa25.TitleSubTitle)!==null&&_checklistGuideMetaDa26!==void 0&&_checklistGuideMetaDa26.titleItalic?\"italic\":\"normal\",color:((_checklistGuideMetaDa27=checklistGuideMetaData[0])===null||_checklistGuideMetaDa27===void 0?void 0:(_checklistGuideMetaDa28=_checklistGuideMetaDa27.TitleSubTitle)===null||_checklistGuideMetaDa28===void 0?void 0:_checklistGuideMetaDa28.titleColor)||\"#333\",display:\"block\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\",overflow:\"hidden\"},children:localizedTitleSubTitle.title}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",fontWeight:(_checklistGuideMetaDa29=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa29!==void 0&&(_checklistGuideMetaDa30=_checklistGuideMetaDa29.TitleSubTitle)!==null&&_checklistGuideMetaDa30!==void 0&&_checklistGuideMetaDa30.subTitleBold?\"bold\":\"normal\",fontStyle:(_checklistGuideMetaDa31=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa31!==void 0&&(_checklistGuideMetaDa32=_checklistGuideMetaDa31.TitleSubTitle)!==null&&_checklistGuideMetaDa32!==void 0&&_checklistGuideMetaDa32.subTitleItalic?\"italic\":\"normal\",color:((_checklistGuideMetaDa33=checklistGuideMetaData[0])===null||_checklistGuideMetaDa33===void 0?void 0:(_checklistGuideMetaDa34=_checklistGuideMetaDa33.TitleSubTitle)===null||_checklistGuideMetaDa34===void 0?void 0:_checklistGuideMetaDa34.subTitleColor)||\"#8D8D8D\"},className:\"qadpt-subtl\",children:localizedTitleSubTitle.subTitle})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",marginBottom:\"8px\"},children:/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:\"14px\",color:\"#6b7280\"},children:[progress,\"/\",totalItems]})}),/*#__PURE__*/_jsx(\"div\",{style:{height:\"8px\",backgroundColor:\"#e5e7eb\",borderRadius:\"9999px\",overflow:\"hidden\"},children:/*#__PURE__*/_jsx(\"div\",{style:{height:\"100%\",backgroundColor:(_checklistGuideMetaDa35=checklistGuideMetaData[0])===null||_checklistGuideMetaDa35===void 0?void 0:(_checklistGuideMetaDa36=_checklistGuideMetaDa35.canvas)===null||_checklistGuideMetaDa36===void 0?void 0:_checklistGuideMetaDa36.primaryColor,borderRadius:\"9999px\",width:`${progress/totalItems*100}%`}})})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{overflow:\"auto\",maxHeight:`calc(${((_checklistGuideMetaDa37=checklistGuideMetaData[0])===null||_checklistGuideMetaDa37===void 0?void 0:(_checklistGuideMetaDa38=_checklistGuideMetaDa37.canvas)===null||_checklistGuideMetaDa38===void 0?void 0:_checklistGuideMetaDa38.height)||500}px - 190px)`},children:checkpointslistData.length===0?/*#__PURE__*/_jsx(\"div\",{className:`${activeItem===\"default-placeholder\"?\"qadpt-chkstp\":\"\"}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chkstpctn\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",gap:\"8px\"},children:[/*#__PURE__*/_jsx(\"img\",{src:base64Icon,alt:\"icon\",style:{width:\"20px\",height:\"20px\"}}),/*#__PURE__*/_jsx(\"span\",{style:{color:(_checklistGuideMetaDa39=checklistGuideMetaData[0])===null||_checklistGuideMetaDa39===void 0?void 0:(_checklistGuideMetaDa40=_checklistGuideMetaDa39.checkpoints)===null||_checklistGuideMetaDa40===void 0?void 0:_checklistGuideMetaDa40.checkpointTitles},children:translate(\"Step Title\",{defaultValue:\"Step Title\"})})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(ChecklistCircle,{completed:true,onClick:()=>{},size:\"sm\"},\"default\")})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",marginTop:\"8px\",gap:\"8px\"},children:/*#__PURE__*/_jsx(\"p\",{style:{fontSize:\"14px\",color:(_checklistGuideMetaDa41=checklistGuideMetaData[0])===null||_checklistGuideMetaDa41===void 0?void 0:(_checklistGuideMetaDa42=_checklistGuideMetaDa41.checkpoints)===null||_checklistGuideMetaDa42===void 0?void 0:_checklistGuideMetaDa42.checkpointsDescription,lineHeight:\"1.5\",margin:0,whiteSpace:\"normal\",wordBreak:\"break-word\"},children:translate(\"Step Description\",{defaultValue:\"Step Description\"})})})]})}):checkpointslistData===null||checkpointslistData===void 0?void 0:checkpointslistData.map(item=>{var _checklistGuideMetaDa43,_checklistGuideMetaDa44,_checklistGuideMetaDa45,_checklistGuideMetaDa46,_checklistGuideMetaDa47,_checklistGuideMetaDa48;return/*#__PURE__*/_jsx(\"div\",{className:`${(checkpointsEditPopup?editInteractionName===item.id:activeItem===item.id)?\"qadpt-chkstp\":\"\"}`,children:/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-chkstpctn\",onClick:()=>handleSelect(item.id),children:/*#__PURE__*/_jsxs(\"div\",{style:{paddingLeft:\"10px\",display:\"flex\",gap:\"6px\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",gap:\"10px\",flexDirection:\"row\",width:\"calc(100% - 60px)\"},children:[item.icon&&typeof item.icon===\"string\"?/*#__PURE__*/_jsx(\"img\",{src:modifySVGColor(item.icon,((_checklistGuideMetaDa43=checklistGuideMetaData[0])===null||_checklistGuideMetaDa43===void 0?void 0:(_checklistGuideMetaDa44=_checklistGuideMetaDa43.checkpoints)===null||_checklistGuideMetaDa44===void 0?void 0:_checklistGuideMetaDa44.checkpointsIcons)||\"#333\"),alt:\"icon\",style:{width:\"20px\",height:\"20px\"}}):/*#__PURE__*/_jsx(\"div\",{style:{width:\"20px\",height:\"20px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(\"span\",{style:{width:\"16px\",height:\"16px\"}})}),/*#__PURE__*/_jsx(\"span\",{style:{color:((_checklistGuideMetaDa45=checklistGuideMetaData[0])===null||_checklistGuideMetaDa45===void 0?void 0:(_checklistGuideMetaDa46=_checklistGuideMetaDa45.checkpoints)===null||_checklistGuideMetaDa46===void 0?void 0:_checklistGuideMetaDa46.checkpointTitles)||\"#333\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\"},children:item.title})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(ChecklistCircle,{completed:completedStatus[item.id]||false,onClick:()=>toggleItemCompletion(item.id),size:\"sm\"},item.id)})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{style:{fontSize:\"14px\",color:(_checklistGuideMetaDa47=checklistGuideMetaData[0])===null||_checklistGuideMetaDa47===void 0?void 0:(_checklistGuideMetaDa48=_checklistGuideMetaDa47.checkpoints)===null||_checklistGuideMetaDa48===void 0?void 0:_checklistGuideMetaDa48.checkpointsDescription},className:\"qadpt-chkpopdesc\",children:item.description})})]})},item.id)});})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{width:\"60%\",padding:\"20px 20px 0 20px\"},className:\"qadpt-chklft\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",placeContent:\"end\",width:\"100%\",gap:\"6px\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:maximize},style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"6px\",display:\"flex\",cursor:\"pointer\"}}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:closepluginicon},style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"8px\",display:\"flex\",cursor:\"pointer\"}})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",flexDirection:\"column\",gap:\"10px\",height:\"calc(100% - 90px)\"},children:/*#__PURE__*/_jsxs(\"div\",{style:{overflow:\"hidden auto\",display:\"flex\",alignItems:\"center\",flexDirection:\"column\",width:\"-webkit-fill-available\"},children:[(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support=selectedItem.supportingMedia)===null||_selectedItem$support===void 0?void 0:_selectedItem$support.length)>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[selectedItem.supportingMedia.some(file=>{var _file$Base;return file===null||file===void 0?void 0:(_file$Base=file.Base64)===null||_file$Base===void 0?void 0:_file$Base.startsWith(\"data:image\");})&&/*#__PURE__*/_jsx(ImageCarousel,{selectedItem:selectedItem,activeItem:activeItem,images:selectedItem.supportingMedia.filter(file=>{var _file$Base2;return file===null||file===void 0?void 0:(_file$Base2=file.Base64)===null||_file$Base2===void 0?void 0:_file$Base2.startsWith(\"data:image\");}).map(file=>file.Base64),isMaximized:\"\"}),selectedItem.supportingMedia.some(file=>{var _file$Base3;return file===null||file===void 0?void 0:(_file$Base3=file.Base64)===null||_file$Base3===void 0?void 0:_file$Base3.startsWith(\"data:video\");})&&selectedItem.supportingMedia.filter(file=>{var _file$Base4;return file===null||file===void 0?void 0:(_file$Base4=file.Base64)===null||_file$Base4===void 0?void 0:_file$Base4.startsWith(\"data:video\");}).map((file,index)=>/*#__PURE__*/_jsx(VideoPlayer,{videoFile:file.Base64,isMaximized:\"\"},index))]}),((selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support2=selectedItem.supportingMedia)===null||_selectedItem$support2===void 0?void 0:_selectedItem$support2.length)===0||!(selectedItem!==null&&selectedItem!==void 0&&selectedItem.supportingMedia))&&/*#__PURE__*/_jsxs(\"div\",{style:{width:\"auto\",height:\"244px\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkdefault}}),/*#__PURE__*/_jsx(\"div\",{style:{color:\"#8D8D8D\"},children:translate('Check tasks, stay organized, and finish strong!',{defaultValue:'Check tasks, stay organized, and finish strong!'})})]}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",marginTop:\"10px\"},className:\"qadpt-chkdesc\",children:selectedItem&&/*#__PURE__*/_jsx(\"div\",{style:{height:\"100%\",display:\"flex\",flexDirection:\"column\"},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:isRTL?\"right\":\"left\",display:\"flex\",flexDirection:\"column\",gap:\"12px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"16px\",fontWeight:600,color:\"#333\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\"},children:selectedItem.mediaTitle}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-desc\",style:{color:\"#8D8D8D\"},children:selectedItem.mediaDescription})]})})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"12px\",alignItems:\"center\",placeContent:\"end\",paddingBottom:\"20px\"},className:\"qadpt-btnsec\",children:[/*#__PURE__*/_jsx(\"button\",{style:{backgroundColor:(_checklistGuideMetaDa49=checklistGuideMetaData[0])===null||_checklistGuideMetaDa49===void 0?void 0:(_checklistGuideMetaDa50=_checklistGuideMetaDa49.canvas)===null||_checklistGuideMetaDa50===void 0?void 0:_checklistGuideMetaDa50.primaryColor,borderRadius:\"10px\",padding:\"9px 16px\",color:\"#fff\",border:\"none\",cursor:\"pointer\"},children:translate('Take Tour',{defaultValue:'Take Tour'})}),(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support3=selectedItem.supportingMedia)===null||_selectedItem$support3===void 0?void 0:_selectedItem$support3.length)>0&&/*#__PURE__*/_jsx(\"button\",{style:{borderRadius:\"10px\",padding:\"9px 16px\",color:(_checklistGuideMetaDa51=checklistGuideMetaData[0])===null||_checklistGuideMetaDa51===void 0?void 0:(_checklistGuideMetaDa52=_checklistGuideMetaDa51.canvas)===null||_checklistGuideMetaDa52===void 0?void 0:_checklistGuideMetaDa52.primaryColor,border:\"none\",background:\"#D3D9DA\",cursor:\"pointer\"},onClick:handleMarkAsCompleted,children:translate('Mark as Completed',{defaultValue:'Mark as Completed'})})]})]})]})})})]})]});};const ChecklistApp=_ref2=>{var _checklistGuideMetaDa53,_checklistGuideMetaDa54,_checklistGuideMetaDa55,_checklistGuideMetaDa56,_checklistGuideMetaDa57,_checklistGuideMetaDa58,_checklistGuideMetaDa59,_checklistGuideMetaDa60,_checklistGuideMetaDa61,_checklistGuideMetaDa62,_checklistGuideMetaDa63,_checklistGuideMetaDa64,_checklistGuideMetaDa65,_checklistGuideMetaDa66,_checklistGuideMetaDa67,_checklistGuideMetaDa68,_checklistGuideMetaDa69,_checklistGuideMetaDa70,_checklistGuideMetaDa71,_checklistGuideMetaDa72,_checklistGuideMetaDa73,_checklistGuideMetaDa74,_checklistGuideMetaDa75,_checklistGuideMetaDa76,_checklistGuideMetaDa77,_checklistGuideMetaDa78,_checklistGuideMetaDa79,_checklistGuideMetaDa80,_checklistGuideMetaDa81,_checklistGuideMetaDa82,_checklistGuideMetaDa83,_checklistGuideMetaDa84,_checklistGuideMetaDa85;let{setopenWarning,handleLeave}=_ref2;const{checklistGuideMetaData,setShowLauncherSettings,showLauncherSettings,setOpenWarning}=useDrawerStore(state=>state);let base64Icon;const[icons,setIcons]=useState([{id:1,component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn1},style:{zoom:1,display:\"flex\"}}),selected:true}]);const encodeToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};const iconColor=((_checklistGuideMetaDa53=checklistGuideMetaData[0])===null||_checklistGuideMetaDa53===void 0?void 0:(_checklistGuideMetaDa54=_checklistGuideMetaDa53.launcher)===null||_checklistGuideMetaDa54===void 0?void 0:_checklistGuideMetaDa54.iconColor)||\"#fff\";// Default to black if no color\nconst base64IconFinal=(_checklistGuideMetaDa55=checklistGuideMetaData[0])===null||_checklistGuideMetaDa55===void 0?void 0:(_checklistGuideMetaDa56=_checklistGuideMetaDa55.launcher)===null||_checklistGuideMetaDa56===void 0?void 0:_checklistGuideMetaDa56.icon;const initialSelectedIcon=icons.find(icon=>icon.selected);if(initialSelectedIcon){var _initialSelectedIcon$2;const svgElement=(_initialSelectedIcon$2=initialSelectedIcon.component.props.dangerouslySetInnerHTML)===null||_initialSelectedIcon$2===void 0?void 0:_initialSelectedIcon$2.__html;if(svgElement){base64Icon=encodeToBase64(svgElement);}}// Using the modifySVGColor function defined at the top of the file\nconst modifiedIcon=modifySVGColor(base64IconFinal||base64Icon,iconColor);const[isOpen,setIsOpen]=useState(true);const[remainingCount,setRemainingCount]=useState(\"00\");const handleRemainingCountUpdate=formattedCount=>{setRemainingCount(formattedCount);};const[anchorEl,setAnchorEl]=useState(document.body);const[currentStep,setCurrentStep]=useState(0);const[isAnnouncementOpen,setAnnouncementOpen]=useState(false);const[scrollPercentage,setScrollPercentage]=useState(0);const currentUrl=window.location.href;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-chklayout\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setIsOpen(true);setShowLauncherSettings(true);},style:{backgroundColor:(_checklistGuideMetaDa57=checklistGuideMetaData[0])===null||_checklistGuideMetaDa57===void 0?void 0:_checklistGuideMetaDa57.launcher.launcherColor,color:\"white\",borderRadius:((_checklistGuideMetaDa58=checklistGuideMetaData[0])===null||_checklistGuideMetaDa58===void 0?void 0:_checklistGuideMetaDa58.launcher.type)===\"Text\"||((_checklistGuideMetaDa59=checklistGuideMetaData[0])===null||_checklistGuideMetaDa59===void 0?void 0:_checklistGuideMetaDa59.launcher.type)===\"Icon+Txt\"?\"16px\":\"50%\",height:\"54px\",width:((_checklistGuideMetaDa60=checklistGuideMetaData[0])===null||_checklistGuideMetaDa60===void 0?void 0:_checklistGuideMetaDa60.launcher.type)===\"Text\"||((_checklistGuideMetaDa61=checklistGuideMetaData[0])===null||_checklistGuideMetaDa61===void 0?void 0:_checklistGuideMetaDa61.launcher.type)===\"Icon+Txt\"?`auto`:\"54px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",padding:\"8px\",boxShadow:\"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",transition:\"all 0.2s ease\",border:\"none\",cursor:\"pointer\",position:\"relative\"},children:[((_checklistGuideMetaDa62=checklistGuideMetaData[0])===null||_checklistGuideMetaDa62===void 0?void 0:(_checklistGuideMetaDa63=_checklistGuideMetaDa62.launcher)===null||_checklistGuideMetaDa63===void 0?void 0:_checklistGuideMetaDa63.type)===\"Icon\"&&/*#__PURE__*/_jsx(\"img\",{src:modifiedIcon,alt:\"icon\",style:{width:\"20px\",height:\"20px\"}}),((_checklistGuideMetaDa64=checklistGuideMetaData[0])===null||_checklistGuideMetaDa64===void 0?void 0:(_checklistGuideMetaDa65=_checklistGuideMetaDa64.launcher)===null||_checklistGuideMetaDa65===void 0?void 0:_checklistGuideMetaDa65.type)===\"Text\"&&((_checklistGuideMetaDa66=checklistGuideMetaData[0])===null||_checklistGuideMetaDa66===void 0?void 0:(_checklistGuideMetaDa67=_checklistGuideMetaDa66.launcher)===null||_checklistGuideMetaDa67===void 0?void 0:_checklistGuideMetaDa67.text)&&/*#__PURE__*/_jsx(\"span\",{style:{fontSize:\"16px\",fontWeight:\"bold\",color:(_checklistGuideMetaDa68=checklistGuideMetaData[0])===null||_checklistGuideMetaDa68===void 0?void 0:(_checklistGuideMetaDa69=_checklistGuideMetaDa68.launcher)===null||_checklistGuideMetaDa69===void 0?void 0:_checklistGuideMetaDa69.textColor,padding:\"8px\",whiteSpace:\"nowrap\"},children:checklistGuideMetaData[0].launcher.text}),((_checklistGuideMetaDa70=checklistGuideMetaData[0])===null||_checklistGuideMetaDa70===void 0?void 0:(_checklistGuideMetaDa71=_checklistGuideMetaDa70.launcher)===null||_checklistGuideMetaDa71===void 0?void 0:_checklistGuideMetaDa71.type)===\"Icon+Txt\"&&((_checklistGuideMetaDa72=checklistGuideMetaData[0])===null||_checklistGuideMetaDa72===void 0?void 0:(_checklistGuideMetaDa73=_checklistGuideMetaDa72.launcher)===null||_checklistGuideMetaDa73===void 0?void 0:_checklistGuideMetaDa73.text)&&((_checklistGuideMetaDa74=checklistGuideMetaData[0])===null||_checklistGuideMetaDa74===void 0?void 0:(_checklistGuideMetaDa75=_checklistGuideMetaDa74.launcher)===null||_checklistGuideMetaDa75===void 0?void 0:_checklistGuideMetaDa75.icon)&&/*#__PURE__*/_jsxs(\"span\",{style:{display:\"flex\",alignItems:\"center\",gap:\"8px\",color:(_checklistGuideMetaDa76=checklistGuideMetaData[0])===null||_checklistGuideMetaDa76===void 0?void 0:(_checklistGuideMetaDa77=_checklistGuideMetaDa76.launcher)===null||_checklistGuideMetaDa77===void 0?void 0:_checklistGuideMetaDa77.textColor,fontSize:\"16px\",fontWeight:\"bold\",padding:\"8px\"},children:[/*#__PURE__*/_jsx(\"img\",{src:modifiedIcon,alt:\"icon\",style:{width:\"20px\",height:\"20px\"}}),(_checklistGuideMetaDa78=checklistGuideMetaData[0])===null||_checklistGuideMetaDa78===void 0?void 0:(_checklistGuideMetaDa79=_checklistGuideMetaDa78.launcher)===null||_checklistGuideMetaDa79===void 0?void 0:_checklistGuideMetaDa79.text]}),((_checklistGuideMetaDa80=checklistGuideMetaData[0])===null||_checklistGuideMetaDa80===void 0?void 0:(_checklistGuideMetaDa81=_checklistGuideMetaDa80.launcher)===null||_checklistGuideMetaDa81===void 0?void 0:_checklistGuideMetaDa81.notificationBadge)&&/*#__PURE__*/_jsx(\"div\",{style:{position:\"absolute\",top:\"-8px\",right:\"-8px\",backgroundColor:(_checklistGuideMetaDa82=checklistGuideMetaData[0])===null||_checklistGuideMetaDa82===void 0?void 0:(_checklistGuideMetaDa83=_checklistGuideMetaDa82.launcher)===null||_checklistGuideMetaDa83===void 0?void 0:_checklistGuideMetaDa83.notificationBadgeColor,color:(_checklistGuideMetaDa84=checklistGuideMetaData[0])===null||_checklistGuideMetaDa84===void 0?void 0:(_checklistGuideMetaDa85=_checklistGuideMetaDa84.launcher)===null||_checklistGuideMetaDa85===void 0?void 0:_checklistGuideMetaDa85.notificationTextColor,fontSize:\"12px\",borderRadius:\"9999px\",height:\"24px\",width:\"24px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:remainingCount})]})}),showLauncherSettings&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(LauncherSettings,{})}),/*#__PURE__*/_jsx(ChecklistPopup,{data:\"\",guideDetails:\"\",isOpen:isOpen,onClose:()=>setIsOpen(true),onRemainingCountUpdate:handleRemainingCountUpdate,setopenWarning:setOpenWarning,handleLeave:handleLeave})]});};export default ChecklistApp;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ChecklistCircle", "useDrawerStore", "LauncherSettings", "ImageCarousel", "VideoPlayer", "editInteractionName", "closepluginicon", "maximize", "chkicn1", "chkdefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "btoa", "error", "console", "ChecklistPopup", "_ref", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checkpointslistData$", "_checklistGuideMetaDa4", "_checklistGuideMetaDa5", "_checklistGuideMetaDa6", "_checklistGuideMetaDa7", "_checklistGuideMetaDa8", "_checklistGuideMetaDa9", "_checklistGuideMetaDa10", "_checklistGuideMetaDa11", "_checklistGuideMetaDa12", "_checklistGuideMetaDa13", "_checklistGuideMetaDa14", "_checklistGuideMetaDa15", "_checklistGuideMetaDa16", "_checklistGuideMetaDa17", "_checklistGuideMetaDa18", "_checklistGuideMetaDa19", "_checklistGuideMetaDa20", "_checklistGuideMetaDa21", "_checklistGuideMetaDa22", "_checklistGuideMetaDa23", "_checklistGuideMetaDa24", "_checklistGuideMetaDa25", "_checklistGuideMetaDa26", "_checklistGuideMetaDa27", "_checklistGuideMetaDa28", "_checklistGuideMetaDa29", "_checklistGuideMetaDa30", "_checklistGuideMetaDa31", "_checklistGuideMetaDa32", "_checklistGuideMetaDa33", "_checklistGuideMetaDa34", "_checklistGuideMetaDa35", "_checklistGuideMetaDa36", "_checklistGuideMetaDa37", "_checklistGuideMetaDa38", "_checklistGuideMetaDa39", "_checklistGuideMetaDa40", "_checklistGuideMetaDa41", "_checklistGuideMetaDa42", "_selectedItem$support", "_selectedItem$support2", "_checklistGuideMetaDa49", "_checklistGuideMetaDa50", "_selectedItem$support3", "_checklistGuideMetaDa51", "_checklistGuideMetaDa52", "isOpen", "onClose", "onRemainingCountUpdate", "data", "guideDetails", "setopenWarning", "handleLeave", "t", "translate", "checklistGuideMetaData", "checkpointsEditPopup", "isUnSavedChanges", "openWarning", "state", "initialCompletedStatus", "completedStatus", "setCompletedStatus", "checkpointslistData", "checkpoints", "checkpointsList", "map", "checkpoint", "index", "completed", "checklistItems", "setChecklistItems", "activeItem", "setActiveItem", "length", "id", "checklistColor", "canvas", "primaryColor", "getLocalizedTitleSubTitle", "ts", "defaultTitle", "defaultSubTitle", "title", "defaultValue", "subTitle", "localizedTitleSubTitle", "TitleSubTitle", "for<PERSON>ach", "item", "remainingItems", "formattedCount", "toString", "padStart", "window", "localStorage", "setItem", "document", "documentElement", "style", "setProperty", "_checkpointslistData$2", "icons", "setIcons", "component", "dangerouslySetInnerHTML", "__html", "zoom", "display", "selected", "encodeToBase64", "svgString", "base64Icon", "initialSelectedIcon", "find", "icon", "_initialSelectedIcon$", "svgElement", "props", "iconColor", "launcher", "base64IconFinal", "totalItems", "progress", "Object", "keys", "values", "filter", "status", "toggleItemCompletion", "prevStatus", "newStatus", "log", "handleMarkAsCompleted", "handleSelect", "handleClose", "_checkpointslistData$3", "selectedItem", "description", "mediaTitle", "mediaDescription", "supportingMedia", "handleNavigate", "isRTL", "getAttribute", "body", "children", "position", "inset", "alignItems", "zIndex", "backgroundColor", "onClick", "className", "border", "borderWidth", "borderColor", "borderRadius", "cornerRadius", "width", "height", "overflow", "flexDirection", "gap", "borderBottom", "padding", "textAlign", "fontSize", "fontWeight", "titleBold", "fontStyle", "titleItalic", "titleColor", "textOverflow", "whiteSpace", "wordBreak", "subTitleBold", "subTitleItalic", "subTitleColor", "justifyContent", "marginBottom", "maxHeight", "src", "alt", "checkpointTitles", "size", "marginTop", "checkpointsDescription", "lineHeight", "margin", "_checklistGuideMetaDa43", "_checklistGuideMetaDa44", "_checklistGuideMetaDa45", "_checklistGuideMetaDa46", "_checklistGuideMetaDa47", "_checklistGuideMetaDa48", "paddingLeft", "checkpointsIcons", "place<PERSON><PERSON>nt", "background", "cursor", "some", "file", "_file$Base", "Base64", "startsWith", "images", "_file$Base2", "isMaximized", "_file$Base3", "_file$Base4", "videoFile", "paddingBottom", "ChecklistApp", "_ref2", "_checklistGuideMetaDa53", "_checklistGuideMetaDa54", "_checklistGuideMetaDa55", "_checklistGuideMetaDa56", "_checklistGuideMetaDa57", "_checklistGuideMetaDa58", "_checklistGuideMetaDa59", "_checklistGuideMetaDa60", "_checklistGuideMetaDa61", "_checklistGuideMetaDa62", "_checklistGuideMetaDa63", "_checklistGuideMetaDa64", "_checklistGuideMetaDa65", "_checklistGuideMetaDa66", "_checklistGuideMetaDa67", "_checklistGuideMetaDa68", "_checklistGuideMetaDa69", "_checklistGuideMetaDa70", "_checklistGuideMetaDa71", "_checklistGuideMetaDa72", "_checklistGuideMetaDa73", "_checklistGuideMetaDa74", "_checklistGuideMetaDa75", "_checklistGuideMetaDa76", "_checklistGuideMetaDa77", "_checklistGuideMetaDa78", "_checklistGuideMetaDa79", "_checklistGuideMetaDa80", "_checklistGuideMetaDa81", "_checklistGuideMetaDa82", "_checklistGuideMetaDa83", "_checklistGuideMetaDa84", "_checklistGuideMetaDa85", "setShowLauncherSettings", "showLauncherSettings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_initialSelectedIcon$2", "modifiedIcon", "setIsOpen", "remainingCount", "setRemainingCount", "handleRemainingCountUpdate", "anchorEl", "setAnchorEl", "currentStep", "setCurrentStep", "isAnnouncementOpen", "setAnnouncementOpen", "scrollPercentage", "setScrollPercentage", "currentUrl", "location", "href", "launcherColor", "type", "boxShadow", "transition", "text", "textColor", "notificationBadge", "top", "right", "notificationBadgeColor", "notificationTextColor"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistPopup.tsx"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from 'react';\r\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport LauncherSettings from './LauncherSettings';\r\nimport ImageCarousel from \"./ImageCarousel\";\r\nimport VideoPlayer from \"./VideoPlayer\";\r\nimport { editInteractionName } from './Chekpoints';\r\nimport { GetGudeDetailsByGuideId } from '../../services/GuideListServices';\r\nimport { closeicon, closepluginicon, maximize, chkicn1, chkdefault } from '../../assets/icons/icons';\r\nimport AlertPopup from '../drawer/AlertPopup';\r\nimport '../../styles/rtl_styles.scss';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\n\r\ninterface CheckListPopupProps {\r\n  isOpen: any;\r\n  onClose: () => void;\r\n  onRemainingCountUpdate: (formattedCount: string) => void;\r\n  data: any;\r\n  guideDetails: any;\r\n  setopenWarning: any;\r\n  handleLeave: () => void;\r\n\r\n}\r\n// Function to modify the color of an SVG icon\r\nconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\tif (!base64SVG) {\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\ttry {\r\n\t\t// Check if the string is a valid base64 SVG\r\n\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t}\r\n\r\n\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t} else if (hasColoredFill) {\r\n\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t} else {\r\n\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t}\r\n\r\n\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\treturn modifiedBase64;\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\treturn base64SVG; // Return the original if there's an error\r\n\t}\r\n};\r\n\r\nconst ChecklistPopup: React.FC<CheckListPopupProps> = ({\r\n\tisOpen,\r\n\tonClose,\r\n\tonRemainingCountUpdate,\r\n\tdata,\r\n\tguideDetails,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst { checklistGuideMetaData, checkpointsEditPopup, isUnSavedChanges, openWarning } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\r\n\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t// State to track which steps are completed\r\n\tconst [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});\r\n\r\n\t// Map checkpoints and set the first one as completed by default\r\n\tconst checkpointslistData =\r\n\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({\r\n\t\t\t...checkpoint,\r\n\t\t\tcompleted: index === 0 ? true : false,\r\n\t\t})) || [];\r\n\r\n\tconst [checklistItems, setChecklistItems] = useState(checkpointslistData);\r\n\tconst [activeItem, setActiveItem] = useState(\r\n\t\tcheckpointslistData.length > 0 && !checkpointsEditPopup ? checkpointslistData[0]?.id : \"default-placeholder\"\r\n\t);\r\n\tconst checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;\r\n\r\nfunction getLocalizedTitleSubTitle(ts: any, t: any) {\r\n\t  const defaultTitle = \"Checklist Title\";\r\n\t  const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\r\n\t  return {\r\n\t    title:\r\n\t      !ts?.title || ts.title === defaultTitle\r\n\t        ? t(defaultTitle, { defaultValue: defaultTitle })\r\n\t        : ts.title,\r\n\t    subTitle:\r\n\t      !ts?.subTitle || ts.subTitle === defaultSubTitle\r\n\t        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })\r\n\t        : ts.subTitle,\r\n\t  };\r\n\t}\r\n\r\n\tconst localizedTitleSubTitle = getLocalizedTitleSubTitle(checklistGuideMetaData[0]?.TitleSubTitle, translate); {/*this is here what i did is i am getting the title and subtitle from the checklistGuideMetaData and then i am using it for modifying inital translation of the title and subtitle so if you want to modify data such as color and all go to store and look for checklistGuideMetaData */ }\r\n\r\n\t// Initialize completedStatus when checkpointslistData changes\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length > 0) {\r\n\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t\t\tcheckpointslistData.forEach((item: any, index: number) => {\r\n\t\t\t\t// Set the first item as completed by default, others as not completed\r\n\t\t\t\tinitialCompletedStatus[item.id] = index === 0;\r\n\t\t\t});\r\n\r\n\t\t\t// console.log(\"Initializing completedStatus:\", initialCompletedStatus);\r\n\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\r\n\t\t\t// Calculate and update the initial remaining count\r\n\t\t\tconst remainingItems = checkpointslistData.length - 1; // All items except the first one\r\n\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t// console.log(\"Initial remaining count set to:\", formattedCount);\r\n\r\n\t\t\t// Update localStorage directly\r\n\t\t\tif (window.localStorage) {\r\n\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t}\r\n\r\n\t\t\t// Call the callback\r\n\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [checkpointslistData, onRemainingCountUpdate,completedStatus]);\r\n\tuseEffect(() => {\r\n\t\tdocument.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\r\n\t}, [checklistColor]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length === 0) {\r\n\t\t\tsetActiveItem(\"default-placeholder\");\r\n\t\t} else {\r\n\t\t\tsetActiveItem(checkpointslistData[0]?.id);\r\n\t\t}\r\n\t}, [checkpointslistData.length == 1]);\r\n\r\n\tconst [icons, setIcons] = useState<any[]>([\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tcomponent: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t/>\r\n\t\t\t),\r\n\t\t\tselected: true,\r\n\t\t},\r\n\t]);\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t};\r\n\tlet base64Icon: any;\r\n\r\n\tconst initialSelectedIcon = icons.find((icon) => icon.selected);\r\n\tif (initialSelectedIcon && checkpointslistData.length == 0) {\r\n\t\tconst svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\r\n\t\tif (svgElement) {\r\n\t\t\tbase64Icon = encodeToBase64(svgElement);\r\n\t\t}\r\n\t}\r\n\tconst iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || \"#fff\"; // Default to black if no color\r\n\tconst base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon || base64Icon;\r\n\r\n\tconst totalItems = checkpointslistData.length || 1;\r\n\tconst progress = 1;\r\n\r\n\t// Update the remaining count whenever completedStatus or checkpointslistData changes\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length > 0 && Object.keys(completedStatus).length > 0) {\r\n\t\t\t// Count the number of incomplete steps\r\n\t\t\tconst remainingItems =\r\n\t\t\t\tcheckpointslistData.length - Object.values(completedStatus).filter((status) => status).length;\r\n\t\t\t// Format with leading zero (01, 02, 03, etc.)\r\n\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t// console.log(\"ChecklistPopup updating count to:\", formattedCount, \"from completedStatus:\", completedStatus);\r\n\r\n\t\t\t// Make sure the callback is called with the updated count\r\n\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t}\r\n\r\n\t\t\t// Also update the count in the drawerStore to ensure it's available to all components\r\n\t\t\tif (window.localStorage) {\r\n\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [completedStatus, checkpointslistData, onRemainingCountUpdate]);\r\n\r\n\tconst toggleItemCompletion = (id: string) => {\r\n\t\tsetCompletedStatus((prevStatus) => {\r\n\t\t\tconst newStatus = {\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: !prevStatus[id],\r\n\t\t\t};\r\n\r\n\t\t\t// Update the remaining count immediately\r\n\t\t\tif (checkpointslistData.length > 0) {\r\n\t\t\t\tconst remainingItems = checkpointslistData.length - Object.values(newStatus).filter((status) => status).length;\r\n\t\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t\tconsole.log(\"toggleItemCompletion updating count to:\", formattedCount);\r\n\r\n\t\t\t\t// Update localStorage directly\r\n\t\t\t\tif (window.localStorage) {\r\n\t\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Call the callback\r\n\t\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn newStatus;\r\n\t\t});\r\n\t};\r\n\r\n\t// Mark the active item as completed\r\n\tconst handleMarkAsCompleted = () => {\r\n\t\tif (activeItem && activeItem !== \"default-placeholder\") {\r\n\t\t\tsetCompletedStatus((prevStatus) => {\r\n\t\t\t\tconst newStatus = {\r\n\t\t\t\t\t...prevStatus,\r\n\t\t\t\t\t[activeItem]: true,\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// Update the remaining count immediately\r\n\t\t\t\tif (checkpointslistData.length > 0) {\r\n\t\t\t\t\tconst remainingItems =\r\n\t\t\t\t\t\tcheckpointslistData.length - Object.values(newStatus).filter((status) => status).length;\r\n\t\t\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t\t\tconsole.log(\"handleMarkAsCompleted updating count to:\", formattedCount);\r\n\r\n\t\t\t\t\t// Update localStorage directly\r\n\t\t\t\t\tif (window.localStorage) {\r\n\t\t\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Call the callback\r\n\t\t\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log(\"Marked item as completed:\", activeItem);\r\n\t\t\t\treturn newStatus;\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleSelect = (id: any) => {\r\n\t\tsetActiveItem(id);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tonClose();\r\n\t\tsetActiveItem(checkpointslistData[0]?.id);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tif (!isOpen) return null;\r\n\tconst selectedItem =\r\n\t\tcheckpointslistData.length > 0\r\n\t\t\t? checkpointslistData.find((item: any) =>\r\n\t\t\t\t\tcheckpointsEditPopup ? item.id === editInteractionName : item.id === activeItem\r\n\t\t\t  ) || {\r\n\t\t\t\t\tid: \"default-placeholder\",\r\n\t\t\t\ttitle: translate(\"Step Title\", { defaultValue: \"Step Title\" }),\r\n\t\t\t\tdescription: translate(\"Step Description\", { defaultValue: \"Step Description\" }),\r\n\t\t\t\t\ticon: base64Icon,\r\n\t\t\t\tmediaTitle: translate(\"Media Title\", { defaultValue: \"Media Title\" }),\r\n\t\t\t\tmediaDescription: translate(\"Media Description\", { defaultValue: \"Media Description\" }),\r\n\t\t\t\t\tsupportingMedia: [],\r\n\t\t\t  }\r\n\t\t\t: {\r\n\t\t\t\t\tid: \"default-placeholder\",\r\n\t\t\t\ttitle: translate(\"Step Title\", { defaultValue: \"Step Title\" }),\r\n\t\t\t\tdescription: translate(\"Step Description\", { defaultValue: \"Step Description\" }),\r\n\t\t\t\t\ticon: base64Icon,\r\n\t\t\t\tmediaTitle: translate(\"Media Title\", { defaultValue: \"Media Title\" }),\r\n\t\t\t\tmediaDescription: translate(\"Media Description\", { defaultValue: \"Media Description\" }),\r\n\t\t\t\t\tsupportingMedia: [],\r\n\t\t\t  };\r\n\r\n\tconst handleNavigate = () => {\r\n\t\t// window.open(\"http://localhost:3000/\", '_blank');\r\n\t};\r\n\tconst isRTL = \r\n  document.documentElement.getAttribute('dir') === 'rtl' ||\r\n  document.body.getAttribute('dir') === 'rtl';\r\n\t\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{isUnSavedChanges && openWarning && (\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t<div\r\n\t\t\t\tstyle={{\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\tinset: 0,\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\tzIndex: 99999,\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.3)\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t></div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\t\r\n\t\t\t\t\tclassName='qadpt-chkpopup'\r\n\t\t\t\t>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\twidth: `${checklistGuideMetaData[0]?.canvas?.width || 930}px`,\r\n\t\t\t\t\t\t\theight: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\toverflow: \"auto hidden\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chkrgt\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"24px 24px 16px 24px\",\r\n\t\t\t\t\t\t\t\t\t\ttextAlign : isRTL ? 'right' : 'left',\r\n\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{localizedTitleSubTitle.title}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || \"#8D8D8D\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-subtl\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{localizedTitleSubTitle.subTitle}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span style={{ fontSize: \"14px\", color: \"#6b7280\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{progress}/{totalItems}\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5e7eb\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: `${(progress / totalItems) * 100}%`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div style={{ overflow: \"auto\", maxHeight:  `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`}}>\r\n\t\t\t\t\t\t\t\t\t{checkpointslistData.length === 0 ? (\r\n\t\t\t\t\t\t\t\t\t\t<div className={`${activeItem === \"default-placeholder\" ? \"qadpt-chkstp\" : \"\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName='qadpt-chkstpctn'\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Default Icon */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={base64Icon}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ color: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Step Title\", { defaultValue: \"Step Title\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey=\"default\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={true}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", marginTop: \"8px\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"1.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Step Description\", { defaultValue: \"Step Description\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\tcheckpointslistData?.map((item: any) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName={`${\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(checkpointsEditPopup ? editInteractionName === item.id : activeItem === item.id)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"qadpt-chkstp\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}`}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName='qadpt-chkstpctn'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSelect(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ paddingLeft: \"10px\", display: \"flex\", gap: \"6px\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 60px)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.icon && typeof item.icon === \"string\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || \"#333\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ width: \"16px\", height: \"16px\" }}></span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.title}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={completedStatus[item.id] || false}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => toggleItemCompletion(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.description}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: maximize }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t    height: \"calc(100% - 90px)\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t\t<div style={{\r\n    \t\t\t\t\t\t\t\t\toverflow: \"hidden auto\",display: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",width:\"-webkit-fill-available\"}} >\r\n\r\n\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:image\")) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={\"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.Base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={\"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate('Check tasks, stay organized, and finish strong!', { defaultValue: 'Check tasks, stay organized, and finish strong!' })}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\": \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"#8D8D8D\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaDescription}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate('Take Tour', { defaultValue: 'Take Tour' })}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleMarkAsCompleted}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate('Mark as Completed', { defaultValue: 'Mark as Completed' })}\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nconst ChecklistApp = ({ setopenWarning, handleLeave }: { setopenWarning: any; handleLeave: any }) => {\r\n\tconst { checklistGuideMetaData, setShowLauncherSettings, showLauncherSettings, setOpenWarning } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\tlet base64Icon: any;\r\n\tconst [icons, setIcons] = useState<any[]>([\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tcomponent: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t/>\r\n\t\t\t),\r\n\t\t\tselected: true,\r\n\t\t},\r\n\t]);\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t};\r\n\tconst iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || \"#fff\"; // Default to black if no color\r\n\tconst base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon;\r\n\r\n\tconst initialSelectedIcon = icons.find((icon) => icon.selected);\r\n\tif (initialSelectedIcon) {\r\n\t\tconst svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\r\n\t\tif (svgElement) {\r\n\t\t\tbase64Icon = encodeToBase64(svgElement);\r\n\t\t}\r\n\t}\r\n\t// Using the modifySVGColor function defined at the top of the file\r\n\tconst modifiedIcon = modifySVGColor(base64IconFinal || base64Icon, iconColor);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [remainingCount, setRemainingCount] = useState(\"00\");\r\n\r\n\tconst handleRemainingCountUpdate = (formattedCount: string) => {\r\n\t\tsetRemainingCount(formattedCount);\r\n\t};\r\n\r\n\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);\r\n\tconst [currentStep, setCurrentStep] = useState(0);\r\n\tconst [isAnnouncementOpen, setAnnouncementOpen] = useState(false);\r\n\tconst [scrollPercentage, setScrollPercentage] = useState(0);\r\n\tconst currentUrl = window.location.href;\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{/* Your main app content here */}\r\n\r\n\t\t\t{/* Floating action button */}\r\n\t\t\t<div\r\n  className='qadpt-chklayout'\r\n>\r\n\t\t\t\t<button\r\n\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\tsetIsOpen(true);\r\n\t\t\t\t\t\tsetShowLauncherSettings(true);\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher.launcherColor,\r\n\t\t\t\t\t\tcolor: \"white\",\r\n\t\t\t\t\t\tborderRadius:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? \"16px\"\r\n\t\t\t\t\t\t\t\t: \"50%\",\r\n\t\t\t\t\t\theight: \"54px\",\r\n\t\t\t\t\t\twidth:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? `auto`\r\n\t\t\t\t\t\t\t\t: \"54px\",\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tboxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\r\n\t\t\t\t\t\ttransition: \"all 0.2s ease\",\r\n\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon\" && (\r\n\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Text Only */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Text\" && checklistGuideMetaData[0]?.launcher?.text && (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{checklistGuideMetaData[0].launcher.text}\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Icon + Text */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon+Txt\" &&\r\n\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher?.text &&\r\n\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher?.icon && (\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.text}\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Notification Badge */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.notificationBadge && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"-8px\",\r\n\t\t\t\t\t\t\t\tright: \"-8px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher?.notificationBadgeColor,\r\n\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.notificationTextColor,\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{remainingCount}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</button>\r\n\t\t\t</div>\r\n\r\n\t\t\t{showLauncherSettings && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<LauncherSettings />\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\r\n\t\t\t{/* Popup component */}\r\n\t\t\t<ChecklistPopup\r\n\t\t\t\tdata={\"\"}\r\n\t\t\t\tguideDetails={\"\"}\r\n\t\t\t\tisOpen={isOpen}\r\n\t\t\t\tonClose={() => setIsOpen(true)}\r\n\t\t\t\tonRemainingCountUpdate={handleRemainingCountUpdate}\r\n\t\t\t\tsetopenWarning={setOpenWarning}\r\n\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t/>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default ChecklistApp;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAWC,QAAQ,KAAQ,OAAO,CAC3D,MAAO,CAAAC,eAAe,KAAM,sBAAsB,CAClD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CACjD,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,OAASC,mBAAmB,KAAQ,cAAc,CAElD,OAAoBC,eAAe,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,UAAU,KAAQ,0BAA0B,CACpG,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,MAAO,8BAA8B,CACrC,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAc/C;AACA,KAAM,CAAAC,cAAc,CAAGA,CAACC,SAAc,CAAEC,KAAU,GAAK,CACtD,GAAI,CAACD,SAAS,CAAE,CACf,MAAO,EAAE,CACV,CAEA,GAAI,CACH;AACA,GAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,CAAE,CACtD,MAAO,CAAAF,SAAS,CAAE;AACnB,CAEA,KAAM,CAAAG,UAAU,CAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAC,SAAS,CAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAK,cAAc,CAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC,CAE/D,GAAI,CAAAM,WAAW,CAAGN,UAAU,CAE5B,GAAIG,SAAS,EAAI,CAACC,cAAc,CAAE,CACjC;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,CAAE,WAAWT,KAAK,GAAG,CAAC,CAC1E,CAAC,IAAM,IAAIM,cAAc,CAAE,CAC1B;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,CAAE,SAAST,KAAK,GAAG,CAAC,CAC9E,CAAC,IAAM,CACN;AACAQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,CAAE,eAAeT,KAAK,GAAG,CAAC,CAClFQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,CAAE,cAAcT,KAAK,GAAG,CAAC,CACjF,CAEA,KAAM,CAAAU,cAAc,CAAG,6BAA6BC,IAAI,CAACH,WAAW,CAAC,EAAE,CACvE,MAAO,CAAAE,cAAc,CACtB,CAAE,MAAOE,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CAAAb,SAAS,CAAE;AACnB,CACD,CAAC,CAED,KAAM,CAAAe,cAA6C,CAAGC,IAAA,EAQhD,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IARiD,CACtDC,MAAM,CACNC,OAAO,CACPC,sBAAsB,CACtBC,IAAI,CACJC,YAAY,CACZC,cAAc,CACdC,WACD,CAAC,CAAAzD,IAAA,CACA,KAAM,CAAE0D,CAAC,CAAEC,SAAU,CAAC,CAAGnF,cAAc,CAAC,CAAC,CACzC,KAAM,CAAEoF,sBAAsB,CAAEC,oBAAoB,CAAEC,gBAAgB,CAAEC,WAAY,CAAC,CAAGjG,cAAc,CACpGkG,KAAU,EAAKA,KACjB,CAAC,CAED,KAAM,CAAAC,sBAAkD,CAAG,CAAC,CAAC,CAE7D;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGvG,QAAQ,CAA6B,CAAC,CAAC,CAAC,CAEtF;AACA,KAAM,CAAAwG,mBAAmB,CACxB,EAAAnE,qBAAA,CAAA2D,sBAAsB,CAAC,CAAC,CAAC,UAAA3D,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA2BoE,WAAW,UAAAnE,sBAAA,kBAAAC,sBAAA,CAAtCD,sBAAA,CAAwCoE,eAAe,UAAAnE,sBAAA,iBAAvDA,sBAAA,CAAyDoE,GAAG,CAAC,CAACC,UAAe,CAAEC,KAAa,IAAM,CACjG,GAAGD,UAAU,CACbE,SAAS,CAAED,KAAK,GAAK,CAAC,CAAG,IAAI,CAAG,KACjC,CAAC,CAAC,CAAC,GAAI,EAAE,CAEV,KAAM,CAACE,cAAc,CAAEC,iBAAiB,CAAC,CAAGhH,QAAQ,CAACwG,mBAAmB,CAAC,CACzE,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGlH,QAAQ,CAC3CwG,mBAAmB,CAACW,MAAM,CAAG,CAAC,EAAI,CAAClB,oBAAoB,EAAAzD,qBAAA,CAAGgE,mBAAmB,CAAC,CAAC,CAAC,UAAAhE,qBAAA,iBAAtBA,qBAAA,CAAwB4E,EAAE,CAAG,qBACxF,CAAC,CACD,KAAM,CAAAC,cAAc,EAAA5E,sBAAA,CAAGuD,sBAAsB,CAAC,CAAC,CAAC,UAAAvD,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA2B6E,MAAM,UAAA5E,sBAAA,iBAAjCA,sBAAA,CAAmC6E,YAAY,CAEvE,QAAS,CAAAC,yBAAyBA,CAACC,EAAO,CAAE3B,CAAM,CAAE,CACjD,KAAM,CAAA4B,YAAY,CAAG,iBAAiB,CACtC,KAAM,CAAAC,eAAe,CAAG,oFAAoF,CAC5G,MAAO,CACLC,KAAK,CACH,EAACH,EAAE,SAAFA,EAAE,WAAFA,EAAE,CAAEG,KAAK,GAAIH,EAAE,CAACG,KAAK,GAAKF,YAAY,CACnC5B,CAAC,CAAC4B,YAAY,CAAE,CAAEG,YAAY,CAAEH,YAAa,CAAC,CAAC,CAC/CD,EAAE,CAACG,KAAK,CACdE,QAAQ,CACN,EAACL,EAAE,SAAFA,EAAE,WAAFA,EAAE,CAAEK,QAAQ,GAAIL,EAAE,CAACK,QAAQ,GAAKH,eAAe,CAC5C7B,CAAC,CAAC6B,eAAe,CAAE,CAAEE,YAAY,CAAEF,eAAgB,CAAC,CAAC,CACrDF,EAAE,CAACK,QACX,CAAC,CACH,CAEA,KAAM,CAAAC,sBAAsB,CAAGP,yBAAyB,EAAA7E,sBAAA,CAACqD,sBAAsB,CAAC,CAAC,CAAC,UAAArD,sBAAA,iBAAzBA,sBAAA,CAA2BqF,aAAa,CAAEjC,SAAS,CAAC,CAAE,CAAC,0RAEhH;AACAhG,SAAS,CAAC,IAAM,CACf,GAAIyG,mBAAmB,CAACW,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAAd,sBAAkD,CAAG,CAAC,CAAC,CAE7DG,mBAAmB,CAACyB,OAAO,CAAC,CAACC,IAAS,CAAErB,KAAa,GAAK,CACzD;AACAR,sBAAsB,CAAC6B,IAAI,CAACd,EAAE,CAAC,CAAGP,KAAK,GAAK,CAAC,CAC9C,CAAC,CAAC,CAEF;AACAN,kBAAkB,CAACF,sBAAsB,CAAC,CAE1C;AACA,KAAM,CAAA8B,cAAc,CAAG3B,mBAAmB,CAACW,MAAM,CAAG,CAAC,CAAE;AACvD,KAAM,CAAAiB,cAAc,CAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACjE;AAEA;AACA,GAAIC,MAAM,CAACC,YAAY,CAAE,CACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAEL,cAAc,CAAC,CAC9D,CAEA;AACA,GAAI3C,sBAAsB,CAAE,CAC3BA,sBAAsB,CAAC2C,cAAc,CAAC,CACvC,CACD,CACD,CAAC,CAAE,CAAC5B,mBAAmB,CAAEf,sBAAsB,CAACa,eAAe,CAAC,CAAC,CACjEvG,SAAS,CAAC,IAAM,CACf2I,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,YAAY,CAAExB,cAAc,CAAC,CACzE,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CAEpBtH,SAAS,CAAC,IAAM,CACf,GAAIyG,mBAAmB,CAACW,MAAM,GAAK,CAAC,CAAE,CACrCD,aAAa,CAAC,qBAAqB,CAAC,CACrC,CAAC,IAAM,KAAA4B,sBAAA,CACN5B,aAAa,EAAA4B,sBAAA,CAACtC,mBAAmB,CAAC,CAAC,CAAC,UAAAsC,sBAAA,iBAAtBA,sBAAA,CAAwB1B,EAAE,CAAC,CAC1C,CACD,CAAC,CAAE,CAACZ,mBAAmB,CAACW,MAAM,EAAI,CAAC,CAAC,CAAC,CAErC,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAGhJ,QAAQ,CAAQ,CACzC,CACCoH,EAAE,CAAE,CAAC,CACL6B,SAAS,cACRnI,IAAA,SACCoI,uBAAuB,CAAE,CAAEC,MAAM,CAAE1I,OAAQ,CAAE,CAC7CmI,KAAK,CAAE,CAAEQ,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,IACX,CAAC,CACD,CAAC,CACF,KAAM,CAAAC,cAAc,CAAIC,SAAiB,EAAK,CAC7C,MAAO,6BAA6BxH,IAAI,CAACwH,SAAS,CAAC,EAAE,CACtD,CAAC,CACD,GAAI,CAAAC,UAAe,CAEnB,KAAM,CAAAC,mBAAmB,CAAGX,KAAK,CAACY,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACN,QAAQ,CAAC,CAC/D,GAAII,mBAAmB,EAAIlD,mBAAmB,CAACW,MAAM,EAAI,CAAC,CAAE,KAAA0C,qBAAA,CAC3D,KAAM,CAAAC,UAAU,EAAAD,qBAAA,CAAGH,mBAAmB,CAACT,SAAS,CAACc,KAAK,CAACb,uBAAuB,UAAAW,qBAAA,iBAA3DA,qBAAA,CAA6DV,MAAM,CAEtF,GAAIW,UAAU,CAAE,CACfL,UAAU,CAAGF,cAAc,CAACO,UAAU,CAAC,CACxC,CACD,CACA,KAAM,CAAAE,SAAS,CAAG,EAAApH,sBAAA,CAAAoD,sBAAsB,CAAC,CAAC,CAAC,UAAApD,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA2BqH,QAAQ,UAAApH,sBAAA,iBAAnCA,sBAAA,CAAqCmH,SAAS,GAAI,MAAM,CAAE;AAC5E,KAAM,CAAAE,eAAe,CAAG,EAAApH,sBAAA,CAAAkD,sBAAsB,CAAC,CAAC,CAAC,UAAAlD,sBAAA,kBAAAC,uBAAA,CAAzBD,sBAAA,CAA2BmH,QAAQ,UAAAlH,uBAAA,iBAAnCA,uBAAA,CAAqC6G,IAAI,GAAIH,UAAU,CAE/E,KAAM,CAAAU,UAAU,CAAG3D,mBAAmB,CAACW,MAAM,EAAI,CAAC,CAClD,KAAM,CAAAiD,QAAQ,CAAG,CAAC,CAElB;AACArK,SAAS,CAAC,IAAM,CACf,GAAIyG,mBAAmB,CAACW,MAAM,CAAG,CAAC,EAAIkD,MAAM,CAACC,IAAI,CAAChE,eAAe,CAAC,CAACa,MAAM,CAAG,CAAC,CAAE,CAC9E;AACA,KAAM,CAAAgB,cAAc,CACnB3B,mBAAmB,CAACW,MAAM,CAAGkD,MAAM,CAACE,MAAM,CAACjE,eAAe,CAAC,CAACkE,MAAM,CAAEC,MAAM,EAAKA,MAAM,CAAC,CAACtD,MAAM,CAC9F;AACA,KAAM,CAAAiB,cAAc,CAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACjE;AAEA;AACA,GAAI7C,sBAAsB,CAAE,CAC3BA,sBAAsB,CAAC2C,cAAc,CAAC,CACvC,CAEA;AACA,GAAIG,MAAM,CAACC,YAAY,CAAE,CACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAEL,cAAc,CAAC,CAC9D,CACD,CACD,CAAC,CAAE,CAAC9B,eAAe,CAAEE,mBAAmB,CAAEf,sBAAsB,CAAC,CAAC,CAElE,KAAM,CAAAiF,oBAAoB,CAAItD,EAAU,EAAK,CAC5Cb,kBAAkB,CAAEoE,UAAU,EAAK,CAClC,KAAM,CAAAC,SAAS,CAAG,CACjB,GAAGD,UAAU,CACb,CAACvD,EAAE,EAAG,CAACuD,UAAU,CAACvD,EAAE,CACrB,CAAC,CAED;AACA,GAAIZ,mBAAmB,CAACW,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAAgB,cAAc,CAAG3B,mBAAmB,CAACW,MAAM,CAAGkD,MAAM,CAACE,MAAM,CAACK,SAAS,CAAC,CAACJ,MAAM,CAAEC,MAAM,EAAKA,MAAM,CAAC,CAACtD,MAAM,CAC9G,KAAM,CAAAiB,cAAc,CAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACjEpG,OAAO,CAAC2I,GAAG,CAAC,yCAAyC,CAAEzC,cAAc,CAAC,CAEtE;AACA,GAAIG,MAAM,CAACC,YAAY,CAAE,CACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAEL,cAAc,CAAC,CAC9D,CAEA;AACA,GAAI3C,sBAAsB,CAAE,CAC3BA,sBAAsB,CAAC2C,cAAc,CAAC,CACvC,CACD,CAEA,MAAO,CAAAwC,SAAS,CACjB,CAAC,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAE,qBAAqB,CAAGA,CAAA,GAAM,CACnC,GAAI7D,UAAU,EAAIA,UAAU,GAAK,qBAAqB,CAAE,CACvDV,kBAAkB,CAAEoE,UAAU,EAAK,CAClC,KAAM,CAAAC,SAAS,CAAG,CACjB,GAAGD,UAAU,CACb,CAAC1D,UAAU,EAAG,IACf,CAAC,CAED;AACA,GAAIT,mBAAmB,CAACW,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAAgB,cAAc,CACnB3B,mBAAmB,CAACW,MAAM,CAAGkD,MAAM,CAACE,MAAM,CAACK,SAAS,CAAC,CAACJ,MAAM,CAAEC,MAAM,EAAKA,MAAM,CAAC,CAACtD,MAAM,CACxF,KAAM,CAAAiB,cAAc,CAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACjEpG,OAAO,CAAC2I,GAAG,CAAC,0CAA0C,CAAEzC,cAAc,CAAC,CAEvE;AACA,GAAIG,MAAM,CAACC,YAAY,CAAE,CACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAEL,cAAc,CAAC,CAC9D,CAEA;AACA,GAAI3C,sBAAsB,CAAE,CAC3BA,sBAAsB,CAAC2C,cAAc,CAAC,CACvC,CACD,CAEAlG,OAAO,CAAC2I,GAAG,CAAC,2BAA2B,CAAE5D,UAAU,CAAC,CACpD,MAAO,CAAA2D,SAAS,CACjB,CAAC,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAAG,YAAY,CAAI3D,EAAO,EAAK,CACjCF,aAAa,CAACE,EAAE,CAAC,CAClB,CAAC,CAED,KAAM,CAAA4D,WAAW,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CACzBzF,OAAO,CAAC,CAAC,CACT0B,aAAa,EAAA+D,sBAAA,CAACzE,mBAAmB,CAAC,CAAC,CAAC,UAAAyE,sBAAA,iBAAtBA,sBAAA,CAAwB7D,EAAE,CAAC,CAC1C,CAAC,CAED,GAAI,CAAC7B,MAAM,CAAE,MAAO,KAAI,CAExB,GAAI,CAACA,MAAM,CAAE,MAAO,KAAI,CACxB,KAAM,CAAA2F,YAAY,CACjB1E,mBAAmB,CAACW,MAAM,CAAG,CAAC,CAC3BX,mBAAmB,CAACmD,IAAI,CAAEzB,IAAS,EACnCjC,oBAAoB,CAAGiC,IAAI,CAACd,EAAE,GAAK9G,mBAAmB,CAAG4H,IAAI,CAACd,EAAE,GAAKH,UACrE,CAAC,EAAI,CACLG,EAAE,CAAE,qBAAqB,CAC1BQ,KAAK,CAAE7B,SAAS,CAAC,YAAY,CAAE,CAAE8B,YAAY,CAAE,YAAa,CAAC,CAAC,CAC9DsD,WAAW,CAAEpF,SAAS,CAAC,kBAAkB,CAAE,CAAE8B,YAAY,CAAE,kBAAmB,CAAC,CAAC,CAC/E+B,IAAI,CAAEH,UAAU,CACjB2B,UAAU,CAAErF,SAAS,CAAC,aAAa,CAAE,CAAE8B,YAAY,CAAE,aAAc,CAAC,CAAC,CACrEwD,gBAAgB,CAAEtF,SAAS,CAAC,mBAAmB,CAAE,CAAE8B,YAAY,CAAE,mBAAoB,CAAC,CAAC,CACtFyD,eAAe,CAAE,EACjB,CAAC,CACD,CACAlE,EAAE,CAAE,qBAAqB,CAC1BQ,KAAK,CAAE7B,SAAS,CAAC,YAAY,CAAE,CAAE8B,YAAY,CAAE,YAAa,CAAC,CAAC,CAC9DsD,WAAW,CAAEpF,SAAS,CAAC,kBAAkB,CAAE,CAAE8B,YAAY,CAAE,kBAAmB,CAAC,CAAC,CAC/E+B,IAAI,CAAEH,UAAU,CACjB2B,UAAU,CAAErF,SAAS,CAAC,aAAa,CAAE,CAAE8B,YAAY,CAAE,aAAc,CAAC,CAAC,CACrEwD,gBAAgB,CAAEtF,SAAS,CAAC,mBAAmB,CAAE,CAAE8B,YAAY,CAAE,mBAAoB,CAAC,CAAC,CACtFyD,eAAe,CAAE,EACjB,CAAC,CAEL,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5B;AAAA,CACA,CACD,KAAM,CAAAC,KAAK,CACV9C,QAAQ,CAACC,eAAe,CAAC8C,YAAY,CAAC,KAAK,CAAC,GAAK,KAAK,EACtD/C,QAAQ,CAACgD,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,GAAK,KAAK,CAE5C,mBACCzK,KAAA,CAAAE,SAAA,EAAAyK,QAAA,EACEzF,gBAAgB,EAAIC,WAAW,eAC/BrF,IAAA,CAACH,UAAU,EACVwF,WAAW,CAAEA,WAAY,CACzBP,cAAc,CAAEA,cAAe,CAC/BC,WAAW,CAAEA,WAAY,CACzB,CACD,cACD7E,KAAA,QACC4H,KAAK,CAAE,CACNgD,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,CAAC,CACRxC,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB;AACAC,MAAM,CAAE,KAAU,CAAE,CAAAJ,QAAA,eAErB7K,IAAA,QACC8H,KAAK,CAAE,CACNgD,QAAQ,CAAE,UAAU,CACpBC,KAAK,CAAE,CAAC,CACRG,eAAe,CAAE,oBAClB,CAAE,CACFC,OAAO,CAAEjB,WAAY,CAChB,CAAC,cAEPlK,IAAA,QAECoL,SAAS,CAAC,gBAAgB,CAAAP,QAAA,cAE1B7K,IAAA,QACC8H,KAAK,CAAE,CACNoD,eAAe,EAAAhJ,uBAAA,CAAEgD,sBAAsB,CAAC,CAAC,CAAC,UAAAhD,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BsE,MAAM,UAAArE,uBAAA,iBAAjCA,uBAAA,CAAmC+I,eAAe,CACnEG,MAAM,CAAE,IAAAjJ,uBAAA,CAAG8C,sBAAsB,CAAC,CAAC,CAAC,UAAA9C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BoE,MAAM,UAAAnE,uBAAA,iBAAjCA,uBAAA,CAAmCiJ,WAAW,aAAAhJ,uBAAA,CAAY4C,sBAAsB,CAAC,CAAC,CAAC,UAAA5C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BkE,MAAM,UAAAjE,uBAAA,iBAAjCA,uBAAA,CAAmCgJ,WAAW,EAAE,CACrHC,YAAY,CAAE,IAAAhJ,uBAAA,CAAG0C,sBAAsB,CAAC,CAAC,CAAC,UAAA1C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BgE,MAAM,UAAA/D,uBAAA,iBAAjCA,uBAAA,CAAmCgJ,YAAY,IAAI,CACpEC,KAAK,CAAE,GAAG,EAAAhJ,uBAAA,CAAAwC,sBAAsB,CAAC,CAAC,CAAC,UAAAxC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B8D,MAAM,UAAA7D,uBAAA,iBAAjCA,uBAAA,CAAmC+I,KAAK,GAAI,GAAG,IAAI,CAC7DC,MAAM,CAAE,GAAG,EAAA/I,uBAAA,CAAAsC,sBAAsB,CAAC,CAAC,CAAC,UAAAtC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B4D,MAAM,UAAA3D,uBAAA,iBAAjCA,uBAAA,CAAmC8I,MAAM,GAAI,GAAG,IAC5D,CAAE,CAAAd,QAAA,cAEF3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfoD,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MAAM,CACbE,QAAQ,CAAE,aACX,CAAE,CACFR,SAAS,CAAC,kBAAkB,CAAAP,QAAA,eAG5B3K,KAAA,QAECkL,SAAS,CAAC,cAAc,CAAAP,QAAA,eAExB3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfsD,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MAAM,CACXC,YAAY,CAAE,mBAAmB,CACjCC,OAAO,CAAE,qBAAqB,CAC9BC,SAAS,CAAGvB,KAAK,CAAG,OAAO,CAAG,MAE/B,CAAE,CAAAG,QAAA,eAEF3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfsD,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,KACN,CAAE,CAAAjB,QAAA,eAEF7K,IAAA,QACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,CAAArJ,uBAAA,CAAAoC,sBAAsB,CAAC,CAAC,CAAC,UAAApC,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2BoE,aAAa,UAAAnE,uBAAA,WAAxCA,uBAAA,CAA0CqJ,SAAS,CAAG,MAAM,CAAG,QAAQ,CACnFC,SAAS,CAAE,CAAArJ,uBAAA,CAAAkC,sBAAsB,CAAC,CAAC,CAAC,UAAAlC,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2BkE,aAAa,UAAAjE,uBAAA,WAAxCA,uBAAA,CAA0CqJ,WAAW,CAAG,QAAQ,CAAG,QAAQ,CACtF/L,KAAK,CAAE,EAAA2C,uBAAA,CAAAgC,sBAAsB,CAAC,CAAC,CAAC,UAAAhC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BgE,aAAa,UAAA/D,uBAAA,iBAAxCA,uBAAA,CAA0CoJ,UAAU,GAAI,MAAM,CACrEhE,OAAO,CAAE,OAAO,CAChBiE,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YAAY,CACvBd,QAAQ,CAAE,QACX,CAAE,CAAAf,QAAA,CAED5D,sBAAsB,CAACH,KAAK,CACzB,CAAC,cAEN9G,IAAA,QACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,CAAA/I,uBAAA,CAAA8B,sBAAsB,CAAC,CAAC,CAAC,UAAA9B,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2B8D,aAAa,UAAA7D,uBAAA,WAAxCA,uBAAA,CAA0CsJ,YAAY,CAAG,MAAM,CAAG,QAAQ,CACtFN,SAAS,CAAE,CAAA/I,uBAAA,CAAA4B,sBAAsB,CAAC,CAAC,CAAC,UAAA5B,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2B4D,aAAa,UAAA3D,uBAAA,WAAxCA,uBAAA,CAA0CqJ,cAAc,CAAG,QAAQ,CAAG,QAAQ,CACzFrM,KAAK,CAAE,EAAAiD,uBAAA,CAAA0B,sBAAsB,CAAC,CAAC,CAAC,UAAA1B,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B0D,aAAa,UAAAzD,uBAAA,iBAAxCA,uBAAA,CAA0CoJ,aAAa,GAAI,SACnE,CAAE,CACFzB,SAAS,CAAC,aAAa,CAAAP,QAAA,CAEtB5D,sBAAsB,CAACD,QAAQ,CAC5B,CAAC,EACF,CAAC,cAEN9G,KAAA,QAAA2K,QAAA,eACC7K,IAAA,QACC8H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,eAAe,CAC/BC,YAAY,CAAE,KACf,CAAE,CAAAlC,QAAA,cAEF3K,KAAA,SAAM4H,KAAK,CAAE,CAAEoE,QAAQ,CAAE,MAAM,CAAE3L,KAAK,CAAE,SAAU,CAAE,CAAAsK,QAAA,EAClDvB,QAAQ,CAAC,GAAC,CAACD,UAAU,EACjB,CAAC,CACH,CAAC,cACNrJ,IAAA,QACC8H,KAAK,CAAE,CACN6D,MAAM,CAAE,KAAK,CACbT,eAAe,CAAE,SAAS,CAC1BM,YAAY,CAAE,QAAQ,CACtBI,QAAQ,CAAE,QACX,CAAE,CAAAf,QAAA,cAEF7K,IAAA,QACC8H,KAAK,CAAE,CACN6D,MAAM,CAAE,MAAM,CACdT,eAAe,EAAAxH,uBAAA,CAAEwB,sBAAsB,CAAC,CAAC,CAAC,UAAAxB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B8C,MAAM,UAAA7C,uBAAA,iBAAjCA,uBAAA,CAAmC8C,YAAY,CAChE+E,YAAY,CAAE,QAAQ,CACtBE,KAAK,CAAE,GAAIpC,QAAQ,CAAGD,UAAU,CAAI,GAAG,GACxC,CAAE,CACG,CAAC,CACH,CAAC,EACF,CAAC,EACF,CAAC,cAENrJ,IAAA,QAAK8H,KAAK,CAAE,CAAE8D,QAAQ,CAAE,MAAM,CAAEoB,SAAS,CAAG,QAAQ,EAAApJ,uBAAA,CAAAsB,sBAAsB,CAAC,CAAC,CAAC,UAAAtB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B4C,MAAM,UAAA3C,uBAAA,iBAAjCA,uBAAA,CAAmC8H,MAAM,GAAI,GAAG,aAAa,CAAE,CAAAd,QAAA,CACjHnF,mBAAmB,CAACW,MAAM,GAAK,CAAC,cAChCrG,IAAA,QAAKoL,SAAS,CAAE,GAAGjF,UAAU,GAAK,qBAAqB,CAAG,cAAc,CAAG,EAAE,EAAG,CAAA0E,QAAA,cAC/E3K,KAAA,QAECkL,SAAS,CAAC,iBAAiB,CAAAP,QAAA,eAG3B3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,eAAe,CAC/BpB,KAAK,CAAE,MACR,CAAE,CAAAb,QAAA,eAEF3K,KAAA,QAAK4H,KAAK,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEyC,UAAU,CAAE,QAAQ,CAAEc,GAAG,CAAE,KAAM,CAAE,CAAAjB,QAAA,eAEjE7K,IAAA,QACCiN,GAAG,CAAEtE,UAAW,CAChBuE,GAAG,CAAC,MAAM,CACVpF,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACzC,CAAC,cACF3L,IAAA,SAAM8H,KAAK,CAAE,CAAEvH,KAAK,EAAAuD,uBAAA,CAAEoB,sBAAsB,CAAC,CAAC,CAAC,UAAApB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B6B,WAAW,UAAA5B,uBAAA,iBAAtCA,uBAAA,CAAwCoJ,gBAAiB,CAAE,CAAAtC,QAAA,CAC/E5F,SAAS,CAAC,YAAY,CAAE,CAAE8B,YAAY,CAAE,YAAa,CAAC,CAAC,CACnD,CAAC,EACH,CAAC,cACN/G,IAAA,QAAA6K,QAAA,cACC7K,IAAA,CAACb,eAAe,EAEf6G,SAAS,CAAE,IAAK,CAChBmF,OAAO,CAAEA,CAAA,GAAM,CAAC,CAAE,CAClBiC,IAAI,CAAC,IAAI,EAHL,SAIJ,CAAC,CACE,CAAC,EACF,CAAC,cACNpN,IAAA,QAAK8H,KAAK,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEyC,UAAU,CAAE,QAAQ,CAAEqC,SAAS,CAAE,KAAK,CAAEvB,GAAG,CAAE,KAAM,CAAE,CAAAjB,QAAA,cACnF7K,IAAA,MACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChB3L,KAAK,EAAAyD,uBAAA,CAAEkB,sBAAsB,CAAC,CAAC,CAAC,UAAAlB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B2B,WAAW,UAAA1B,uBAAA,iBAAtCA,uBAAA,CAAwCqJ,sBAAsB,CACrEC,UAAU,CAAE,KAAK,CACjBC,MAAM,CAAE,CAAC,CACTf,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA7B,QAAA,CAED5F,SAAS,CAAC,kBAAkB,CAAE,CAAE8B,YAAY,CAAE,kBAAmB,CAAC,CAAC,CAClE,CAAC,CACA,CAAC,EACF,CAAC,CACF,CAAC,CAENrB,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEG,GAAG,CAAEuB,IAAS,OAAAqG,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,oBAClC9N,IAAA,QACCoL,SAAS,CAAE,GACV,CAACjG,oBAAoB,CAAG3F,mBAAmB,GAAK4H,IAAI,CAACd,EAAE,CAAGH,UAAU,GAAKiB,IAAI,CAACd,EAAE,EAC7E,cAAc,CACd,EAAE,EACH,CAAAuE,QAAA,cAEH7K,IAAA,QAECoL,SAAS,CAAC,iBAAiB,CAC3BD,OAAO,CAAEA,CAAA,GAAMlB,YAAY,CAAC7C,IAAI,CAACd,EAAE,CAAE,CAAAuE,QAAA,cAGrC3K,KAAA,QAAK4H,KAAK,CAAE,CAAEiG,WAAW,CAAE,MAAM,CAAExF,OAAO,CAAE,MAAM,CAAEuD,GAAG,CAAE,KAAK,CAAED,aAAa,CAAE,QAAS,CAAE,CAAAhB,QAAA,eACzF3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,eAAe,CAC/BpB,KAAK,CAAE,MACR,CAAE,CAAAb,QAAA,eAEF3K,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpBc,GAAG,CAAE,MAAM,CACXD,aAAa,CAAE,KAAK,CACpBH,KAAK,CAAE,mBACR,CAAE,CAAAb,QAAA,EAEDzD,IAAI,CAAC0B,IAAI,EAAI,MAAO,CAAA1B,IAAI,CAAC0B,IAAI,GAAK,QAAQ,cAC1C9I,IAAA,QACCiN,GAAG,CAAE5M,cAAc,CAAC+G,IAAI,CAAC0B,IAAI,CAAE,EAAA2E,uBAAA,CAAAvI,sBAAsB,CAAC,CAAC,CAAC,UAAAuI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B9H,WAAW,UAAA+H,uBAAA,iBAAtCA,uBAAA,CAAwCM,gBAAgB,GAAI,MAAM,CAAE,CACnGd,GAAG,CAAC,MAAM,CACVpF,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACzC,CAAC,cAEF3L,IAAA,QACC8H,KAAK,CAAE,CACN4D,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdpD,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,QACjB,CAAE,CAAAjC,QAAA,cAEF7K,IAAA,SAAM8H,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAO,CAAC,CACnD,CACL,cAED3L,IAAA,SACC8H,KAAK,CAAE,CACNvH,KAAK,CAAE,EAAAoN,uBAAA,CAAAzI,sBAAsB,CAAC,CAAC,CAAC,UAAAyI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BhI,WAAW,UAAAiI,uBAAA,iBAAtCA,uBAAA,CAAwCT,gBAAgB,GAAI,MAAM,CACzEvB,QAAQ,CAAE,QAAQ,CAClBY,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA7B,QAAA,CAEDzD,IAAI,CAACN,KAAK,CACN,CAAC,EAEH,CAAC,cACN9G,IAAA,QAAA6K,QAAA,cACC7K,IAAA,CAACb,eAAe,EAEf6G,SAAS,CAAER,eAAe,CAAC4B,IAAI,CAACd,EAAE,CAAC,EAAI,KAAM,CAC7C6E,OAAO,CAAEA,CAAA,GAAMvB,oBAAoB,CAACxC,IAAI,CAACd,EAAE,CAAE,CAC7C8G,IAAI,CAAC,IAAI,EAHJhG,IAAI,CAACd,EAIV,CAAC,CACE,CAAC,EACF,CAAC,cACNtG,IAAA,QAAA6K,QAAA,cACC7K,IAAA,MACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChB3L,KAAK,EAAAsN,uBAAA,CAAE3I,sBAAsB,CAAC,CAAC,CAAC,UAAA2I,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BlI,WAAW,UAAAmI,uBAAA,iBAAtCA,uBAAA,CAAwCR,sBAChD,CAAE,CACFlC,SAAS,CAAC,kBAAkB,CAAAP,QAAA,CAE3BzD,IAAI,CAACiD,WAAW,CACf,CAAC,CACA,CAAC,EACF,CAAC,EA5EDjD,IAAI,CAACd,EA6EN,CAAC,CACF,CAAC,EACN,CACD,CACG,CAAC,EACF,CAAC,cAGNpG,KAAA,QACC4H,KAAK,CAAE,CACN4D,KAAK,CAAE,KAAK,CACZM,OAAO,CAAE,kBACV,CAAE,CACFZ,SAAS,CAAC,cAAc,CAAAP,QAAA,eAExB3K,KAAA,QACE4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpBiD,YAAY,CAAE,KAAK,CACnBvC,KAAK,CAAE,MAAM,CACbI,GAAG,CAAE,KACN,CAAE,CAAAjB,QAAA,eAEF7K,IAAA,SACCoI,uBAAuB,CAAE,CAAEC,MAAM,CAAE3I,QAAS,CAAE,CAC9CoI,KAAK,CAAE,CACNoG,UAAU,CAAE,SAAS,CACrB1C,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,KAAK,CACdzD,OAAO,CAAE,MAAM,CACf4F,MAAM,CAAE,SACT,CAAE,CACF,CAAC,cACFnO,IAAA,SACCoI,uBAAuB,CAAE,CAAEC,MAAM,CAAE5I,eAAgB,CAAE,CACrDqI,KAAK,CAAE,CACNoG,UAAU,CAAE,SAAS,CACrB1C,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,KAAK,CACdzD,OAAO,CAAE,MAAM,CACf4F,MAAM,CAAE,SACT,CAAE,CACF,CAAC,EACE,CAAC,cACPnO,IAAA,QACC8H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpBa,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MAAM,CACPH,MAAM,CAAE,mBACb,CAAE,CAAAd,QAAA,cAIF3K,KAAA,QAAK4H,KAAK,CAAE,CACR8D,QAAQ,CAAE,aAAa,CAACrD,OAAO,CAAE,MAAM,CAC1CyC,UAAU,CAAE,QAAQ,CACpBa,aAAa,CAAE,QAAQ,CAACH,KAAK,CAAC,wBAAwB,CAAE,CAAAb,QAAA,EAExD,CAAAT,YAAY,SAAZA,YAAY,kBAAAlG,qBAAA,CAAZkG,YAAY,CAAEI,eAAe,UAAAtG,qBAAA,iBAA7BA,qBAAA,CAA+BmC,MAAM,EAAG,CAAC,eACzCnG,KAAA,CAAAE,SAAA,EAAAyK,QAAA,EACET,YAAY,CAACI,eAAe,CAAC4D,IAAI,CAAEC,IAAS,OAAAC,UAAA,OAAK,CAAAD,IAAI,SAAJA,IAAI,kBAAAC,UAAA,CAAJD,IAAI,CAAEE,MAAM,UAAAD,UAAA,iBAAZA,UAAA,CAAcE,UAAU,CAAC,YAAY,CAAC,GAAC,eACxFxO,IAAA,CAACV,aAAa,EACb8K,YAAY,CAAEA,YAAa,CAC3BjE,UAAU,CAAEA,UAAW,CACvBsI,MAAM,CAAErE,YAAY,CAACI,eAAe,CAClCd,MAAM,CAAE2E,IAAS,OAAAK,WAAA,OAAK,CAAAL,IAAI,SAAJA,IAAI,kBAAAK,WAAA,CAAJL,IAAI,CAAEE,MAAM,UAAAG,WAAA,iBAAZA,WAAA,CAAcF,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7D3I,GAAG,CAAEwI,IAAS,EAAKA,IAAI,CAACE,MAAM,CAAE,CAClCI,WAAW,CAAE,EAAG,CAChB,CACD,CAEAvE,YAAY,CAACI,eAAe,CAAC4D,IAAI,CAAEC,IAAS,OAAAO,WAAA,OAAK,CAAAP,IAAI,SAAJA,IAAI,kBAAAO,WAAA,CAAJP,IAAI,CAAEE,MAAM,UAAAK,WAAA,iBAAZA,WAAA,CAAcJ,UAAU,CAAC,YAAY,CAAC,GAAC,EACxFpE,YAAY,CAACI,eAAe,CAC1Bd,MAAM,CAAE2E,IAAS,OAAAQ,WAAA,OAAK,CAAAR,IAAI,SAAJA,IAAI,kBAAAQ,WAAA,CAAJR,IAAI,CAAEE,MAAM,UAAAM,WAAA,iBAAZA,WAAA,CAAcL,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7D3I,GAAG,CAAC,CAACwI,IAAS,CAAEtI,KAAa,gBAC7B/F,IAAA,CAACT,WAAW,EAEXuP,SAAS,CAAET,IAAI,CAACE,MAAO,CACvBI,WAAW,CAAE,EAAG,EAFX5I,KAGL,CACD,CAAC,EACH,CACF,CACA,CAAC,CAAAqE,YAAY,SAAZA,YAAY,kBAAAjG,sBAAA,CAAZiG,YAAY,CAAEI,eAAe,UAAArG,sBAAA,iBAA7BA,sBAAA,CAA+BkC,MAAM,IAAK,CAAC,EAAI,EAAC+D,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEI,eAAe,iBAC9EtK,KAAA,QAAK4H,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAd,QAAA,eAC9C7K,IAAA,SAAMoI,uBAAuB,CAAE,CAAEC,MAAM,CAAEzI,UAAW,CAAE,CAAE,CAAC,cACzDI,IAAA,QAAK8H,KAAK,CAAE,CAAEvH,KAAK,CAAE,SAAU,CAAE,CAAAsK,QAAA,CAAE5F,SAAS,CAAC,iDAAiD,CAAE,CAAE8B,YAAY,CAAE,iDAAkD,CAAC,CAAC,CAAM,CAAC,EACvK,CACL,cAED/G,IAAA,QACC8H,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAE2B,SAAS,CAAE,MAAO,CAAE,CAC5CjC,SAAS,CAAC,eAAe,CAAAP,QAAA,CAExBT,YAAY,eACZpK,IAAA,QAAK8H,KAAK,CAAE,CAAE6D,MAAM,CAAE,MAAM,CAAEpD,OAAO,CAAE,MAAM,CAAEsD,aAAa,CAAE,QAAS,CAAE,CAAAhB,QAAA,cACxE3K,KAAA,QACC4H,KAAK,CAAE,CACNmE,SAAS,CAAEvB,KAAK,CAAG,OAAO,CAAE,MAAM,CAClCnC,OAAO,CAAE,MAAM,CACfsD,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MACN,CAAE,CAAAjB,QAAA,eAEF7K,IAAA,QACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACf5L,KAAK,CAAE,MAAM,CACbqL,QAAQ,CAAE,QAAQ,CAClBY,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA7B,QAAA,CAEDT,YAAY,CAACE,UAAU,CACpB,CAAC,cAENtK,IAAA,QACCoL,SAAS,CAAC,YAAY,CACtBtD,KAAK,CAAE,CAAEvH,KAAK,CAAE,SAAU,CAAE,CAAAsK,QAAA,CAE3BT,YAAY,CAACG,gBAAgB,CAC1B,CAAC,EACF,CAAC,CACF,CACL,CACI,CAAC,EACD,CAAC,CACH,CAAC,cACNrK,KAAA,QACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfuD,GAAG,CAAE,MAAM,CACXd,UAAU,CAAE,QAAQ,CACpBiD,YAAY,CAAE,KAAK,CACnBc,aAAa,CAAE,MAChB,CAAE,CACF3D,SAAS,CAAC,cAAc,CAAAP,QAAA,eAExB7K,IAAA,WACC8H,KAAK,CAAE,CACNoD,eAAe,EAAA9G,uBAAA,CAAEc,sBAAsB,CAAC,CAAC,CAAC,UAAAd,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BoC,MAAM,UAAAnC,uBAAA,iBAAjCA,uBAAA,CAAmCoC,YAAY,CAChE+E,YAAY,CAAE,MAAM,CACpBQ,OAAO,CAAE,UAAU,CACnBzL,KAAK,CAAE,MAAM,CACb8K,MAAM,CAAE,MAAM,CACd8C,MAAM,CAAE,SACT,CAAE,CAAAtD,QAAA,CAED5F,SAAS,CAAC,WAAW,CAAE,CAAE8B,YAAY,CAAE,WAAY,CAAC,CAAC,CAC/C,CAAC,CACR,CAAAqD,YAAY,SAAZA,YAAY,kBAAA9F,sBAAA,CAAZ8F,YAAY,CAAEI,eAAe,UAAAlG,sBAAA,iBAA7BA,sBAAA,CAA+B+B,MAAM,EAAG,CAAC,eACzCrG,IAAA,WACC8H,KAAK,CAAE,CACN0D,YAAY,CAAE,MAAM,CACpBQ,OAAO,CAAE,UAAU,CACnBzL,KAAK,EAAAgE,uBAAA,CAAEW,sBAAsB,CAAC,CAAC,CAAC,UAAAX,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BiC,MAAM,UAAAhC,uBAAA,iBAAjCA,uBAAA,CAAmCiC,YAAY,CACtD4E,MAAM,CAAE,MAAM,CACd6C,UAAU,CAAE,SAAS,CACrBC,MAAM,CAAE,SACT,CAAE,CACFhD,OAAO,CAAEnB,qBAAsB,CAAAa,QAAA,CAE9B5F,SAAS,CAAC,mBAAmB,CAAE,CAAE8B,YAAY,CAAE,mBAAoB,CAAC,CAAC,CAC/D,CACR,EACG,CAAC,EACF,CAAC,EAEF,CAAC,CACF,CAAC,CACF,CAAC,EACF,CAAC,EACL,CAAC,CAEL,CAAC,CAED,KAAM,CAAAiI,YAAY,CAAGC,KAAA,EAAgF,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IAA/E,CAAEpM,cAAc,CAAEC,WAAuD,CAAC,CAAAkK,KAAA,CAC/F,KAAM,CAAE/J,sBAAsB,CAAEiM,uBAAuB,CAAEC,oBAAoB,CAAEC,cAAe,CAAC,CAAGjS,cAAc,CAC9GkG,KAAU,EAAKA,KACjB,CAAC,CACD,GAAI,CAAAqD,UAAe,CACnB,KAAM,CAACV,KAAK,CAAEC,QAAQ,CAAC,CAAGhJ,QAAQ,CAAQ,CACzC,CACCoH,EAAE,CAAE,CAAC,CACL6B,SAAS,cACRnI,IAAA,SACCoI,uBAAuB,CAAE,CAAEC,MAAM,CAAE1I,OAAQ,CAAE,CAC7CmI,KAAK,CAAE,CAAEQ,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CACpC,CACD,CACDC,QAAQ,CAAE,IACX,CAAC,CACD,CAAC,CACF,KAAM,CAAAC,cAAc,CAAIC,SAAiB,EAAK,CAC7C,MAAO,6BAA6BxH,IAAI,CAACwH,SAAS,CAAC,EAAE,CACtD,CAAC,CACD,KAAM,CAAAQ,SAAS,CAAG,EAAAgG,uBAAA,CAAAhK,sBAAsB,CAAC,CAAC,CAAC,UAAAgK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B/F,QAAQ,UAAAgG,uBAAA,iBAAnCA,uBAAA,CAAqCjG,SAAS,GAAI,MAAM,CAAE;AAC5E,KAAM,CAAAE,eAAe,EAAAgG,uBAAA,CAAGlK,sBAAsB,CAAC,CAAC,CAAC,UAAAkK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BjG,QAAQ,UAAAkG,uBAAA,iBAAnCA,uBAAA,CAAqCvG,IAAI,CAEjE,KAAM,CAAAF,mBAAmB,CAAGX,KAAK,CAACY,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACN,QAAQ,CAAC,CAC/D,GAAII,mBAAmB,CAAE,KAAA0I,sBAAA,CACxB,KAAM,CAAAtI,UAAU,EAAAsI,sBAAA,CAAG1I,mBAAmB,CAACT,SAAS,CAACc,KAAK,CAACb,uBAAuB,UAAAkJ,sBAAA,iBAA3DA,sBAAA,CAA6DjJ,MAAM,CAEtF,GAAIW,UAAU,CAAE,CACfL,UAAU,CAAGF,cAAc,CAACO,UAAU,CAAC,CACxC,CACD,CACA;AACA,KAAM,CAAAuI,YAAY,CAAGlR,cAAc,CAAC+I,eAAe,EAAIT,UAAU,CAAEO,SAAS,CAAC,CAC7E,KAAM,CAACzE,MAAM,CAAE+M,SAAS,CAAC,CAAGtS,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACuS,cAAc,CAAEC,iBAAiB,CAAC,CAAGxS,QAAQ,CAAC,IAAI,CAAC,CAE1D,KAAM,CAAAyS,0BAA0B,CAAIrK,cAAsB,EAAK,CAC9DoK,iBAAiB,CAACpK,cAAc,CAAC,CAClC,CAAC,CAED,KAAM,CAACsK,QAAQ,CAAEC,WAAW,CAAC,CAAG3S,QAAQ,CAAqB0I,QAAQ,CAACgD,IAAI,CAAC,CAC3E,KAAM,CAACkH,WAAW,CAAEC,cAAc,CAAC,CAAG7S,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC8S,kBAAkB,CAAEC,mBAAmB,CAAC,CAAG/S,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACgT,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjT,QAAQ,CAAC,CAAC,CAAC,CAC3D,KAAM,CAAAkT,UAAU,CAAG3K,MAAM,CAAC4K,QAAQ,CAACC,IAAI,CAEvC,mBACCpS,KAAA,QAAA2K,QAAA,eAIC7K,IAAA,QACDoL,SAAS,CAAC,iBAAiB,CAAAP,QAAA,cAEzB3K,KAAA,WACCiL,OAAO,CAAEA,CAAA,GAAM,CACdqG,SAAS,CAAC,IAAI,CAAC,CACfL,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACFrJ,KAAK,CAAE,CACNoD,eAAe,EAAAoE,uBAAA,CAAEpK,sBAAsB,CAAC,CAAC,CAAC,UAAAoK,uBAAA,iBAAzBA,uBAAA,CAA2BnG,QAAQ,CAACoJ,aAAa,CAClEhS,KAAK,CAAE,OAAO,CACdiL,YAAY,CACX,EAAA+D,uBAAA,CAAArK,sBAAsB,CAAC,CAAC,CAAC,UAAAqK,uBAAA,iBAAzBA,uBAAA,CAA2BpG,QAAQ,CAACqJ,IAAI,IAAK,MAAM,EACnD,EAAAhD,uBAAA,CAAAtK,sBAAsB,CAAC,CAAC,CAAC,UAAAsK,uBAAA,iBAAzBA,uBAAA,CAA2BrG,QAAQ,CAACqJ,IAAI,IAAK,UAAU,CACpD,MAAM,CACN,KAAK,CACT7G,MAAM,CAAE,MAAM,CACdD,KAAK,CACJ,EAAA+D,uBAAA,CAAAvK,sBAAsB,CAAC,CAAC,CAAC,UAAAuK,uBAAA,iBAAzBA,uBAAA,CAA2BtG,QAAQ,CAACqJ,IAAI,IAAK,MAAM,EACnD,EAAA9C,uBAAA,CAAAxK,sBAAsB,CAAC,CAAC,CAAC,UAAAwK,uBAAA,iBAAzBA,uBAAA,CAA2BvG,QAAQ,CAACqJ,IAAI,IAAK,UAAU,CACpD,MAAM,CACN,MAAM,CACVjK,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,QAAQ,CACxBd,OAAO,CAAE,KAAK,CACdyG,SAAS,CAAE,yEAAyE,CACpFC,UAAU,CAAE,eAAe,CAC3BrH,MAAM,CAAE,MAAM,CACd8C,MAAM,CAAE,SAAS,CACjBrD,QAAQ,CAAE,UACX,CAAE,CAAAD,QAAA,EAED,EAAA8E,uBAAA,CAAAzK,sBAAsB,CAAC,CAAC,CAAC,UAAAyK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BxG,QAAQ,UAAAyG,uBAAA,iBAAnCA,uBAAA,CAAqC4C,IAAI,IAAK,MAAM,eACpDxS,IAAA,QACCiN,GAAG,CAAEsE,YAAa,CAClBrE,GAAG,CAAC,MAAM,CACVpF,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACzC,CACD,CAGA,EAAAkE,uBAAA,CAAA3K,sBAAsB,CAAC,CAAC,CAAC,UAAA2K,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B1G,QAAQ,UAAA2G,uBAAA,iBAAnCA,uBAAA,CAAqC0C,IAAI,IAAK,MAAM,IAAAzC,uBAAA,CAAI7K,sBAAsB,CAAC,CAAC,CAAC,UAAA6K,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B5G,QAAQ,UAAA6G,uBAAA,iBAAnCA,uBAAA,CAAqC2C,IAAI,gBACjG3S,IAAA,SACC8H,KAAK,CAAE,CACNoE,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MAAM,CAClB5L,KAAK,EAAA0P,uBAAA,CAAE/K,sBAAsB,CAAC,CAAC,CAAC,UAAA+K,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B9G,QAAQ,UAAA+G,uBAAA,iBAAnCA,uBAAA,CAAqC0C,SAAS,CACrD5G,OAAO,CAAE,KAAK,CACdS,UAAU,CAAE,QACb,CAAE,CAAA5B,QAAA,CAED3F,sBAAsB,CAAC,CAAC,CAAC,CAACiE,QAAQ,CAACwJ,IAAI,CACnC,CACN,CAGA,EAAAxC,uBAAA,CAAAjL,sBAAsB,CAAC,CAAC,CAAC,UAAAiL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BhH,QAAQ,UAAAiH,uBAAA,iBAAnCA,uBAAA,CAAqCoC,IAAI,IAAK,UAAU,IAAAnC,uBAAA,CACxDnL,sBAAsB,CAAC,CAAC,CAAC,UAAAmL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BlH,QAAQ,UAAAmH,uBAAA,iBAAnCA,uBAAA,CAAqCqC,IAAI,KAAApC,uBAAA,CACzCrL,sBAAsB,CAAC,CAAC,CAAC,UAAAqL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BpH,QAAQ,UAAAqH,uBAAA,iBAAnCA,uBAAA,CAAqC1H,IAAI,gBACxC5I,KAAA,SACC4H,KAAK,CAAE,CACNS,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpBc,GAAG,CAAE,KAAK,CACVvL,KAAK,EAAAkQ,uBAAA,CAAEvL,sBAAsB,CAAC,CAAC,CAAC,UAAAuL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BtH,QAAQ,UAAAuH,uBAAA,iBAAnCA,uBAAA,CAAqCkC,SAAS,CACrD1G,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MAAM,CAClBH,OAAO,CAAE,KACV,CAAE,CAAAnB,QAAA,eAEF7K,IAAA,QACCiN,GAAG,CAAEsE,YAAa,CAClBrE,GAAG,CAAC,MAAM,CACVpF,KAAK,CAAE,CAAE4D,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACzC,CAAC,EAAAgF,uBAAA,CACDzL,sBAAsB,CAAC,CAAC,CAAC,UAAAyL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BxH,QAAQ,UAAAyH,uBAAA,iBAAnCA,uBAAA,CAAqC+B,IAAI,EACrC,CACN,CAGD,EAAA9B,uBAAA,CAAA3L,sBAAsB,CAAC,CAAC,CAAC,UAAA2L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B1H,QAAQ,UAAA2H,uBAAA,iBAAnCA,uBAAA,CAAqC+B,iBAAiB,gBACtD7S,IAAA,QACC8H,KAAK,CAAE,CACNgD,QAAQ,CAAE,UAAU,CACpBgI,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACb7H,eAAe,EAAA6F,uBAAA,CAAE7L,sBAAsB,CAAC,CAAC,CAAC,UAAA6L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B5H,QAAQ,UAAA6H,uBAAA,iBAAnCA,uBAAA,CAAqCgC,sBAAsB,CAC5EzS,KAAK,EAAA0Q,uBAAA,CAAE/L,sBAAsB,CAAC,CAAC,CAAC,UAAA+L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B9H,QAAQ,UAAA+H,uBAAA,iBAAnCA,uBAAA,CAAqC+B,qBAAqB,CACjE/G,QAAQ,CAAE,MAAM,CAChBV,YAAY,CAAE,QAAQ,CACtBG,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MAAM,CACbnD,OAAO,CAAE,MAAM,CACfyC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,QACjB,CAAE,CAAAjC,QAAA,CAED4G,cAAc,CACX,CACL,EACM,CAAC,CACL,CAAC,CAELL,oBAAoB,eACpBpR,IAAA,CAAAI,SAAA,EAAAyK,QAAA,cACC7K,IAAA,CAACX,gBAAgB,GAAE,CAAC,CACnB,CACF,cAGDW,IAAA,CAACqB,cAAc,EACduD,IAAI,CAAE,EAAG,CACTC,YAAY,CAAE,EAAG,CACjBJ,MAAM,CAAEA,MAAO,CACfC,OAAO,CAAEA,CAAA,GAAM8M,SAAS,CAAC,IAAI,CAAE,CAC/B7M,sBAAsB,CAAEgN,0BAA2B,CACnD7M,cAAc,CAAEuM,cAAe,CAC/BtM,WAAW,CAAEA,WAAY,CACzB,CAAC,EACE,CAAC,CAER,CAAC,CAED,cAAe,CAAAiK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}