<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickAdapt Loader Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #5F9EA0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .button:hover {
            background: #4F8E90;
        }
        .instructions {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #5F9EA0;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 QuickAdapt Popup Loader Test</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Open your browser's Developer Console (F12)</li>
                <li>Click the buttons below to test the loader</li>
                <li>Watch for console messages and the popup in the top-right corner</li>
                <li>The loader should appear for a few seconds then disappear</li>
            </ol>
        </div>

        <div>
            <button class="button" onclick="testOriginalLoader()">Test Original Loader</button>
            <button class="button" onclick="testVisibleLoader()">Test Visible Loader</button>
            <button class="button" onclick="hideLoader()">Hide Loader</button>
            <button class="button" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="console-output" id="console-output">
            Console output will appear here...
        </div>
    </div>

    <script>
        // Capture console logs and display them
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            consoleOutput.innerHTML += message + '<br>';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // Test functions
        function testOriginalLoader() {
            console.log('🧪 Testing original QuickAdapt loader...');
            showQuickAdaptLoader();
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                hideQuickAdaptLoader();
            }, 3000);
        }

        function testVisibleLoader() {
            console.log('🧪 Testing highly visible loader...');
            testQuickAdaptLoader();
        }

        function hideLoader() {
            console.log('🔄 Manually hiding loader...');
            hideQuickAdaptLoader();
        }

        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...<br>';
        }

        // QuickAdapt Loader Functions (copied from content script)
        function showQuickAdaptLoader() {
            console.log('🔄 QuickAdapt: Showing popup loader...');
            
            if (document.getElementById('qadpt-popup-loader')) {
                console.log('⚠️ QuickAdapt: Loader already exists');
                return;
            }

            const loaderHTML = `
                <div id="qadpt-popup-loader" class="qadpt-popup-loader">
                    <div class="qadpt-loader-container">
                        <div class="qadpt-loader-spinner"></div>
                        <div class="qadpt-loader-text">QuickAdapt</div>
                    </div>
                </div>
            `;

            const loaderElement = document.createElement('div');
            loaderElement.innerHTML = loaderHTML;
            const loader = loaderElement.firstElementChild;

            const loaderStyles = `
                .qadpt-popup-loader {
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    z-index: 2147483647 !important;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
                    pointer-events: none !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                .qadpt-loader-container {
                    background-color: #ffffff !important;
                    border: 2px solid #5F9EA0 !important;
                    border-radius: 8px !important;
                    padding: 12px 16px !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
                    display: flex !important;
                    align-items: center !important;
                    gap: 10px !important;
                    min-width: 120px !important;
                    max-width: 200px !important;
                    animation: qadpt-loader-fadeIn 0.5s ease-out !important;
                    transform: translateZ(0) !important;
                }
                .qadpt-loader-spinner {
                    width: 18px !important;
                    height: 18px !important;
                    border: 2px solid #e0e0e0 !important;
                    border-top: 2px solid #5F9EA0 !important;
                    border-radius: 50% !important;
                    animation: qadpt-loader-spin 1s linear infinite !important;
                    flex-shrink: 0 !important;
                    display: block !important;
                }
                .qadpt-loader-text {
                    font-size: 13px !important;
                    font-weight: 600 !important;
                    color: #5F9EA0 !important;
                    white-space: nowrap !important;
                    letter-spacing: 0.5px !important;
                }
                @keyframes qadpt-loader-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                @keyframes qadpt-loader-fadeIn {
                    0% { opacity: 0; transform: translateY(-20px) scale(0.8); }
                    100% { opacity: 1; transform: translateY(0) scale(1); }
                }
                @keyframes qadpt-loader-fadeOut {
                    0% { opacity: 1; transform: translateY(0) scale(1); }
                    100% { opacity: 0; transform: translateY(-20px) scale(0.8); }
                }
                .qadpt-popup-loader.fade-out .qadpt-loader-container {
                    animation: qadpt-loader-fadeOut 0.3s ease-in forwards !important;
                }
            `;

            if (!document.getElementById('qadpt-loader-styles')) {
                const styleElement = document.createElement('style');
                styleElement.id = 'qadpt-loader-styles';
                styleElement.textContent = loaderStyles;
                document.head.appendChild(styleElement);
            }

            document.body.appendChild(loader);
            console.log('✅ QuickAdapt: Popup loader added to page');
        }

        function hideQuickAdaptLoader() {
            console.log('🔄 QuickAdapt: Hiding popup loader...');
            
            const loader = document.getElementById('qadpt-popup-loader');
            if (loader) {
                console.log('✅ QuickAdapt: Found loader, starting fade-out');
                loader.classList.add('fade-out');
                setTimeout(() => {
                    if (loader.parentNode) {
                        loader.parentNode.removeChild(loader);
                        console.log('✅ QuickAdapt: Popup loader removed');
                    }
                    const styles = document.getElementById('qadpt-loader-styles');
                    if (styles && styles.parentNode) {
                        styles.parentNode.removeChild(styles);
                        console.log('✅ QuickAdapt: Loader styles removed');
                    }
                }, 300);
            } else {
                console.log('⚠️ QuickAdapt: No loader found to hide');
            }
        }

        function testQuickAdaptLoader() {
            console.log('🧪 Testing QuickAdapt loader...');
            
            const existing = document.getElementById('qadpt-popup-loader');
            if (existing) existing.remove();
            
            const testLoader = document.createElement('div');
            testLoader.id = 'qadpt-popup-loader';
            testLoader.innerHTML = `
                <div style="
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    z-index: 2147483647 !important;
                    background: #ff4444 !important;
                    color: white !important;
                    padding: 20px !important;
                    border-radius: 10px !important;
                    font-family: Arial, sans-serif !important;
                    font-size: 16px !important;
                    font-weight: bold !important;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.5) !important;
                    border: 3px solid #ffffff !important;
                    min-width: 150px !important;
                    text-align: center !important;
                ">
                    🔄 QuickAdapt Loading...
                </div>
            `;
            
            document.body.appendChild(testLoader);
            console.log('🧪 Test loader created and added to page');
            
            setTimeout(() => {
                if (testLoader.parentNode) {
                    testLoader.parentNode.removeChild(testLoader);
                    console.log('🧪 Test loader removed');
                }
            }, 5000);
        }

        // Initial message
        console.log('🚀 QuickAdapt Loader Test Page Ready');
        console.log('📍 Click the buttons above to test the loader functionality');
    </script>
</body>
</html>
