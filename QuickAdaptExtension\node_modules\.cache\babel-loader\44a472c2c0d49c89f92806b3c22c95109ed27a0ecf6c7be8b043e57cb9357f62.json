{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\LottieSpinner.tsx\";\nimport React from 'react';\nimport { DotLottieReact } from '@lottiefiles/dotlottie-react';\nimport './LottieSpinner.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LottieSpinner = ({\n  size = 60,\n  className = '',\n  backgroundColor = 'transparent'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `lottie-spinner-container ${className}`,\n    style: {\n      width: size,\n      height: size,\n      backgroundColor: backgroundColor,\n      borderRadius: '50%',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: /*#__PURE__*/_jsxDEV(DotLottieReact, {\n      src: \"https://lottie.host/1af42d2f-54f8-4b9f-b8fc-433ef9ab57cc/ZHCCOvYhBx.lottie\",\n      loop: true,\n      autoplay: true,\n      className: \"lottie-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = LottieSpinner;\nexport default LottieSpinner;\nvar _c;\n$RefreshReg$(_c, \"LottieSpinner\");", "map": {"version": 3, "names": ["React", "DotLottieReact", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "size", "className", "backgroundColor", "style", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "children", "src", "loop", "autoplay", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/LottieSpinner.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { DotLottieReact } from '@lottiefiles/dotlottie-react';\r\nimport './LottieSpinner.css';\r\n\r\ninterface LottieSpinnerProps {\r\n  size?: number;\r\n  className?: string;\r\n  backgroundColor?: string;\r\n}\r\n\r\nconst LottieSpinner: React.FC<LottieSpinnerProps> = ({\r\n  size = 60,\r\n  className = '',\r\n  backgroundColor = 'transparent'\r\n}) => {\r\n  return (\r\n    <div\r\n      className={`lottie-spinner-container ${className}`}\r\n      style={{\r\n        width: size,\r\n        height: size,\r\n        backgroundColor: backgroundColor,\r\n        borderRadius: '50%',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center'\r\n      }}\r\n    >\r\n      <DotLottieReact\r\n        src=\"https://lottie.host/1af42d2f-54f8-4b9f-b8fc-433ef9ab57cc/ZHCCOvYhBx.lottie\"\r\n        loop\r\n        autoplay\r\n        className=\"lottie-spinner\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LottieSpinner;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ7B,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,IAAI,GAAG,EAAE;EACTC,SAAS,GAAG,EAAE;EACdC,eAAe,GAAG;AACpB,CAAC,KAAK;EACJ,oBACEJ,OAAA;IACEG,SAAS,EAAE,4BAA4BA,SAAS,EAAG;IACnDE,KAAK,EAAE;MACLC,KAAK,EAAEJ,IAAI;MACXK,MAAM,EAAEL,IAAI;MACZE,eAAe,EAAEA,eAAe;MAChCI,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAC,QAAA,eAEFZ,OAAA,CAACF,cAAc;MACbe,GAAG,EAAC,4EAA4E;MAChFC,IAAI;MACJC,QAAQ;MACRZ,SAAS,EAAC;IAAgB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACC,EAAA,GA1BInB,aAA2C;AA4BjD,eAAeA,aAAa;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}