{"ast": null, "code": "import{adminApiService}from\"./APIService\";export const getLoginUserRoles=async userId=>{try{const response=await adminApiService.get(`/User/GetLoginUserRoles`);// LoginUserRoles \nreturn response.data;}catch(error){console.error(\"Error fetching user roles\",error);throw error;}};export const getRolesByUser=async()=>{try{const response=await adminApiService.get(`/User/GetUserRoles`);return response.data;}catch(error){console.error(\"Error fetching user roles\",error);throw error;}};", "map": {"version": 3, "names": ["adminApiService", "getLoginUserRoles", "userId", "response", "get", "data", "error", "console", "getRolesByUser"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/services/UserRoleService.ts"], "sourcesContent": ["import { adminApiService, userApiService } from \"./APIService\";\r\n\r\nexport const getLoginUserRoles = async (userId:string) => {\r\n    try {\r\n        const response = await adminApiService.get(`/User/GetLoginUserRoles`); // LoginUserRoles \r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching user roles\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const getRolesByUser = async () => {\r\n    try {\r\n        const response = await adminApiService.get(`/User/GetUserRoles`);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error fetching user roles\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,OAASA,eAAe,KAAwB,cAAc,CAE9D,MAAO,MAAM,CAAAC,iBAAiB,CAAG,KAAO,CAAAC,MAAa,EAAK,CACtD,GAAI,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAH,eAAe,CAACI,GAAG,CAAC,yBAAyB,CAAC,CAAE;AACvE,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACxB,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CACf,CACJ,CAAC,CAED,MAAO,MAAM,CAAAE,cAAc,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACA,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAH,eAAe,CAACI,GAAG,CAAC,oBAAoB,CAAC,CAChE,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACxB,CAAE,MAAOC,KAAK,CAAE,CACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CACf,CACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}