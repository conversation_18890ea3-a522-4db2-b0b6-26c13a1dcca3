{"ast": null, "code": "// ScrapingService.ts - Handles targeted click-based scraping functionality\n\nimport i18n from '../multilinguial/i18n';\n\n// Get the translation function directly from i18next\nconst t = i18n.t.bind(i18n);\n\n/**\r\n * Translation keys used in this service:\r\n * \r\n * \"Click Scraping Active\" - Main title for scraping instructions\r\n * \"Click any element to scrape its XPath data\" - Click element instruction\r\n * \"Only the clicked element is scraped no duplicates\" - Only clicked element instruction  \r\n * \"Original click functionality still works\" - Original click functionality instruction\r\n * \"Red borders show scraped elements\" - Red borders instruction\r\n * \"Data is saved to Chrome storage\" - Data saved instruction\r\n * \"Scraped\" - Feedback when element is scraped\r\n * \"Element already scraped click blocked\" - Tooltip for already scraped elements\r\n * \r\n * This service uses the i18n instance directly for translations.\r\n * The translations will automatically fallback to the English keys if translation is missing.\r\n */\n\n// Global state to track if scraping is active\nlet _isScrapingActive = false;\nlet _scrapedData = [];\nlet _lastScrapedTimestamp = '';\nlet _elementMap = new Map(); // Map to track elements by XPath\nlet _clickListener = null;\nlet _highlightedElements = new Set(); // Track highlighted elements\nlet _overlayElements = new Set(); // Track overlay elements for click blocking\n// Interface for element data\n\n// Interface for scraped page data\n\n/**\r\n * Check if scraping is currently active\r\n */\nexport const isScrapingActive = () => {\n  return _isScrapingActive;\n};\n\n/**\r\n * Set the scraping active state\r\n */\nexport const setScrapingActive = active => {\n  _isScrapingActive = active;\n};\n\n/**\r\n * Generate XPath for an element\r\n */\nconst generateXPath = el => {\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';\n  const path = [];\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\n    let index = 1;\n    let sibling = el.previousElementSibling;\n    while (sibling) {\n      if (sibling.tagName === el.tagName) index++;\n      sibling = sibling.previousElementSibling;\n    }\n    path.unshift(`${el.tagName}[${index}]`);\n    el = el.parentElement;\n  }\n  return '/' + path.join('/');\n};\n\n/**\r\n * Generate CSS selector for an element\r\n */\nconst generateCssSelector = el => {\n  if (!el) return '';\n  const path = [];\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\n    let selector = el.tagName.toLowerCase();\n    if (el.id) {\n      selector += `#${el.id}`;\n      path.unshift(selector);\n      break;\n    } else {\n      // Safely handle className - it might be a string or SVGAnimatedString\n      const className = getElementClassName(el);\n      if (className) {\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\n      }\n      path.unshift(selector);\n      el = el.parentElement;\n    }\n  }\n  return path.join(' > ');\n};\n\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\nconst getElementClassName = el => {\n  if (!el) return '';\n\n  // For HTML elements, className is usually a string\n  if (typeof el.className === 'string') {\n    return el.className;\n  }\n\n  // For SVG elements, className might be an SVGAnimatedString\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\n    return el.className.baseVal || '';\n  }\n\n  // Fallback: try to get class attribute directly\n  return el.getAttribute('class') || '';\n};\n\n/**\r\n * Check if element should be ignored for highlighting\r\n */\nconst shouldIgnoreHighlight = element => {\n  return element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || !!element.closest(\"#Tooltip-unique\") || !!element.closest(\"#my-react-drawer\") || !!element.closest(\"#tooltip-section-popover\") || !!element.closest(\"#btn-setting-toolbar\") || !!element.closest(\"#button-toolbar\") || !!element.closest(\"#color-picker\") || !!element.closest(\".qadpt-ext-banner\") || !!element.closest(\"#leftDrawer\") || !!element.closest(\"#image-popover\") || !!element.closest(\"#toggle-fit\") || !!element.closest(\"#color-popover\") || !!element.closest(\"#rte-popover\") || !!element.closest(\"#rte-alignment\") || !!element.closest(\"#rte-alignment-menu\") || !!element.closest(\"#rte-font\") || !!element.closest(\"#rte-bold\") || !!element.closest(\"#rte-italic\") || !!element.closest(\"#rte-underline\") || !!element.closest(\"#rte-strke-through\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#rte-more\") || !!element.closest(\"#rte-text-color\") || !!element.closest(\"#rte-text-color-popover\") || !!element.closest(\"#rte-text-highlight\") || !!element.closest(\"#rte-text-highlight-pop\") || !!element.closest(\"#rte-text-heading\") || !!element.closest(\"#rte-text-heading-menu-items\") || !!element.closest(\"#rte-text-format\") || !!element.closest(\"#rte-text-ul\") || !!element.closest(\"#rte-text-hyperlink\") || !!element.closest(\"#rte-video\") || !!element.closest(\"#rte-clear-formatting\") || !!element.closest(\"#rte-hyperlink-popover\") || !!element.closest(\"#rte-box\") || !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") || !!element.closest(\"#rte-placeholder\") || !!element.closest(\"#qadpt-designpopup\") || !!element.closest(\"#image-properties\") || !!element.closest(\"#rte-toolbar\") || !!element.closest(\"#tooltipdialog\") || !!element.closest(\"#rte-toolbar-paper\") || !!element.closest(\"#stop-scraping-button-container\") || !!element.closest(\"#rte-alignment-menu\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\n  ;\n};\n\n/**\r\n * Check if element should be ignored for events\r\n */\nconst shouldIgnoreEvents = element => {\n  return element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || !!element.closest(\"#Tooltip-unique\") || !!element.closest(\"#tooltip-section-popover\") || !!element.closest(\"#btn-setting-toolbar\") || !!element.closest(\"#button-toolbar\") || !!element.closest(\"#color-picker\") || !!element.closest(\".qadpt-ext-banner\") || !!element.closest(\"#leftDrawer\") || !!element.closest(\"#rte-popover\") || !!element.closest(\"#stop-scraping-button-container\") || !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") || !!element.closest(\"#rte-box\") || !!element.closest(\"#rte-placeholder\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#qadpt-designpopup\") || !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\n  ;\n};\n\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\nconst addPersistentHighlightWithoutBlocking = element => {\n  if (shouldIgnoreHighlight(element)) return;\n\n  // Add persistent red border\n  element.style.outline = '3px solid #ff0000 !important';\n  element.style.outlineOffset = '2px';\n  element.setAttribute('data-quickadapt-highlighted', 'true');\n  _highlightedElements.add(element);\n\n  // No overlay creation - allow clicks to pass through\n};\n\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\nconst addPersistentHighlight = element => {\n  var _element$offsetParent;\n  if (shouldIgnoreHighlight(element)) return;\n\n  // Add persistent red border\n  element.style.outline = '3px solid #ff0000 !important';\n  element.style.outlineOffset = '2px';\n  element.setAttribute('data-quickadapt-highlighted', 'true');\n  _highlightedElements.add(element);\n\n  // Create click-blocking overlay\n  const overlay = document.createElement('div');\n  overlay.style.cssText = `\n    position: absolute;\n    top: ${element.offsetTop}px;\n    left: ${element.offsetLeft}px;\n    width: ${element.offsetWidth}px;\n    height: ${element.offsetHeight}px;\n    background: transparent;\n    z-index: 999999;\n    pointer-events: auto;\n    cursor: not-allowed;\n  `;\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\n  const tooltipText = t('Element already scraped click blocked');\n  overlay.title = tooltipText;\n\n  // Position overlay relative to the element's parent\n  const rect = element.getBoundingClientRect();\n  const parentRect = ((_element$offsetParent = element.offsetParent) === null || _element$offsetParent === void 0 ? void 0 : _element$offsetParent.getBoundingClientRect()) || {\n    top: 0,\n    left: 0\n  };\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\n\n  // Add overlay to the element's parent or body\n  const parent = element.offsetParent || document.body;\n  parent.appendChild(overlay);\n  _overlayElements.add(overlay);\n\n  // Block clicks on the overlay\n  overlay.addEventListener('click', e => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, true);\n};\n\n/**\r\n * Remove all highlights and overlays\r\n */\nconst removeAllHighlights = () => {\n  // Remove highlights\n  _highlightedElements.forEach(element => {\n    if (element && element.style) {\n      element.style.outline = '';\n      element.style.outlineOffset = '';\n      element.removeAttribute('data-quickadapt-highlighted');\n    }\n  });\n  _highlightedElements.clear();\n\n  // Remove overlays\n  _overlayElements.forEach(overlay => {\n    if (overlay && overlay.parentNode) {\n      overlay.parentNode.removeChild(overlay);\n    }\n  });\n  _overlayElements.clear();\n};\n\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\nconst showClickFeedback = element => {\n  try {\n    // Create a temporary feedback indicator\n    const feedback = document.createElement('div');\n    feedback.style.cssText = `\n      position: absolute;\n      background: #4CAF50;\n      color: white;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: bold;\n      z-index: 10001;\n      pointer-events: none;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n      opacity: 0;\n      transition: opacity 0.2s ease;\n    `;\n    const scrapedText = `✓ ${t('Scraped')}`;\n    feedback.textContent = scrapedText;\n\n    // Position the feedback near the clicked element\n    const rect = element.getBoundingClientRect();\n    feedback.style.left = `${rect.left + window.scrollX}px`;\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\n    document.body.appendChild(feedback);\n\n    // Animate in\n    setTimeout(() => {\n      feedback.style.opacity = '1';\n    }, 10);\n\n    // Remove after 2 seconds\n    setTimeout(() => {\n      feedback.style.opacity = '0';\n      setTimeout(() => {\n        if (feedback.parentNode) {\n          feedback.parentNode.removeChild(feedback);\n        }\n      }, 200);\n    }, 2000);\n  } catch (error) {}\n};\n\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\nconst extractElementData = element => {\n  var _element$textContent;\n  const rect = element.getBoundingClientRect();\n  const xpath = generateXPath(element);\n  const cssSelector = generateCssSelector(element);\n  return {\n    tagName: element.tagName,\n    id: element.id || '',\n    className: getElementClassName(element),\n    text: ((_element$textContent = element.textContent) === null || _element$textContent === void 0 ? void 0 : _element$textContent.trim()) || '',\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {}),\n    xpath,\n    cssSelector,\n    rect: {\n      top: rect.top,\n      left: rect.left,\n      width: rect.width,\n      height: rect.height\n    },\n    children: [],\n    // We don't need children for click-based scraping\n    isVisible: rect.width > 0 && rect.height > 0,\n    timestamp: new Date().toISOString(),\n    url: window.location.href // Add URL to each element\n  };\n};\n\n/**\r\n * Handle click events for element scraping\r\n */\nconst handleElementClick = event => {\n  try {\n    // IMPORTANT: Don't prevent default or stop propagation\n    // This allows the original click functionality to work normally\n    // (navigation, form submission, button clicks, etc.)\n\n    const target = event.target;\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\n      return;\n    }\n    if (shouldIgnoreEvents(target)) {\n      return;\n    }\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\n      return;\n    }\n\n    // Extract data from clicked element ONLY\n    const clickedElementData = extractElementData(target);\n\n    // Store only the clicked element data (no parent element)\n    const elementsToStore = [clickedElementData];\n\n    // Add to scraped data\n    setScrapedData({\n      elements: elementsToStore\n    }, true);\n\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\n    addPersistentHighlightWithoutBlocking(target);\n\n    // // Show brief success feedback\n    // showClickFeedback(target);\n  } catch (error) {}\n};\nexport const setScrapedData = (data, append = false) => {\n  const timestamp = new Date().toISOString();\n  _lastScrapedTimestamp = timestamp;\n  if (!append) {\n    // Clear existing data if not appending\n    _scrapedData = [];\n    _elementMap.clear();\n  }\n\n  // Process each element in the data\n  if (data && data.elements && Array.isArray(data.elements)) {\n    data.elements.forEach(element => {\n      // Add timestamp to the element\n      element.timestamp = timestamp;\n\n      // Use XPath as a unique identifier for the element\n      if (element.xpath) {\n        // If element already exists in the map, don't add it again (prevent duplicates)\n        if (_elementMap.has(element.xpath)) {\n          return; // Skip this element\n        } else {\n          // New element, add to map and data array\n          _elementMap.set(element.xpath, element);\n          _scrapedData.push(element);\n        }\n      } else {\n        // No XPath, check for duplicates by other means (tagName + id + className)\n        const isDuplicate = _scrapedData.some(existing => existing.tagName === element.tagName && existing.id === element.id && existing.className === element.className);\n        if (!isDuplicate) {\n          _scrapedData.push(element);\n        } else {}\n      }\n    });\n  }\n};\n\n/**\r\n * Get the currently scraped data\r\n */\nexport const getScrapedData = () => {\n  return _scrapedData;\n};\n\n/**\r\n * Get element by XPath\r\n */\nexport const getElementByXPath = xpath => {\n  return _elementMap.get(xpath);\n};\n\n/**\r\n * Get all xpath data from scraped elements\r\n */\nexport const getXPathData = () => {\n  return _scrapedData.map(element => ({\n    xpath: element.xpath,\n    tagName: element.tagName,\n    id: element.id,\n    className: element.className,\n    text: element.text,\n    timestamp: element.timestamp,\n    url: element.url\n  }));\n};\n\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\nexport const exportScrapedDataToFile = async accountId => {\n  try {\n    if (_scrapedData.length === 0) {\n      // Try to load from storage if no current data\n      const storedData = await loadScrapedDataFromStorage();\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\n        return;\n      }\n      // Use stored data for API call\n      await saveScrapedDataToFile(accountId);\n    } else {\n      // Save current data to storage first, then send to API\n      await saveScrapedDataToStorage();\n      await saveScrapedDataToFile(accountId);\n    }\n  } catch (error) {\n    // alert('Error sending scraped data to backend API. Check console for details.');\n  }\n};\n\n/**\r\n * Get scraped data count\r\n */\nexport const getScrapedDataCount = () => {\n  return _scrapedData.length;\n};\n\n/**\r\n * Get the timestamp of the last scrape\r\n */\nexport const getLastScrapedTimestamp = () => {\n  return _lastScrapedTimestamp;\n};\n\n/**\r\n * Clear scraped data\r\n */\nexport const clearScrapedData = () => {\n  _scrapedData = [];\n  _lastScrapedTimestamp = '';\n};\n\n/**\r\n * Save scraped data to Chrome storage\r\n */\nexport const saveScrapedDataToStorage = async () => {\n  try {\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\n      const storageData = {\n        scrapedData: _scrapedData,\n        timestamp: _lastScrapedTimestamp,\n        url: window.location.href,\n        title: document.title,\n        elementCount: _scrapedData.length,\n        xpathData: _scrapedData.map(element => ({\n          xpath: element.xpath,\n          tagName: element.tagName,\n          id: element.id,\n          className: element.className,\n          text: element.text,\n          attributes: element.attributes,\n          timestamp: element.timestamp,\n          url: element.url\n        }))\n      };\n      await chrome.storage.local.set({\n        'quickadapt-scraped-data': storageData\n      });\n      storageData.xpathData.forEach((item, index) => {\n        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\n      });\n    } else {\n      // Fallback: save to localStorage\n      const storageData = {\n        scrapedData: _scrapedData,\n        timestamp: _lastScrapedTimestamp,\n        url: window.location.href,\n        title: document.title,\n        elementCount: _scrapedData.length,\n        xpathData: _scrapedData.map(element => ({\n          xpath: element.xpath,\n          tagName: element.tagName,\n          id: element.id,\n          className: element.className,\n          text: element.text,\n          attributes: element.attributes,\n          timestamp: element.timestamp,\n          url: element.url\n        }))\n      };\n      localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\n    }\n  } catch (error) {}\n};\n\n/**\r\n * Load scraped data from Chrome storage\r\n */\nexport const loadScrapedDataFromStorage = async () => {\n  try {\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\n      const result = await chrome.storage.local.get('quickadapt-scraped-data');\n      return result['quickadapt-scraped-data'] || null;\n    } else {\n      // Fallback: load from localStorage\n      const data = localStorage.getItem('quickadapt-scraped-data');\n      return data ? JSON.parse(data) : null;\n    }\n  } catch (error) {\n    return null;\n  }\n};\n\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\nexport const saveScrapedDataToFile = async accountId => {\n  try {\n    const storedData = await loadScrapedDataFromStorage();\n    if (!storedData) {\n      return;\n    }\n    const apiData = {\n      metadata: {\n        url: storedData.url || window.location.href,\n        title: storedData.title || document.title,\n        timestamp: storedData.timestamp || new Date().toISOString(),\n        elementCount: storedData.elementCount || 0,\n        exportedAt: new Date().toISOString()\n      },\n      elements: storedData.scrapedData || [],\n      xpathData: storedData.xpathData || []\n    };\n\n    // Send data to backend API\n    await uploadXPathsFile(apiData, accountId);\n  } catch (error) {}\n};\n\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\nexport const uploadXPathsFile = async (data, accountId) => {\n  try {\n    // Convert JSON data to FormData as expected by the existing API\n    const formData = new FormData();\n\n    // Create a JSON file blob\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\n      type: 'application/json'\n    });\n\n    // Generate filename with timestamp\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\n\n    // Add the file to FormData\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\n\n    // Add metadata as form fields if needed\n    formData.append('elementCount', data.metadata.elementCount.toString());\n    formData.append('url', data.metadata.url);\n    formData.append('timestamp', data.metadata.timestamp);\n\n    // Import and use the existing uploadXpathsFile function\n    const {\n      uploadXpathsFile\n    } = await import('./FileService');\n    if (!accountId) {\n      throw new Error('Account ID is required to upload XPath data');\n    }\n    const response = await uploadXpathsFile(accountId, formData);\n  } catch (error) {\n    // Show error message to user\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n    throw error; // Re-throw to be caught by calling function\n  }\n};\n\n/**\r\n * Start click-based scraping process\r\n */\nexport const startScraping = () => {\n  if (_isScrapingActive) return;\n  _isScrapingActive = true;\n  clearScrapedData();\n\n  // Add click event listener to capture element clicks\n  if (!_clickListener) {\n    _clickListener = handleElementClick;\n    document.addEventListener('click', _clickListener, true); // Use capture phase\n  }\n\n  // Send message to content script to enable click-based scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'startClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\n    }\n  }\n\n  // Show user instruction with translation support\n  showScrapingInstructions();\n};\n\n/**\r\n * Stop click-based scraping process\r\n */\nexport const stopScraping = async accountId => {\n  if (!_isScrapingActive) return;\n  _isScrapingActive = false;\n\n  // Remove click event listener\n  if (_clickListener) {\n    document.removeEventListener('click', _clickListener, true);\n    _clickListener = null;\n  }\n\n  // Save scraped data to Chrome storage\n  if (_scrapedData.length > 0) {\n    await saveScrapedDataToStorage();\n\n    // Get data from Chrome storage and save to file\n    await saveScrapedDataToFile(accountId);\n  }\n\n  // Remove all highlights and overlays\n  removeAllHighlights();\n\n  // Send message to background script to stop scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'stopClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\n    }\n  }\n\n  // Hide instructions\n  hideScrapingInstructions();\n};\n\n/**\r\n * Show scraping instructions to user\r\n */\nconst showScrapingInstructions = () => {\n  // Remove existing instruction if any\n  hideScrapingInstructions();\n  const instructionDiv = document.createElement('div');\n  instructionDiv.id = 'quickadapt-scraping-instructions';\n  instructionDiv.style.cssText = `\n    position: fixed;\n    top: 20px;\n    right: 20px;\n    background: #4CAF50;\n    color: white;\n    padding: 15px 20px;\n    border-radius: 8px;\n    font-family: Arial, sans-serif;\n    font-size: 14px;\n    font-weight: bold;\n    z-index: 10000;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    max-width: 320px;\n    text-align: center;\n  `;\n\n  // Use translations with i18n instance directly\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\n  const originalClick = `• ${t('Original click functionality still works')}`;\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\n  instructionDiv.innerHTML = `\n    ${mainTitle}<br>\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\n      ${clickElement}<br>\n      ${onlyClicked}<br>\n      ${originalClick}<br>\n      ${redBorders}<br>\n      ${dataSaved}\n    </small>\n  `;\n  document.body.appendChild(instructionDiv);\n\n  // Auto-hide after 8 seconds\n  setTimeout(() => {\n    if (instructionDiv.parentNode) {\n      instructionDiv.style.opacity = '0.7';\n    }\n  }, 8000);\n};\n\n/**\r\n * Hide scraping instructions\r\n */\nconst hideScrapingInstructions = () => {\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\n  if (existingInstruction) {\n    existingInstruction.remove();\n  }\n};\n\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\nexport const initScrapingService = () => {\n  _isScrapingActive = false;\n  _scrapedData = [];\n  _elementMap.clear();\n  _lastScrapedTimestamp = '';\n  _clickListener = null;\n\n  // Check if we're in a Chrome extension environment\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\n    // Listen for messages from background script\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\n      if (message.action === 'updateScrapingState') {\n        _isScrapingActive = message.isActive;\n        sendResponse({\n          success: true\n        });\n        return true;\n      }\n      if (message.action === 'getScrapingState') {\n        sendResponse({\n          isActive: _isScrapingActive,\n          lastTimestamp: _lastScrapedTimestamp,\n          elementCount: _scrapedData.length\n        });\n        return true;\n      }\n      if (message.action === 'getScrapedData') {\n        sendResponse({\n          data: _scrapedData,\n          timestamp: _lastScrapedTimestamp\n        });\n        return true;\n      }\n      if (message.action === 'clearScrapedData') {\n        clearScrapedData();\n        sendResponse({\n          success: true\n        });\n        return true;\n      }\n    });\n  } else {}\n};\n\n// Initialize the service\ninitScrapingService();", "map": {"version": 3, "names": ["i18n", "t", "bind", "_isScrapingActive", "_scrapedData", "_lastScrapedTimestamp", "_elementMap", "Map", "_clickListener", "_highlightedElements", "Set", "_overlayElements", "isScrapingActive", "setScrapingActive", "active", "generateXPath", "el", "nodeType", "Node", "ELEMENT_NODE", "path", "index", "sibling", "previousElementSibling", "tagName", "unshift", "parentElement", "join", "generateCssSelector", "selector", "toLowerCase", "id", "className", "getElementClassName", "trim", "replace", "baseVal", "getAttribute", "shouldIgnoreHighlight", "element", "classList", "contains", "closest", "startsWith", "shouldIgnoreEvents", "addPersistentHighlightWithoutBlocking", "style", "outline", "outlineOffset", "setAttribute", "add", "addPersistentHighlight", "_element$offsetParent", "overlay", "document", "createElement", "cssText", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "tooltipText", "title", "rect", "getBoundingClientRect", "parentRect", "offsetParent", "top", "left", "window", "scrollY", "scrollX", "parent", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "e", "preventDefault", "stopPropagation", "removeAllHighlights", "for<PERSON>ach", "removeAttribute", "clear", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showClickFeedback", "feedback", "scrapedText", "textContent", "setTimeout", "opacity", "error", "extractElementData", "_element$textContent", "xpath", "cssSelector", "text", "attributes", "Array", "from", "reduce", "acc", "attr", "name", "value", "width", "height", "children", "isVisible", "timestamp", "Date", "toISOString", "url", "location", "href", "handleElementClick", "event", "target", "hasAttribute", "clickedElementData", "elementsToStore", "setScrapedData", "elements", "data", "append", "isArray", "has", "set", "push", "isDuplicate", "some", "existing", "getScrapedData", "getElementByXPath", "get", "getXPathData", "map", "exportScrapedDataToFile", "accountId", "length", "storedData", "loadScrapedDataFromStorage", "scrapedData", "saveScrapedDataToFile", "saveScrapedDataToStorage", "getScrapedDataCount", "getLastScrapedTimestamp", "clearScrapedData", "chrome", "storage", "local", "storageData", "elementCount", "xpathData", "item", "console", "log", "localStorage", "setItem", "JSON", "stringify", "result", "getItem", "parse", "apiData", "metadata", "exportedAt", "uploadXPathsFile", "formData", "FormData", "jsonBlob", "Blob", "type", "pageTitle", "filename", "toString", "uploadXpathsFile", "Error", "response", "errorMessage", "message", "startScraping", "runtime", "sendMessage", "action", "dispatchEvent", "CustomEvent", "showScrapingInstructions", "stopScraping", "removeEventListener", "hideScrapingInstructions", "instructionDiv", "mainTitle", "clickElement", "onlyClicked", "originalClick", "redBorders", "dataSaved", "innerHTML", "existingInstruction", "getElementById", "remove", "initScrapingService", "onMessage", "addListener", "_sender", "sendResponse", "isActive", "success", "lastTimestamp"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/services/ScrapingService.ts"], "sourcesContent": ["// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality\r\n\r\nimport i18n from '../multilinguial/i18n';\r\n\r\n// Get the translation function directly from i18next\r\nconst t = i18n.t.bind(i18n);\r\n\r\n/**\r\n * Translation keys used in this service:\r\n * \r\n * \"Click Scraping Active\" - Main title for scraping instructions\r\n * \"Click any element to scrape its XPath data\" - Click element instruction\r\n * \"Only the clicked element is scraped no duplicates\" - Only clicked element instruction  \r\n * \"Original click functionality still works\" - Original click functionality instruction\r\n * \"Red borders show scraped elements\" - Red borders instruction\r\n * \"Data is saved to Chrome storage\" - Data saved instruction\r\n * \"Scraped\" - Feedback when element is scraped\r\n * \"Element already scraped click blocked\" - Tooltip for already scraped elements\r\n * \r\n * This service uses the i18n instance directly for translations.\r\n * The translations will automatically fallback to the English keys if translation is missing.\r\n */\r\n\r\n// Global state to track if scraping is active\r\nlet _isScrapingActive = false;\r\nlet _scrapedData: any[] = [];\r\nlet _lastScrapedTimestamp: string = '';\r\nlet _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath\r\nlet _clickListener: ((event: MouseEvent) => void) | null = null;\r\nlet _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements\r\nlet _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking\r\n// Interface for element data\r\nexport interface ElementData {\r\n  tagName: string;\r\n  id: string;\r\n  className: string;\r\n  text: string;\r\n  attributes: Record<string, string>;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  rect: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n  children: ElementData[];\r\n  isVisible: boolean;\r\n  timestamp: string;\r\n  url: string; // Add URL field to track which page the element is from\r\n}\r\n\r\n// Interface for scraped page data\r\nexport interface ScrapedPageData {\r\n  url: string;\r\n  title: string;\r\n  timestamp: string;\r\n  elements: ElementData[];\r\n}\r\n\r\n/**\r\n * Check if scraping is currently active\r\n */\r\nexport const isScrapingActive = (): boolean => {\r\n  return _isScrapingActive;\r\n};\r\n\r\n/**\r\n * Set the scraping active state\r\n */\r\nexport const setScrapingActive = (active: boolean): void => {\r\n  _isScrapingActive = active;\r\n};\r\n\r\n/**\r\n * Generate XPath for an element\r\n */\r\nconst generateXPath = (el: HTMLElement): string => {\r\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let index = 1;\r\n    let sibling = el.previousElementSibling;\r\n    while (sibling) {\r\n      if (sibling.tagName === el.tagName) index++;\r\n      sibling = sibling.previousElementSibling;\r\n    }\r\n    path.unshift(`${el.tagName}[${index}]`);\r\n    el = el.parentElement!;\r\n  }\r\n  return '/' + path.join('/');\r\n};\r\n\r\n/**\r\n * Generate CSS selector for an element\r\n */\r\nconst generateCssSelector = (el: HTMLElement): string => {\r\n  if (!el) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = el.tagName.toLowerCase();\r\n    if (el.id) {\r\n      selector += `#${el.id}`;\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      // Safely handle className - it might be a string or SVGAnimatedString\r\n      const className = getElementClassName(el);\r\n      if (className) {\r\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\r\n      }\r\n      path.unshift(selector);\r\n      el = el.parentElement!;\r\n    }\r\n  }\r\n  return path.join(' > ');\r\n};\r\n\r\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\r\nconst getElementClassName = (el: Element): string => {\r\n  if (!el) return '';\r\n\r\n  // For HTML elements, className is usually a string\r\n  if (typeof el.className === 'string') {\r\n    return el.className;\r\n  }\r\n\r\n  // For SVG elements, className might be an SVGAnimatedString\r\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\r\n    return (el.className as any).baseVal || '';\r\n  }\r\n\r\n  // Fallback: try to get class attribute directly\r\n  return el.getAttribute('class') || '';\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for highlighting\r\n */\r\nconst shouldIgnoreHighlight = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#my-react-drawer\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#image-popover\") ||\r\n    !!element.closest(\"#toggle-fit\") ||\r\n    !!element.closest(\"#color-popover\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#rte-alignment\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-font\") ||\r\n    !!element.closest(\"#rte-bold\") ||\r\n    !!element.closest(\"#rte-italic\") ||\r\n    !!element.closest(\"#rte-underline\") ||\r\n    !!element.closest(\"#rte-strke-through\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#rte-more\") ||\r\n    !!element.closest(\"#rte-text-color\") ||\r\n    !!element.closest(\"#rte-text-color-popover\") ||\r\n    !!element.closest(\"#rte-text-highlight\") ||\r\n    !!element.closest(\"#rte-text-highlight-pop\") ||\r\n    !!element.closest(\"#rte-text-heading\") ||\r\n    !!element.closest(\"#rte-text-heading-menu-items\") ||\r\n    !!element.closest(\"#rte-text-format\") ||\r\n    !!element.closest(\"#rte-text-ul\") ||\r\n    !!element.closest(\"#rte-text-hyperlink\") ||\r\n    !!element.closest(\"#rte-video\") ||\r\n    !!element.closest(\"#rte-clear-formatting\") ||\r\n    !!element.closest(\"#rte-hyperlink-popover\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#image-properties\") ||\r\n    !!element.closest(\"#rte-toolbar\") ||\r\n    !!element.closest(\"#tooltipdialog\") ||\r\n    !!element.closest(\"#rte-toolbar-paper\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\r\n  );\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for events\r\n */\r\nconst shouldIgnoreEvents = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\r\n  );\r\n};\r\n\r\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\r\nconst addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // No overlay creation - allow clicks to pass through\r\n};\r\n\r\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\r\nconst addPersistentHighlight = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // Create click-blocking overlay\r\n  const overlay = document.createElement('div');\r\n  overlay.style.cssText = `\r\n    position: absolute;\r\n    top: ${element.offsetTop}px;\r\n    left: ${element.offsetLeft}px;\r\n    width: ${element.offsetWidth}px;\r\n    height: ${element.offsetHeight}px;\r\n    background: transparent;\r\n    z-index: 999999;\r\n    pointer-events: auto;\r\n    cursor: not-allowed;\r\n  `;\r\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\r\n  \r\n  const tooltipText = t('Element already scraped click blocked');\r\n  overlay.title = tooltipText;\r\n\r\n  // Position overlay relative to the element's parent\r\n  const rect = element.getBoundingClientRect();\r\n  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };\r\n\r\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\r\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\r\n\r\n  // Add overlay to the element's parent or body\r\n  const parent = element.offsetParent || document.body;\r\n  parent.appendChild(overlay);\r\n  _overlayElements.add(overlay);\r\n\r\n  // Block clicks on the overlay\r\n  overlay.addEventListener('click', (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  }, true);\r\n};\r\n\r\n/**\r\n * Remove all highlights and overlays\r\n */\r\nconst removeAllHighlights = (): void => {\r\n  // Remove highlights\r\n  _highlightedElements.forEach(element => {\r\n    if (element && element.style) {\r\n      element.style.outline = '';\r\n      element.style.outlineOffset = '';\r\n      element.removeAttribute('data-quickadapt-highlighted');\r\n    }\r\n  });\r\n  _highlightedElements.clear();\r\n\r\n  // Remove overlays\r\n  _overlayElements.forEach(overlay => {\r\n    if (overlay && overlay.parentNode) {\r\n      overlay.parentNode.removeChild(overlay);\r\n    }\r\n  });\r\n  _overlayElements.clear();\r\n};\r\n\r\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\r\nconst showClickFeedback = (element: HTMLElement): void => {\r\n  try {\r\n    // Create a temporary feedback indicator\r\n    const feedback = document.createElement('div');\r\n    feedback.style.cssText = `\r\n      position: absolute;\r\n      background: #4CAF50;\r\n      color: white;\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      font-weight: bold;\r\n      z-index: 10001;\r\n      pointer-events: none;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\r\n      opacity: 0;\r\n      transition: opacity 0.2s ease;\r\n    `;\r\n    \r\n    const scrapedText = `✓ ${t('Scraped')}`;\r\n    feedback.textContent = scrapedText;\r\n\r\n    // Position the feedback near the clicked element\r\n    const rect = element.getBoundingClientRect();\r\n    feedback.style.left = `${rect.left + window.scrollX}px`;\r\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\r\n\r\n    document.body.appendChild(feedback);\r\n\r\n    // Animate in\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '1';\r\n    }, 10);\r\n\r\n    // Remove after 2 seconds\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '0';\r\n      setTimeout(() => {\r\n        if (feedback.parentNode) {\r\n          feedback.parentNode.removeChild(feedback);\r\n        }\r\n      }, 200);\r\n    }, 2000);\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\r\nconst extractElementData = (element: HTMLElement): ElementData => {\r\n  const rect = element.getBoundingClientRect();\r\n  const xpath = generateXPath(element);\r\n  const cssSelector = generateCssSelector(element);\r\n\r\n  return {\r\n    tagName: element.tagName,\r\n    id: element.id || '',\r\n    className: getElementClassName(element),\r\n    text: element.textContent?.trim() || '',\r\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\r\n      acc[attr.name] = attr.value;\r\n      return acc;\r\n    }, {} as Record<string, string>),\r\n    xpath,\r\n    cssSelector,\r\n    rect: {\r\n      top: rect.top,\r\n      left: rect.left,\r\n      width: rect.width,\r\n      height: rect.height\r\n    },\r\n    children: [], // We don't need children for click-based scraping\r\n    isVisible: rect.width > 0 && rect.height > 0,\r\n    timestamp: new Date().toISOString(),\r\n    url: window.location.href // Add URL to each element\r\n  };\r\n};\r\n\r\n/**\r\n * Handle click events for element scraping\r\n */\r\nconst handleElementClick = (event: MouseEvent): void => {\r\n  try {\r\n    // IMPORTANT: Don't prevent default or stop propagation\r\n    // This allows the original click functionality to work normally\r\n    // (navigation, form submission, button clicks, etc.)\r\n\r\n    const target = event.target as HTMLElement;\r\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\r\n      return;\r\n    }\r\n\r\n    if (shouldIgnoreEvents(target)) {\r\n      return;\r\n    }\r\n\r\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\r\n      return; \r\n    }\r\n\r\n\r\n    // Extract data from clicked element ONLY\r\n    const clickedElementData = extractElementData(target);\r\n\r\n    // Store only the clicked element data (no parent element)\r\n    const elementsToStore = [clickedElementData];\r\n\r\n    // Add to scraped data\r\n    setScrapedData({ elements: elementsToStore }, true);\r\n\r\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\r\n    addPersistentHighlightWithoutBlocking(target);\r\n\r\n\r\n    // // Show brief success feedback\r\n    // showClickFeedback(target);\r\n\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\nexport const setScrapedData = (data: any, append: boolean = false): void => {\r\n  const timestamp = new Date().toISOString();\r\n  _lastScrapedTimestamp = timestamp;\r\n\r\n  if (!append) {\r\n    // Clear existing data if not appending\r\n    _scrapedData = [];\r\n    _elementMap.clear();\r\n  }\r\n\r\n  // Process each element in the data\r\n  if (data && data.elements && Array.isArray(data.elements)) {\r\n    data.elements.forEach((element: ElementData) => {\r\n      // Add timestamp to the element\r\n      element.timestamp = timestamp;\r\n\r\n      // Use XPath as a unique identifier for the element\r\n      if (element.xpath) {\r\n        // If element already exists in the map, don't add it again (prevent duplicates)\r\n        if (_elementMap.has(element.xpath)) {\r\n          return; // Skip this element\r\n        } else {\r\n          // New element, add to map and data array\r\n          _elementMap.set(element.xpath, element);\r\n          _scrapedData.push(element);\r\n        \r\n        }\r\n      } else {\r\n        // No XPath, check for duplicates by other means (tagName + id + className)\r\n        const isDuplicate = _scrapedData.some(existing =>\r\n          existing.tagName === element.tagName &&\r\n          existing.id === element.id &&\r\n          existing.className === element.className\r\n        );\r\n\r\n        if (!isDuplicate) {\r\n          _scrapedData.push(element);\r\n\r\n        } else {\r\n\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Get the currently scraped data\r\n */\r\nexport const getScrapedData = (): any[] => {\r\n  return _scrapedData;\r\n};\r\n\r\n/**\r\n * Get element by XPath\r\n */\r\nexport const getElementByXPath = (xpath: string): ElementData | undefined => {\r\n  return _elementMap.get(xpath);\r\n};\r\n\r\n\r\n\r\n/**\r\n * Get all xpath data from scraped elements\r\n */\r\nexport const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {\r\n  return _scrapedData.map(element => ({\r\n    xpath: element.xpath,\r\n    tagName: element.tagName,\r\n    id: element.id,\r\n    className: element.className,\r\n    text: element.text,\r\n    timestamp: element.timestamp,\r\n    url: element.url\r\n  }));\r\n};\r\n\r\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\r\nexport const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n    if (_scrapedData.length === 0) {\r\n      // Try to load from storage if no current data\r\n      const storedData = await loadScrapedDataFromStorage();\r\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\r\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\r\n        return;\r\n      }\r\n      // Use stored data for API call\r\n      await saveScrapedDataToFile(accountId);\r\n    } else {\r\n      // Save current data to storage first, then send to API\r\n      await saveScrapedDataToStorage();\r\n      await saveScrapedDataToFile(accountId);\r\n    }\r\n  } catch (error) {\r\n    // alert('Error sending scraped data to backend API. Check console for details.');\r\n  }\r\n};\r\n\r\n/**\r\n * Get scraped data count\r\n */\r\nexport const getScrapedDataCount = (): number => {\r\n  return _scrapedData.length;\r\n};\r\n\r\n/**\r\n * Get the timestamp of the last scrape\r\n */\r\nexport const getLastScrapedTimestamp = (): string => {\r\n  return _lastScrapedTimestamp;\r\n};\r\n\r\n/**\r\n * Clear scraped data\r\n */\r\nexport const clearScrapedData = (): void => {\r\n  _scrapedData = [];\r\n  _lastScrapedTimestamp = '';\r\n};\r\n\r\n/**\r\n * Save scraped data to Chrome storage\r\n */\r\nexport const saveScrapedDataToStorage = async (): Promise<void> => {\r\n  try {\r\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n        }))\r\n      };\r\n\r\n      await chrome.storage.local.set({\r\n        'quickadapt-scraped-data': storageData\r\n      });\r\n   storageData.xpathData.forEach((item, index) => {\r\n        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\r\n      });\r\n    } else {\r\n      // Fallback: save to localStorage\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n        }))\r\n      };\r\n      localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\r\n    }\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Load scraped data from Chrome storage\r\n */\r\nexport const loadScrapedDataFromStorage = async (): Promise<any> => {\r\n  try {\r\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\r\n      const result = await chrome.storage.local.get('quickadapt-scraped-data');\r\n      return result['quickadapt-scraped-data'] || null;\r\n    } else {\r\n      // Fallback: load from localStorage\r\n      const data = localStorage.getItem('quickadapt-scraped-data');\r\n      return data ? JSON.parse(data) : null;\r\n    }\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\r\nexport const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    const storedData = await loadScrapedDataFromStorage();\r\n\r\n    if (!storedData) {\r\n      return;\r\n    }\r\n\r\n    const apiData = {\r\n      metadata: {\r\n        url: storedData.url || window.location.href,\r\n        title: storedData.title || document.title,\r\n        timestamp: storedData.timestamp || new Date().toISOString(),\r\n        elementCount: storedData.elementCount || 0,\r\n        exportedAt: new Date().toISOString()\r\n      },\r\n      elements: storedData.scrapedData || [],\r\n      xpathData: storedData.xpathData || []\r\n    };\r\n\r\n\r\n    // Send data to backend API\r\n    await uploadXPathsFile(apiData, accountId);\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\r\nexport const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    // Convert JSON data to FormData as expected by the existing API\r\n    const formData = new FormData();\r\n\r\n    // Create a JSON file blob\r\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\r\n      type: 'application/json'\r\n    });\r\n\r\n    // Generate filename with timestamp\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\r\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\r\n\r\n    // Add the file to FormData\r\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\r\n\r\n    // Add metadata as form fields if needed\r\n    formData.append('elementCount', data.metadata.elementCount.toString());\r\n    formData.append('url', data.metadata.url);\r\n    formData.append('timestamp', data.metadata.timestamp);\r\n\r\n   \r\n\r\n    // Import and use the existing uploadXpathsFile function\r\n    const { uploadXpathsFile } = await import('./FileService');\r\n\r\n    if (!accountId) {\r\n      throw new Error('Account ID is required to upload XPath data');\r\n    }\r\n\r\n    const response = await uploadXpathsFile(accountId, formData);\r\n\r\n  } catch (error) {\r\n\r\n    // Show error message to user\r\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\r\n\r\n    throw error; // Re-throw to be caught by calling function\r\n  }\r\n};\r\n\r\n/**\r\n * Start click-based scraping process\r\n */\r\nexport const startScraping = (): void => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  clearScrapedData();\r\n\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = handleElementClick;\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Show user instruction with translation support\r\n  showScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Stop click-based scraping process\r\n */\r\nexport const stopScraping = async (accountId: string): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n\r\n  _isScrapingActive = false;\r\n\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Save scraped data to Chrome storage\r\n  if (_scrapedData.length > 0) {\r\n    await saveScrapedDataToStorage();\r\n\r\n    // Get data from Chrome storage and save to file\r\n    await saveScrapedDataToFile(accountId);\r\n  }\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Show scraping instructions to user\r\n */\r\nconst showScrapingInstructions = (): void => {\r\n  // Remove existing instruction if any\r\n  hideScrapingInstructions();\r\n\r\n  const instructionDiv = document.createElement('div');\r\n  instructionDiv.id = 'quickadapt-scraping-instructions';\r\n  instructionDiv.style.cssText = `\r\n    position: fixed;\r\n    top: 20px;\r\n    right: 20px;\r\n    background: #4CAF50;\r\n    color: white;\r\n    padding: 15px 20px;\r\n    border-radius: 8px;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 14px;\r\n    font-weight: bold;\r\n    z-index: 10000;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n    max-width: 320px;\r\n    text-align: center;\r\n  `;\r\n  \r\n  // Use translations with i18n instance directly\r\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\r\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\r\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\r\n  const originalClick = `• ${t('Original click functionality still works')}`;\r\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\r\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\r\n  \r\n  instructionDiv.innerHTML = `\r\n    ${mainTitle}<br>\r\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\r\n      ${clickElement}<br>\r\n      ${onlyClicked}<br>\r\n      ${originalClick}<br>\r\n      ${redBorders}<br>\r\n      ${dataSaved}\r\n    </small>\r\n  `;\r\n\r\n  document.body.appendChild(instructionDiv);\r\n\r\n  // Auto-hide after 8 seconds\r\n  setTimeout(() => {\r\n    if (instructionDiv.parentNode) {\r\n      instructionDiv.style.opacity = '0.7';\r\n    }\r\n  }, 8000);\r\n};\r\n\r\n/**\r\n * Hide scraping instructions\r\n */\r\nconst hideScrapingInstructions = (): void => {\r\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\r\n  if (existingInstruction) {\r\n    existingInstruction.remove();\r\n  }\r\n};\r\n\r\n\r\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\r\nexport const initScrapingService = (): void => {\r\n  _isScrapingActive = false;\r\n  _scrapedData = [];\r\n  _elementMap.clear();\r\n  _lastScrapedTimestamp = '';\r\n  _clickListener = null;\r\n\r\n  // Check if we're in a Chrome extension environment\r\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\r\n    // Listen for messages from background script\r\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\r\n      if (message.action === 'updateScrapingState') {\r\n        _isScrapingActive = message.isActive;\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapingState') {\r\n        sendResponse({\r\n          isActive: _isScrapingActive,\r\n          lastTimestamp: _lastScrapedTimestamp,\r\n          elementCount: _scrapedData.length\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapedData') {\r\n        sendResponse({\r\n          data: _scrapedData,\r\n          timestamp: _lastScrapedTimestamp\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'clearScrapedData') {\r\n        clearScrapedData();\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n    });\r\n  } else {\r\n  }\r\n};\r\n\r\n\r\n// Initialize the service\r\ninitScrapingService();\r\n"], "mappings": "AAAA;;AAEA,OAAOA,IAAI,MAAM,uBAAuB;;AAExC;AACA,MAAMC,CAAC,GAAGD,IAAI,CAACC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIG,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,YAAmB,GAAG,EAAE;AAC5B,IAAIC,qBAA6B,GAAG,EAAE;AACtC,IAAIC,WAAqC,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,IAAIC,cAAoD,GAAG,IAAI;AAC/D,IAAIC,oBAAsC,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,IAAIC,gBAAkC,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;AACpD;;AAqBA;;AAQA;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAAA,KAAe;EAC7C,OAAOT,iBAAiB;AAC1B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,iBAAiB,GAAIC,MAAe,IAAW;EAC1DX,iBAAiB,GAAGW,MAAM;AAC5B,CAAC;;AAED;AACA;AACA;AACA,MAAMC,aAAa,GAAIC,EAAe,IAAa;EACjD,IAAI,CAACA,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE,OAAO,EAAE;EACvD,MAAMC,IAAI,GAAG,EAAE;EACf,OAAOJ,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;IAC9C,IAAIE,KAAK,GAAG,CAAC;IACb,IAAIC,OAAO,GAAGN,EAAE,CAACO,sBAAsB;IACvC,OAAOD,OAAO,EAAE;MACd,IAAIA,OAAO,CAACE,OAAO,KAAKR,EAAE,CAACQ,OAAO,EAAEH,KAAK,EAAE;MAC3CC,OAAO,GAAGA,OAAO,CAACC,sBAAsB;IAC1C;IACAH,IAAI,CAACK,OAAO,CAAC,GAAGT,EAAE,CAACQ,OAAO,IAAIH,KAAK,GAAG,CAAC;IACvCL,EAAE,GAAGA,EAAE,CAACU,aAAc;EACxB;EACA,OAAO,GAAG,GAAGN,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA,MAAMC,mBAAmB,GAAIZ,EAAe,IAAa;EACvD,IAAI,CAACA,EAAE,EAAE,OAAO,EAAE;EAClB,MAAMI,IAAI,GAAG,EAAE;EACf,OAAOJ,EAAE,IAAIA,EAAE,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;IAC9C,IAAIU,QAAQ,GAAGb,EAAE,CAACQ,OAAO,CAACM,WAAW,CAAC,CAAC;IACvC,IAAId,EAAE,CAACe,EAAE,EAAE;MACTF,QAAQ,IAAI,IAAIb,EAAE,CAACe,EAAE,EAAE;MACvBX,IAAI,CAACK,OAAO,CAACI,QAAQ,CAAC;MACtB;IACF,CAAC,MAAM;MACL;MACA,MAAMG,SAAS,GAAGC,mBAAmB,CAACjB,EAAE,CAAC;MACzC,IAAIgB,SAAS,EAAE;QACbH,QAAQ,IAAI,GAAG,GAAGG,SAAS,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACzD;MACAf,IAAI,CAACK,OAAO,CAACI,QAAQ,CAAC;MACtBb,EAAE,GAAGA,EAAE,CAACU,aAAc;IACxB;EACF;EACA,OAAON,IAAI,CAACO,IAAI,CAAC,KAAK,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMM,mBAAmB,GAAIjB,EAAW,IAAa;EACnD,IAAI,CAACA,EAAE,EAAE,OAAO,EAAE;;EAElB;EACA,IAAI,OAAOA,EAAE,CAACgB,SAAS,KAAK,QAAQ,EAAE;IACpC,OAAOhB,EAAE,CAACgB,SAAS;EACrB;;EAEA;EACA,IAAIhB,EAAE,CAACgB,SAAS,IAAI,OAAOhB,EAAE,CAACgB,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAIhB,EAAE,CAACgB,SAAS,EAAE;IACjF,OAAQhB,EAAE,CAACgB,SAAS,CAASI,OAAO,IAAI,EAAE;EAC5C;;EAEA;EACA,OAAOpB,EAAE,CAACqB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;AACvC,CAAC;;AAED;AACA;AACA;AACA,MAAMC,qBAAqB,GAAIC,OAAoB,IAAc;EAC/D,OACEA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5DF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzCF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjDF,OAAO,CAACF,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C,CAAC,CAACE,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,0BAA0B,CAAC,IAC7C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,sBAAsB,CAAC,IACzC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,eAAe,CAAC,IAClC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,yBAAyB,CAAC,IAC5C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,yBAAyB,CAAC,IAC5C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,8BAA8B,CAAC,IACjD,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,YAAY,CAAC,IAC/B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,uBAAuB,CAAC,IAC1C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,wBAAwB,CAAC,IAC3C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,UAAU,CAAC,IAC7B,CAAC,CAACH,OAAO,CAACG,OAAO,CAACH,OAAO,CAACR,EAAE,CAACY,UAAU,CAAC,WAAW,CAAC,GAAG,IAAIJ,OAAO,CAACR,EAAE,EAAE,GAAG,MAAM,CAAC,IACjF,CAAC,CAACQ,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iCAAiC,CAAC,IACpD,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mCAAmC,CAAC,CAAC;EAAA;AAE3D,CAAC;;AAED;AACA;AACA;AACA,MAAME,kBAAkB,GAAIL,OAAoB,IAAc;EAC5D,OACEA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5DF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzCF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjDF,OAAO,CAACF,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C,CAAC,CAACE,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,0BAA0B,CAAC,IAC7C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,sBAAsB,CAAC,IACzC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,eAAe,CAAC,IAClC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iCAAiC,CAAC,IACpD,CAAC,CAACH,OAAO,CAACG,OAAO,CAACH,OAAO,CAACR,EAAE,CAACY,UAAU,CAAC,WAAW,CAAC,GAAG,IAAIJ,OAAO,CAACR,EAAE,EAAE,GAAG,MAAM,CAAC,IACjF,CAAC,CAACQ,OAAO,CAACG,OAAO,CAAC,UAAU,CAAC,IAC7B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mCAAmC,CAAC,CAAC;EAAA;AAE3D,CAAC;;AAED;AACA;AACA;AACA,MAAMG,qCAAqC,GAAIN,OAAoB,IAAW;EAC5E,IAAID,qBAAqB,CAACC,OAAO,CAAC,EAAE;;EAEpC;EACAA,OAAO,CAACO,KAAK,CAACC,OAAO,GAAG,8BAA8B;EACtDR,OAAO,CAACO,KAAK,CAACE,aAAa,GAAG,KAAK;EACnCT,OAAO,CAACU,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;EAC3DxC,oBAAoB,CAACyC,GAAG,CAACX,OAAO,CAAC;;EAEjC;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMY,sBAAsB,GAAIZ,OAAoB,IAAW;EAAA,IAAAa,qBAAA;EAC7D,IAAId,qBAAqB,CAACC,OAAO,CAAC,EAAE;;EAEpC;EACAA,OAAO,CAACO,KAAK,CAACC,OAAO,GAAG,8BAA8B;EACtDR,OAAO,CAACO,KAAK,CAACE,aAAa,GAAG,KAAK;EACnCT,OAAO,CAACU,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;EAC3DxC,oBAAoB,CAACyC,GAAG,CAACX,OAAO,CAAC;;EAEjC;EACA,MAAMc,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC7CF,OAAO,CAACP,KAAK,CAACU,OAAO,GAAG;AAC1B;AACA,WAAWjB,OAAO,CAACkB,SAAS;AAC5B,YAAYlB,OAAO,CAACmB,UAAU;AAC9B,aAAanB,OAAO,CAACoB,WAAW;AAChC,cAAcpB,OAAO,CAACqB,YAAY;AAClC;AACA;AACA;AACA;AACA,GAAG;EACDP,OAAO,CAACJ,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;EAEvD,MAAMY,WAAW,GAAG5D,CAAC,CAAC,uCAAuC,CAAC;EAC9DoD,OAAO,CAACS,KAAK,GAAGD,WAAW;;EAE3B;EACA,MAAME,IAAI,GAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC;EAC5C,MAAMC,UAAU,GAAG,EAAAb,qBAAA,GAAAb,OAAO,CAAC2B,YAAY,cAAAd,qBAAA,uBAApBA,qBAAA,CAAsBY,qBAAqB,CAAC,CAAC,KAAI;IAAEG,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAE,CAAC;EAEvFf,OAAO,CAACP,KAAK,CAACqB,GAAG,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAGF,UAAU,CAACE,GAAG,GAAGE,MAAM,CAACC,OAAO,IAAI;EACrEjB,OAAO,CAACP,KAAK,CAACsB,IAAI,GAAG,GAAGL,IAAI,CAACK,IAAI,GAAGH,UAAU,CAACG,IAAI,GAAGC,MAAM,CAACE,OAAO,IAAI;;EAExE;EACA,MAAMC,MAAM,GAAGjC,OAAO,CAAC2B,YAAY,IAAIZ,QAAQ,CAACmB,IAAI;EACpDD,MAAM,CAACE,WAAW,CAACrB,OAAO,CAAC;EAC3B1C,gBAAgB,CAACuC,GAAG,CAACG,OAAO,CAAC;;EAE7B;EACAA,OAAO,CAACsB,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAY;EACtC;EACAtE,oBAAoB,CAACuE,OAAO,CAACzC,OAAO,IAAI;IACtC,IAAIA,OAAO,IAAIA,OAAO,CAACO,KAAK,EAAE;MAC5BP,OAAO,CAACO,KAAK,CAACC,OAAO,GAAG,EAAE;MAC1BR,OAAO,CAACO,KAAK,CAACE,aAAa,GAAG,EAAE;MAChCT,OAAO,CAAC0C,eAAe,CAAC,6BAA6B,CAAC;IACxD;EACF,CAAC,CAAC;EACFxE,oBAAoB,CAACyE,KAAK,CAAC,CAAC;;EAE5B;EACAvE,gBAAgB,CAACqE,OAAO,CAAC3B,OAAO,IAAI;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC8B,UAAU,EAAE;MACjC9B,OAAO,CAAC8B,UAAU,CAACC,WAAW,CAAC/B,OAAO,CAAC;IACzC;EACF,CAAC,CAAC;EACF1C,gBAAgB,CAACuE,KAAK,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA,MAAMG,iBAAiB,GAAI9C,OAAoB,IAAW;EACxD,IAAI;IACF;IACA,MAAM+C,QAAQ,GAAGhC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C+B,QAAQ,CAACxC,KAAK,CAACU,OAAO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAED,MAAM+B,WAAW,GAAG,KAAKtF,CAAC,CAAC,SAAS,CAAC,EAAE;IACvCqF,QAAQ,CAACE,WAAW,GAAGD,WAAW;;IAElC;IACA,MAAMxB,IAAI,GAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC;IAC5CsB,QAAQ,CAACxC,KAAK,CAACsB,IAAI,GAAG,GAAGL,IAAI,CAACK,IAAI,GAAGC,MAAM,CAACE,OAAO,IAAI;IACvDe,QAAQ,CAACxC,KAAK,CAACqB,GAAG,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAGE,MAAM,CAACC,OAAO,GAAG,EAAE,IAAI;IAE1DhB,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACY,QAAQ,CAAC;;IAEnC;IACAG,UAAU,CAAC,MAAM;MACfH,QAAQ,CAACxC,KAAK,CAAC4C,OAAO,GAAG,GAAG;IAC9B,CAAC,EAAE,EAAE,CAAC;;IAEN;IACAD,UAAU,CAAC,MAAM;MACfH,QAAQ,CAACxC,KAAK,CAAC4C,OAAO,GAAG,GAAG;MAC5BD,UAAU,CAAC,MAAM;QACf,IAAIH,QAAQ,CAACH,UAAU,EAAE;UACvBG,QAAQ,CAACH,UAAU,CAACC,WAAW,CAACE,QAAQ,CAAC;QAC3C;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,CAAC,OAAOK,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,GAAIrD,OAAoB,IAAkB;EAAA,IAAAsD,oBAAA;EAChE,MAAM9B,IAAI,GAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC;EAC5C,MAAM8B,KAAK,GAAG/E,aAAa,CAACwB,OAAO,CAAC;EACpC,MAAMwD,WAAW,GAAGnE,mBAAmB,CAACW,OAAO,CAAC;EAEhD,OAAO;IACLf,OAAO,EAAEe,OAAO,CAACf,OAAO;IACxBO,EAAE,EAAEQ,OAAO,CAACR,EAAE,IAAI,EAAE;IACpBC,SAAS,EAAEC,mBAAmB,CAACM,OAAO,CAAC;IACvCyD,IAAI,EAAE,EAAAH,oBAAA,GAAAtD,OAAO,CAACiD,WAAW,cAAAK,oBAAA,uBAAnBA,oBAAA,CAAqB3D,IAAI,CAAC,CAAC,KAAI,EAAE;IACvC+D,UAAU,EAAEC,KAAK,CAACC,IAAI,CAAC5D,OAAO,CAAC0D,UAAU,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC/DD,GAAG,CAACC,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI,CAACE,KAAK;MAC3B,OAAOH,GAAG;IACZ,CAAC,EAAE,CAAC,CAA2B,CAAC;IAChCP,KAAK;IACLC,WAAW;IACXhC,IAAI,EAAE;MACJI,GAAG,EAAEJ,IAAI,CAACI,GAAG;MACbC,IAAI,EAAEL,IAAI,CAACK,IAAI;MACfqC,KAAK,EAAE1C,IAAI,CAAC0C,KAAK;MACjBC,MAAM,EAAE3C,IAAI,CAAC2C;IACf,CAAC;IACDC,QAAQ,EAAE,EAAE;IAAE;IACdC,SAAS,EAAE7C,IAAI,CAAC0C,KAAK,GAAG,CAAC,IAAI1C,IAAI,CAAC2C,MAAM,GAAG,CAAC;IAC5CG,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,GAAG,EAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI,CAAC;EAC5B,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,GAAIC,KAAiB,IAAW;EACtD,IAAI;IACF;IACA;IACA;;IAEA,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACpG,QAAQ,IAAIoG,MAAM,CAACpG,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;MACxE;IACF;IAEA,IAAIyB,kBAAkB,CAACyE,MAAM,CAAC,EAAE;MAC9B;IACF;IAEA,IAAIA,MAAM,CAACC,YAAY,CAAC,6BAA6B,CAAC,EAAE;MACtD;IACF;;IAGA;IACA,MAAMC,kBAAkB,GAAG3B,kBAAkB,CAACyB,MAAM,CAAC;;IAErD;IACA,MAAMG,eAAe,GAAG,CAACD,kBAAkB,CAAC;;IAE5C;IACAE,cAAc,CAAC;MAAEC,QAAQ,EAAEF;IAAgB,CAAC,EAAE,IAAI,CAAC;;IAEnD;IACA3E,qCAAqC,CAACwE,MAAM,CAAC;;IAG7C;IACA;EAGF,CAAC,CAAC,OAAO1B,KAAK,EAAE,CAChB;AACF,CAAC;AAED,OAAO,MAAM8B,cAAc,GAAGA,CAACE,IAAS,EAAEC,MAAe,GAAG,KAAK,KAAW;EAC1E,MAAMf,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC1C1G,qBAAqB,GAAGwG,SAAS;EAEjC,IAAI,CAACe,MAAM,EAAE;IACX;IACAxH,YAAY,GAAG,EAAE;IACjBE,WAAW,CAAC4E,KAAK,CAAC,CAAC;EACrB;;EAEA;EACA,IAAIyC,IAAI,IAAIA,IAAI,CAACD,QAAQ,IAAIxB,KAAK,CAAC2B,OAAO,CAACF,IAAI,CAACD,QAAQ,CAAC,EAAE;IACzDC,IAAI,CAACD,QAAQ,CAAC1C,OAAO,CAAEzC,OAAoB,IAAK;MAC9C;MACAA,OAAO,CAACsE,SAAS,GAAGA,SAAS;;MAE7B;MACA,IAAItE,OAAO,CAACuD,KAAK,EAAE;QACjB;QACA,IAAIxF,WAAW,CAACwH,GAAG,CAACvF,OAAO,CAACuD,KAAK,CAAC,EAAE;UAClC,OAAO,CAAC;QACV,CAAC,MAAM;UACL;UACAxF,WAAW,CAACyH,GAAG,CAACxF,OAAO,CAACuD,KAAK,EAAEvD,OAAO,CAAC;UACvCnC,YAAY,CAAC4H,IAAI,CAACzF,OAAO,CAAC;QAE5B;MACF,CAAC,MAAM;QACL;QACA,MAAM0F,WAAW,GAAG7H,YAAY,CAAC8H,IAAI,CAACC,QAAQ,IAC5CA,QAAQ,CAAC3G,OAAO,KAAKe,OAAO,CAACf,OAAO,IACpC2G,QAAQ,CAACpG,EAAE,KAAKQ,OAAO,CAACR,EAAE,IAC1BoG,QAAQ,CAACnG,SAAS,KAAKO,OAAO,CAACP,SACjC,CAAC;QAED,IAAI,CAACiG,WAAW,EAAE;UAChB7H,YAAY,CAAC4H,IAAI,CAACzF,OAAO,CAAC;QAE5B,CAAC,MAAM,CAEP;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM6F,cAAc,GAAGA,CAAA,KAAa;EACzC,OAAOhI,YAAY;AACrB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiI,iBAAiB,GAAIvC,KAAa,IAA8B;EAC3E,OAAOxF,WAAW,CAACgI,GAAG,CAACxC,KAAK,CAAC;AAC/B,CAAC;;AAID;AACA;AACA;AACA,OAAO,MAAMyC,YAAY,GAAGA,CAAA,KAA4H;EACtJ,OAAOnI,YAAY,CAACoI,GAAG,CAACjG,OAAO,KAAK;IAClCuD,KAAK,EAAEvD,OAAO,CAACuD,KAAK;IACpBtE,OAAO,EAAEe,OAAO,CAACf,OAAO;IACxBO,EAAE,EAAEQ,OAAO,CAACR,EAAE;IACdC,SAAS,EAAEO,OAAO,CAACP,SAAS;IAC5BgE,IAAI,EAAEzD,OAAO,CAACyD,IAAI;IAClBa,SAAS,EAAEtE,OAAO,CAACsE,SAAS;IAC5BG,GAAG,EAAEzE,OAAO,CAACyE;EACf,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyB,uBAAuB,GAAG,MAAOC,SAAkB,IAAoB;EAClF,IAAI;IACF,IAAItI,YAAY,CAACuI,MAAM,KAAK,CAAC,EAAE;MAC7B;MACA,MAAMC,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;MACrD,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACH,MAAM,KAAK,CAAC,EAAE;QACjF;QACA;MACF;MACA;MACA,MAAMI,qBAAqB,CAACL,SAAS,CAAC;IACxC,CAAC,MAAM;MACL;MACA,MAAMM,wBAAwB,CAAC,CAAC;MAChC,MAAMD,qBAAqB,CAACL,SAAS,CAAC;IACxC;EACF,CAAC,CAAC,OAAO/C,KAAK,EAAE;IACd;EAAA;AAEJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsD,mBAAmB,GAAGA,CAAA,KAAc;EAC/C,OAAO7I,YAAY,CAACuI,MAAM;AAC5B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,uBAAuB,GAAGA,CAAA,KAAc;EACnD,OAAO7I,qBAAqB;AAC9B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM8I,gBAAgB,GAAGA,CAAA,KAAY;EAC1C/I,YAAY,GAAG,EAAE;EACjBC,qBAAqB,GAAG,EAAE;AAC5B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM2I,wBAAwB,GAAG,MAAAA,CAAA,KAA2B;EACjE,IAAI;IACF,IAAI,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACC,KAAK,EAAE;MAC3E,MAAMC,WAAW,GAAG;QAClBT,WAAW,EAAE1I,YAAY;QACzByG,SAAS,EAAExG,qBAAqB;QAChC2G,GAAG,EAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI;QACzBpD,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrB0F,YAAY,EAAEpJ,YAAY,CAACuI,MAAM;QACjCc,SAAS,EAAErJ,YAAY,CAACoI,GAAG,CAACjG,OAAO,KAAK;UACtCuD,KAAK,EAAEvD,OAAO,CAACuD,KAAK;UACpBtE,OAAO,EAAEe,OAAO,CAACf,OAAO;UACxBO,EAAE,EAAEQ,OAAO,CAACR,EAAE;UACdC,SAAS,EAAEO,OAAO,CAACP,SAAS;UAC5BgE,IAAI,EAAEzD,OAAO,CAACyD,IAAI;UAClBC,UAAU,EAAE1D,OAAO,CAAC0D,UAAU;UAC9BY,SAAS,EAAEtE,OAAO,CAACsE,SAAS;UAC5BG,GAAG,EAAEzE,OAAO,CAACyE;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMoC,MAAM,CAACC,OAAO,CAACC,KAAK,CAACvB,GAAG,CAAC;QAC7B,yBAAyB,EAAEwB;MAC7B,CAAC,CAAC;MACLA,WAAW,CAACE,SAAS,CAACzE,OAAO,CAAC,CAAC0E,IAAI,EAAErI,KAAK,KAAK;QAC1CsI,OAAO,CAACC,GAAG,CAAC,GAAGvI,KAAK,GAAG,CAAC,KAAKqI,IAAI,CAAClI,OAAO,MAAMkI,IAAI,CAAC5D,KAAK,EAAE,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMyD,WAAW,GAAG;QAClBT,WAAW,EAAE1I,YAAY;QACzByG,SAAS,EAAExG,qBAAqB;QAChC2G,GAAG,EAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI;QACzBpD,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrB0F,YAAY,EAAEpJ,YAAY,CAACuI,MAAM;QACjCc,SAAS,EAAErJ,YAAY,CAACoI,GAAG,CAACjG,OAAO,KAAK;UACtCuD,KAAK,EAAEvD,OAAO,CAACuD,KAAK;UACpBtE,OAAO,EAAEe,OAAO,CAACf,OAAO;UACxBO,EAAE,EAAEQ,OAAO,CAACR,EAAE;UACdC,SAAS,EAAEO,OAAO,CAACP,SAAS;UAC5BgE,IAAI,EAAEzD,OAAO,CAACyD,IAAI;UAClBC,UAAU,EAAE1D,OAAO,CAAC0D,UAAU;UAC9BY,SAAS,EAAEtE,OAAO,CAACsE,SAAS;UAC5BG,GAAG,EAAEzE,OAAO,CAACyE;QACf,CAAC,CAAC;MACJ,CAAC;MACD6C,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAACT,WAAW,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC,OAAO5D,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkD,0BAA0B,GAAG,MAAAA,CAAA,KAA0B;EAClE,IAAI;IACF,IAAI,OAAOO,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACC,KAAK,EAAE;MAC3E,MAAMW,MAAM,GAAG,MAAMb,MAAM,CAACC,OAAO,CAACC,KAAK,CAAChB,GAAG,CAAC,yBAAyB,CAAC;MACxE,OAAO2B,MAAM,CAAC,yBAAyB,CAAC,IAAI,IAAI;IAClD,CAAC,MAAM;MACL;MACA,MAAMtC,IAAI,GAAGkC,YAAY,CAACK,OAAO,CAAC,yBAAyB,CAAC;MAC5D,OAAOvC,IAAI,GAAGoC,IAAI,CAACI,KAAK,CAACxC,IAAI,CAAC,GAAG,IAAI;IACvC;EACF,CAAC,CAAC,OAAOhC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMoD,qBAAqB,GAAG,MAAOL,SAAkB,IAAoB;EAChF,IAAI;IAEF,MAAME,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;IAErD,IAAI,CAACD,UAAU,EAAE;MACf;IACF;IAEA,MAAMwB,OAAO,GAAG;MACdC,QAAQ,EAAE;QACRrD,GAAG,EAAE4B,UAAU,CAAC5B,GAAG,IAAI3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI;QAC3CpD,KAAK,EAAE8E,UAAU,CAAC9E,KAAK,IAAIR,QAAQ,CAACQ,KAAK;QACzC+C,SAAS,EAAE+B,UAAU,CAAC/B,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC3DyC,YAAY,EAAEZ,UAAU,CAACY,YAAY,IAAI,CAAC;QAC1Cc,UAAU,EAAE,IAAIxD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC;MACDW,QAAQ,EAAEkB,UAAU,CAACE,WAAW,IAAI,EAAE;MACtCW,SAAS,EAAEb,UAAU,CAACa,SAAS,IAAI;IACrC,CAAC;;IAGD;IACA,MAAMc,gBAAgB,CAACH,OAAO,EAAE1B,SAAS,CAAC;EAE5C,CAAC,CAAC,OAAO/C,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM4E,gBAAgB,GAAG,MAAAA,CAAO5C,IAAS,EAAEe,SAAkB,KAAoB;EACtF,IAAI;IAEF;IACA,MAAM8B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACA,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACZ,IAAI,CAACC,SAAS,CAACrC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MACzDiD,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACA,MAAM/D,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC5E,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IAChE,MAAM0I,SAAS,GAAG,CAAClD,IAAI,CAAC0C,QAAQ,CAACvG,KAAK,IAAI,cAAc,EAAE3B,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;IACvF,MAAM2I,QAAQ,GAAG,yBAAyBD,SAAS,IAAIhE,SAAS,OAAO;;IAEvE;IACA2D,QAAQ,CAAC5C,MAAM,CAAC,SAAS,EAAE8C,QAAQ,EAAEI,QAAQ,CAAC,CAAC,CAAC;;IAEhD;IACAN,QAAQ,CAAC5C,MAAM,CAAC,cAAc,EAAED,IAAI,CAAC0C,QAAQ,CAACb,YAAY,CAACuB,QAAQ,CAAC,CAAC,CAAC;IACtEP,QAAQ,CAAC5C,MAAM,CAAC,KAAK,EAAED,IAAI,CAAC0C,QAAQ,CAACrD,GAAG,CAAC;IACzCwD,QAAQ,CAAC5C,MAAM,CAAC,WAAW,EAAED,IAAI,CAAC0C,QAAQ,CAACxD,SAAS,CAAC;;IAIrD;IACA,MAAM;MAAEmE;IAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;IAE1D,IAAI,CAACtC,SAAS,EAAE;MACd,MAAM,IAAIuC,KAAK,CAAC,6CAA6C,CAAC;IAChE;IAEA,MAAMC,QAAQ,GAAG,MAAMF,gBAAgB,CAACtC,SAAS,EAAE8B,QAAQ,CAAC;EAE9D,CAAC,CAAC,OAAO7E,KAAK,EAAE;IAEd;IACA,MAAMwF,YAAY,GAAGxF,KAAK,YAAYsF,KAAK,GAAGtF,KAAK,CAACyF,OAAO,GAAG,wBAAwB;IAEtF,MAAMzF,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0F,aAAa,GAAGA,CAAA,KAAY;EACvC,IAAIlL,iBAAiB,EAAE;EAEvBA,iBAAiB,GAAG,IAAI;EACxBgJ,gBAAgB,CAAC,CAAC;;EAGlB;EACA,IAAI,CAAC3I,cAAc,EAAE;IACnBA,cAAc,GAAG2G,kBAAkB;IACnC7D,QAAQ,CAACqB,gBAAgB,CAAC,OAAO,EAAEnE,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;;EAEA;EACA,IAAI,OAAO4I,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACkC,OAAO,EAAE;IACnD,IAAI;MACFlC,MAAM,CAACkC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO7F,KAAK,EAAE;MACd;MACAtB,MAAM,CAACoH,aAAa,CAAC,IAAIC,WAAW,CAAC,iCAAiC,CAAC,CAAC;IAC1E;EACF;;EAEA;EACAC,wBAAwB,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG,MAAOlD,SAAiB,IAAoB;EACtE,IAAI,CAACvI,iBAAiB,EAAE;EAExBA,iBAAiB,GAAG,KAAK;;EAGzB;EACA,IAAIK,cAAc,EAAE;IAClB8C,QAAQ,CAACuI,mBAAmB,CAAC,OAAO,EAAErL,cAAc,EAAE,IAAI,CAAC;IAC3DA,cAAc,GAAG,IAAI;EACvB;;EAEA;EACA,IAAIJ,YAAY,CAACuI,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAMK,wBAAwB,CAAC,CAAC;;IAEhC;IACA,MAAMD,qBAAqB,CAACL,SAAS,CAAC;EACxC;;EAEA;EACA3D,mBAAmB,CAAC,CAAC;;EAErB;EACA,IAAI,OAAOqE,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACkC,OAAO,EAAE;IACnD,IAAI;MACFlC,MAAM,CAACkC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO7F,KAAK,EAAE;MACd;MACAtB,MAAM,CAACoH,aAAa,CAAC,IAAIC,WAAW,CAAC,gCAAgC,CAAC,CAAC;IACzE;EACF;;EAEA;EACAI,wBAAwB,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA,MAAMH,wBAAwB,GAAGA,CAAA,KAAY;EAC3C;EACAG,wBAAwB,CAAC,CAAC;EAE1B,MAAMC,cAAc,GAAGzI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACpDwI,cAAc,CAAChK,EAAE,GAAG,kCAAkC;EACtDgK,cAAc,CAACjJ,KAAK,CAACU,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,MAAMwI,SAAS,GAAG,MAAM/L,CAAC,CAAC,uBAAuB,CAAC,EAAE;EACpD,MAAMgM,YAAY,GAAG,KAAKhM,CAAC,CAAC,4CAA4C,CAAC,EAAE;EAC3E,MAAMiM,WAAW,GAAG,KAAKjM,CAAC,CAAC,mDAAmD,CAAC,EAAE;EACjF,MAAMkM,aAAa,GAAG,KAAKlM,CAAC,CAAC,0CAA0C,CAAC,EAAE;EAC1E,MAAMmM,UAAU,GAAG,KAAKnM,CAAC,CAAC,mCAAmC,CAAC,EAAE;EAChE,MAAMoM,SAAS,GAAG,KAAKpM,CAAC,CAAC,iCAAiC,CAAC,EAAE;EAE7D8L,cAAc,CAACO,SAAS,GAAG;AAC7B,MAAMN,SAAS;AACf;AACA,QAAQC,YAAY;AACpB,QAAQC,WAAW;AACnB,QAAQC,aAAa;AACrB,QAAQC,UAAU;AAClB,QAAQC,SAAS;AACjB;AACA,GAAG;EAED/I,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACqH,cAAc,CAAC;;EAEzC;EACAtG,UAAU,CAAC,MAAM;IACf,IAAIsG,cAAc,CAAC5G,UAAU,EAAE;MAC7B4G,cAAc,CAACjJ,KAAK,CAAC4C,OAAO,GAAG,KAAK;IACtC;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA,MAAMoG,wBAAwB,GAAGA,CAAA,KAAY;EAC3C,MAAMS,mBAAmB,GAAGjJ,QAAQ,CAACkJ,cAAc,CAAC,kCAAkC,CAAC;EACvF,IAAID,mBAAmB,EAAE;IACvBA,mBAAmB,CAACE,MAAM,CAAC,CAAC;EAC9B;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAY;EAC7CvM,iBAAiB,GAAG,KAAK;EACzBC,YAAY,GAAG,EAAE;EACjBE,WAAW,CAAC4E,KAAK,CAAC,CAAC;EACnB7E,qBAAqB,GAAG,EAAE;EAC1BG,cAAc,GAAG,IAAI;;EAErB;EACA,IAAI,OAAO4I,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACkC,OAAO,IAAIlC,MAAM,CAACkC,OAAO,CAACqB,SAAS,EAAE;IAC/E;IACAvD,MAAM,CAACkC,OAAO,CAACqB,SAAS,CAACC,WAAW,CAAC,CAACxB,OAAO,EAAEyB,OAAO,EAAEC,YAAY,KAAK;MACvE,IAAI1B,OAAO,CAACI,MAAM,KAAK,qBAAqB,EAAE;QAC5CrL,iBAAiB,GAAGiL,OAAO,CAAC2B,QAAQ;QACpCD,YAAY,CAAC;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;MAEA,IAAI5B,OAAO,CAACI,MAAM,KAAK,kBAAkB,EAAE;QACzCsB,YAAY,CAAC;UACXC,QAAQ,EAAE5M,iBAAiB;UAC3B8M,aAAa,EAAE5M,qBAAqB;UACpCmJ,YAAY,EAAEpJ,YAAY,CAACuI;QAC7B,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MAEA,IAAIyC,OAAO,CAACI,MAAM,KAAK,gBAAgB,EAAE;QACvCsB,YAAY,CAAC;UACXnF,IAAI,EAAEvH,YAAY;UAClByG,SAAS,EAAExG;QACb,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MAEA,IAAI+K,OAAO,CAACI,MAAM,KAAK,kBAAkB,EAAE;QACzCrC,gBAAgB,CAAC,CAAC;QAClB2D,YAAY,CAAC;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,CACP;AACF,CAAC;;AAGD;AACAN,mBAAmB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}