{"ast": null, "code": "// ScrapingService.ts - Handles targeted click-based scraping functionality\nimport i18n from'../multilinguial/i18n';// Get the translation function directly from i18next\nconst t=i18n.t.bind(i18n);/**\r\n * Translation keys used in this service:\r\n * \r\n * \"Click Scraping Active\" - Main title for scraping instructions\r\n * \"Click any element to scrape its XPath data\" - Click element instruction\r\n * \"Only the clicked element is scraped no duplicates\" - Only clicked element instruction  \r\n * \"Original click functionality still works\" - Original click functionality instruction\r\n * \"Red borders show scraped elements\" - Red borders instruction\r\n * \"Data is saved to Chrome storage\" - Data saved instruction\r\n * \"Scraped\" - Feedback when element is scraped\r\n * \"Element already scraped click blocked\" - Tooltip for already scraped elements\r\n * \r\n * This service uses the i18n instance directly for translations.\r\n * The translations will automatically fallback to the English keys if translation is missing.\r\n */// Global state to track if scraping is active\nlet _isScrapingActive=false;let _scrapedData=[];let _lastScrapedTimestamp='';let _elementMap=new Map();// Map to track elements by XPath\nlet _clickListener=null;let _highlightedElements=new Set();// Track highlighted elements\nlet _overlayElements=new Set();// Track overlay elements for click blocking\n// Interface for element data\n// Interface for scraped page data\n/**\r\n * Check if scraping is currently active\r\n */export const isScrapingActive=()=>{return _isScrapingActive;};/**\r\n * Set the scraping active state\r\n */export const setScrapingActive=active=>{_isScrapingActive=active;};/**\r\n * Generate XPath for an element\r\n */const generateXPath=el=>{if(!el||el.nodeType!==Node.ELEMENT_NODE)return'';const path=[];while(el&&el.nodeType===Node.ELEMENT_NODE){let index=1;let sibling=el.previousElementSibling;while(sibling){if(sibling.tagName===el.tagName)index++;sibling=sibling.previousElementSibling;}path.unshift(`${el.tagName}[${index}]`);el=el.parentElement;}return'/'+path.join('/');};/**\r\n * Generate CSS selector for an element\r\n */const generateCssSelector=el=>{if(!el)return'';const path=[];while(el&&el.nodeType===Node.ELEMENT_NODE){let selector=el.tagName.toLowerCase();if(el.id){selector+=`#${el.id}`;path.unshift(selector);break;}else{// Safely handle className - it might be a string or SVGAnimatedString\nconst className=getElementClassName(el);if(className){selector+='.'+className.trim().replace(/\\s+/g,'.');}path.unshift(selector);el=el.parentElement;}}return path.join(' > ');};/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */const getElementClassName=el=>{if(!el)return'';// For HTML elements, className is usually a string\nif(typeof el.className==='string'){return el.className;}// For SVG elements, className might be an SVGAnimatedString\nif(el.className&&typeof el.className==='object'&&'baseVal'in el.className){return el.className.baseVal||'';}// Fallback: try to get class attribute directly\nreturn el.getAttribute('class')||'';};/**\r\n * Check if element should be ignored for highlighting\r\n */const shouldIgnoreHighlight=element=>{return element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||!!element.closest(\"#Tooltip-unique\")||!!element.closest(\"#my-react-drawer\")||!!element.closest(\"#tooltip-section-popover\")||!!element.closest(\"#btn-setting-toolbar\")||!!element.closest(\"#button-toolbar\")||!!element.closest(\"#color-picker\")||!!element.closest(\".qadpt-ext-banner\")||!!element.closest(\"#leftDrawer\")||!!element.closest(\"#image-popover\")||!!element.closest(\"#toggle-fit\")||!!element.closest(\"#color-popover\")||!!element.closest(\"#rte-popover\")||!!element.closest(\"#rte-alignment\")||!!element.closest(\"#rte-alignment-menu\")||!!element.closest(\"#rte-font\")||!!element.closest(\"#rte-bold\")||!!element.closest(\"#rte-italic\")||!!element.closest(\"#rte-underline\")||!!element.closest(\"#rte-strke-through\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#rte-more\")||!!element.closest(\"#rte-text-color\")||!!element.closest(\"#rte-text-color-popover\")||!!element.closest(\"#rte-text-highlight\")||!!element.closest(\"#rte-text-highlight-pop\")||!!element.closest(\"#rte-text-heading\")||!!element.closest(\"#rte-text-heading-menu-items\")||!!element.closest(\"#rte-text-format\")||!!element.closest(\"#rte-text-ul\")||!!element.closest(\"#rte-text-hyperlink\")||!!element.closest(\"#rte-video\")||!!element.closest(\"#rte-clear-formatting\")||!!element.closest(\"#rte-hyperlink-popover\")||!!element.closest(\"#rte-box\")||!!element.closest(element.id.startsWith(\"rt-editor\")?`#${element.id}`:\"nope\")||!!element.closest(\"#rte-placeholder\")||!!element.closest(\"#qadpt-designpopup\")||!!element.closest(\"#image-properties\")||!!element.closest(\"#rte-toolbar\")||!!element.closest(\"#tooltipdialog\")||!!element.closest(\"#rte-toolbar-paper\")||!!element.closest(\"#stop-scraping-button-container\")||!!element.closest(\"#rte-alignment-menu\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#quickadapt-scraping-instructions\")// Ignore our own instruction banner\n;};/**\r\n * Check if element should be ignored for events\r\n */const shouldIgnoreEvents=element=>{return element.classList.contains(\"mdc-tooltip__surface\")||element.classList.contains(\"mdc-tooltip__surface-animation\")||element.classList.contains(\"mdc-tooltip\")||element.classList.contains(\"mdc-tooltip--shown\")||element.classList.contains(\"mdc-tooltip--showing\")||element.classList.contains(\"mdc-tooltip--hiding\")||element.getAttribute(\"role\")===\"tooltip\"||!!element.closest(\"#Tooltip-unique\")||!!element.closest(\"#tooltip-section-popover\")||!!element.closest(\"#btn-setting-toolbar\")||!!element.closest(\"#button-toolbar\")||!!element.closest(\"#color-picker\")||!!element.closest(\".qadpt-ext-banner\")||!!element.closest(\"#leftDrawer\")||!!element.closest(\"#rte-popover\")||!!element.closest(\"#stop-scraping-button-container\")||!!element.closest(element.id.startsWith(\"rt-editor\")?`#${element.id}`:\"nope\")||!!element.closest(\"#rte-box\")||!!element.closest(\"#rte-placeholder\")||!!element.closest(\"#rte-alignment-menu-items\")||!!element.closest(\"#qadpt-designpopup\")||!!element.closest(\"#quickadapt-scraping-instructions\")// Ignore our own instruction banner\n;};/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */const addPersistentHighlightWithoutBlocking=element=>{if(shouldIgnoreHighlight(element))return;// Add persistent red border\nelement.style.outline='3px solid #ff0000 !important';element.style.outlineOffset='2px';element.setAttribute('data-quickadapt-highlighted','true');_highlightedElements.add(element);// No overlay creation - allow clicks to pass through\n};/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */const addPersistentHighlight=element=>{var _element$offsetParent;if(shouldIgnoreHighlight(element))return;// Add persistent red border\nelement.style.outline='3px solid #ff0000 !important';element.style.outlineOffset='2px';element.setAttribute('data-quickadapt-highlighted','true');_highlightedElements.add(element);// Create click-blocking overlay\nconst overlay=document.createElement('div');overlay.style.cssText=`\n    position: absolute;\n    top: ${element.offsetTop}px;\n    left: ${element.offsetLeft}px;\n    width: ${element.offsetWidth}px;\n    height: ${element.offsetHeight}px;\n    background: transparent;\n    z-index: 999999;\n    pointer-events: auto;\n    cursor: not-allowed;\n  `;overlay.setAttribute('data-quickadapt-overlay','true');const tooltipText=t('Element already scraped click blocked');overlay.title=tooltipText;// Position overlay relative to the element's parent\nconst rect=element.getBoundingClientRect();const parentRect=((_element$offsetParent=element.offsetParent)===null||_element$offsetParent===void 0?void 0:_element$offsetParent.getBoundingClientRect())||{top:0,left:0};overlay.style.top=`${rect.top-parentRect.top+window.scrollY}px`;overlay.style.left=`${rect.left-parentRect.left+window.scrollX}px`;// Add overlay to the element's parent or body\nconst parent=element.offsetParent||document.body;parent.appendChild(overlay);_overlayElements.add(overlay);// Block clicks on the overlay\noverlay.addEventListener('click',e=>{e.preventDefault();e.stopPropagation();},true);};/**\r\n * Remove all highlights and overlays\r\n */const removeAllHighlights=()=>{// Remove highlights\n_highlightedElements.forEach(element=>{if(element&&element.style){element.style.outline='';element.style.outlineOffset='';element.removeAttribute('data-quickadapt-highlighted');}});_highlightedElements.clear();// Remove overlays\n_overlayElements.forEach(overlay=>{if(overlay&&overlay.parentNode){overlay.parentNode.removeChild(overlay);}});_overlayElements.clear();};/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */const showClickFeedback=element=>{try{// Create a temporary feedback indicator\nconst feedback=document.createElement('div');feedback.style.cssText=`\n      position: absolute;\n      background: #4CAF50;\n      color: white;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: bold;\n      z-index: 10001;\n      pointer-events: none;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n      opacity: 0;\n      transition: opacity 0.2s ease;\n    `;const scrapedText=`✓ ${t('Scraped')}`;feedback.textContent=scrapedText;// Position the feedback near the clicked element\nconst rect=element.getBoundingClientRect();feedback.style.left=`${rect.left+window.scrollX}px`;feedback.style.top=`${rect.top+window.scrollY-30}px`;document.body.appendChild(feedback);// Animate in\nsetTimeout(()=>{feedback.style.opacity='1';},10);// Remove after 2 seconds\nsetTimeout(()=>{feedback.style.opacity='0';setTimeout(()=>{if(feedback.parentNode){feedback.parentNode.removeChild(feedback);}},200);},2000);}catch(error){}};/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */const extractElementData=element=>{var _element$textContent;const rect=element.getBoundingClientRect();const xpath=generateXPath(element);const cssSelector=generateCssSelector(element);return{tagName:element.tagName,id:element.id||'',className:getElementClassName(element),text:((_element$textContent=element.textContent)===null||_element$textContent===void 0?void 0:_element$textContent.trim())||'',attributes:Array.from(element.attributes).reduce((acc,attr)=>{acc[attr.name]=attr.value;return acc;},{}),xpath,cssSelector,rect:{top:rect.top,left:rect.left,width:rect.width,height:rect.height},children:[],// We don't need children for click-based scraping\nisVisible:rect.width>0&&rect.height>0,timestamp:new Date().toISOString(),url:window.location.href// Add URL to each element\n};};/**\r\n * Handle click events for element scraping\r\n */const handleElementClick=event=>{try{// IMPORTANT: Don't prevent default or stop propagation\n// This allows the original click functionality to work normally\n// (navigation, form submission, button clicks, etc.)\nconst target=event.target;if(!target||!target.nodeType||target.nodeType!==Node.ELEMENT_NODE){return;}if(shouldIgnoreEvents(target)){return;}if(target.hasAttribute('data-quickadapt-highlighted')){return;}// Extract data from clicked element ONLY\nconst clickedElementData=extractElementData(target);// Store only the clicked element data (no parent element)\nconst elementsToStore=[clickedElementData];// Add to scraped data\nsetScrapedData({elements:elementsToStore},true);// Add persistent red border WITHOUT blocking clicks (only to clicked element)\naddPersistentHighlightWithoutBlocking(target);// // Show brief success feedback\n// showClickFeedback(target);\n}catch(error){}};export const setScrapedData=function(data){let append=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const timestamp=new Date().toISOString();_lastScrapedTimestamp=timestamp;if(!append){// Clear existing data if not appending\n_scrapedData=[];_elementMap.clear();}// Process each element in the data\nif(data&&data.elements&&Array.isArray(data.elements)){data.elements.forEach(element=>{// Add timestamp to the element\nelement.timestamp=timestamp;// Use XPath as a unique identifier for the element\nif(element.xpath){// If element already exists in the map, don't add it again (prevent duplicates)\nif(_elementMap.has(element.xpath)){return;// Skip this element\n}else{// New element, add to map and data array\n_elementMap.set(element.xpath,element);_scrapedData.push(element);}}else{// No XPath, check for duplicates by other means (tagName + id + className)\nconst isDuplicate=_scrapedData.some(existing=>existing.tagName===element.tagName&&existing.id===element.id&&existing.className===element.className);if(!isDuplicate){_scrapedData.push(element);}else{}}});}};/**\r\n * Get the currently scraped data\r\n */export const getScrapedData=()=>{return _scrapedData;};/**\r\n * Get element by XPath\r\n */export const getElementByXPath=xpath=>{return _elementMap.get(xpath);};/**\r\n * Get all xpath data from scraped elements\r\n */export const getXPathData=()=>{return _scrapedData.map(element=>({xpath:element.xpath,tagName:element.tagName,id:element.id,className:element.className,text:element.text,timestamp:element.timestamp,url:element.url}));};/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */export const exportScrapedDataToFile=async accountId=>{try{if(_scrapedData.length===0){// Try to load from storage if no current data\nconst storedData=await loadScrapedDataFromStorage();if(!storedData||!storedData.scrapedData||storedData.scrapedData.length===0){// alert('No scraped data available to send to API. Please scrape some elements first.');\nreturn;}// Use stored data for API call\nawait saveScrapedDataToFile(accountId);}else{// Save current data to storage first, then send to API\nawait saveScrapedDataToStorage();await saveScrapedDataToFile(accountId);}}catch(error){// alert('Error sending scraped data to backend API. Check console for details.');\n}};/**\r\n * Get scraped data count\r\n */export const getScrapedDataCount=()=>{return _scrapedData.length;};/**\r\n * Get the timestamp of the last scrape\r\n */export const getLastScrapedTimestamp=()=>{return _lastScrapedTimestamp;};/**\r\n * Clear scraped data\r\n */export const clearScrapedData=()=>{_scrapedData=[];_lastScrapedTimestamp='';};/**\r\n * Save scraped data to Chrome storage\r\n */export const saveScrapedDataToStorage=async()=>{try{if(typeof chrome!=='undefined'&&chrome.storage&&chrome.storage.local){const storageData={scrapedData:_scrapedData,timestamp:_lastScrapedTimestamp,url:window.location.href,title:document.title,elementCount:_scrapedData.length,xpathData:_scrapedData.map(element=>({xpath:element.xpath,tagName:element.tagName,id:element.id,className:element.className,text:element.text,attributes:element.attributes,timestamp:element.timestamp,url:element.url}))};await chrome.storage.local.set({'quickadapt-scraped-data':storageData});storageData.xpathData.forEach((item,index)=>{console.log(`${index+1}. ${item.tagName} - ${item.xpath}`);});}else{// Fallback: save to localStorage\nconst storageData={scrapedData:_scrapedData,timestamp:_lastScrapedTimestamp,url:window.location.href,title:document.title,elementCount:_scrapedData.length,xpathData:_scrapedData.map(element=>({xpath:element.xpath,tagName:element.tagName,id:element.id,className:element.className,text:element.text,attributes:element.attributes,timestamp:element.timestamp,url:element.url}))};localStorage.setItem('quickadapt-scraped-data',JSON.stringify(storageData));}}catch(error){}};/**\r\n * Load scraped data from Chrome storage\r\n */export const loadScrapedDataFromStorage=async()=>{try{if(typeof chrome!=='undefined'&&chrome.storage&&chrome.storage.local){const result=await chrome.storage.local.get('quickadapt-scraped-data');return result['quickadapt-scraped-data']||null;}else{// Fallback: load from localStorage\nconst data=localStorage.getItem('quickadapt-scraped-data');return data?JSON.parse(data):null;}}catch(error){return null;}};/**\r\n * Send scraped data from Chrome storage to backend API\r\n */export const saveScrapedDataToFile=async accountId=>{try{const storedData=await loadScrapedDataFromStorage();if(!storedData){return;}const apiData={metadata:{url:storedData.url||window.location.href,title:storedData.title||document.title,timestamp:storedData.timestamp||new Date().toISOString(),elementCount:storedData.elementCount||0,exportedAt:new Date().toISOString()},elements:storedData.scrapedData||[],xpathData:storedData.xpathData||[]};// Send data to backend API\nawait uploadXPathsFile(apiData,accountId);}catch(error){}};/**\r\n * Upload XPath data to backend API using existing FileService\r\n */export const uploadXPathsFile=async(data,accountId)=>{try{// Convert JSON data to FormData as expected by the existing API\nconst formData=new FormData();// Create a JSON file blob\nconst jsonBlob=new Blob([JSON.stringify(data,null,2)],{type:'application/json'});// Generate filename with timestamp\nconst timestamp=new Date().toISOString().replace(/[:.]/g,'-');const pageTitle=(data.metadata.title||'scraped-data').replace(/[^a-zA-Z0-9]/g,'-');const filename=`quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;// Add the file to FormData\nformData.append('aifiles',jsonBlob,filename);// ✅ Correct key name\n// Add metadata as form fields if needed\nformData.append('elementCount',data.metadata.elementCount.toString());formData.append('url',data.metadata.url);formData.append('timestamp',data.metadata.timestamp);// Import and use the existing uploadXpathsFile function\nconst{uploadXpathsFile}=await import('./FileService');if(!accountId){throw new Error('Account ID is required to upload XPath data');}const response=await uploadXpathsFile(accountId,formData);}catch(error){// Show error message to user\nconst errorMessage=error instanceof Error?error.message:'Unknown error occurred';throw error;// Re-throw to be caught by calling function\n}};/**\r\n * Start click-based scraping process\r\n */export const startScraping=()=>{if(_isScrapingActive)return;_isScrapingActive=true;clearScrapedData();// Add click event listener to capture element clicks\nif(!_clickListener){_clickListener=handleElementClick;document.addEventListener('click',_clickListener,true);// Use capture phase\n}// Send message to content script to enable click-based scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'startClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));}}// Show user instruction with translation support\nshowScrapingInstructions();};/**\r\n * Stop click-based scraping process\r\n */export const stopScraping=async accountId=>{if(!_isScrapingActive)return;_isScrapingActive=false;// Remove click event listener\nif(_clickListener){document.removeEventListener('click',_clickListener,true);_clickListener=null;}// Save scraped data to Chrome storage\nif(_scrapedData.length>0){await saveScrapedDataToStorage();// Get data from Chrome storage and save to file\nawait saveScrapedDataToFile(accountId);}// Remove all highlights and overlays\nremoveAllHighlights();// Send message to background script to stop scraping\nif(typeof chrome!=='undefined'&&chrome.runtime){try{chrome.runtime.sendMessage({action:'stopClickScraping'});}catch(error){// Fallback: try to communicate through window events\nwindow.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));}}// Hide instructions\nhideScrapingInstructions();};/**\r\n * Show scraping instructions to user\r\n */const showScrapingInstructions=()=>{// Remove existing instruction if any\nhideScrapingInstructions();const instructionDiv=document.createElement('div');instructionDiv.id='quickadapt-scraping-instructions';instructionDiv.style.cssText=`\n    position: fixed;\n    top: 20px;\n    right: 20px;\n    background: #4CAF50;\n    color: white;\n    padding: 15px 20px;\n    border-radius: 8px;\n    font-family: Arial, sans-serif;\n    font-size: 14px;\n    font-weight: bold;\n    z-index: 10000;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    max-width: 320px;\n    text-align: center;\n  `;// Use translations with i18n instance directly\nconst mainTitle=`🎯 ${t('Click Scraping Active')}`;const clickElement=`• ${t('Click any element to scrape its XPath data')}`;const onlyClicked=`• ${t('Only the clicked element is scraped no duplicates')}`;const originalClick=`• ${t('Original click functionality still works')}`;const redBorders=`• ${t('Red borders show scraped elements')}`;const dataSaved=`• ${t('Data is saved to Chrome storage')}`;instructionDiv.innerHTML=`\n    ${mainTitle}<br>\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\n      ${clickElement}<br>\n      ${onlyClicked}<br>\n      ${originalClick}<br>\n      ${redBorders}<br>\n      ${dataSaved}\n    </small>\n  `;document.body.appendChild(instructionDiv);// Auto-hide after 8 seconds\nsetTimeout(()=>{if(instructionDiv.parentNode){instructionDiv.style.opacity='0.7';}},8000);};/**\r\n * Hide scraping instructions\r\n */const hideScrapingInstructions=()=>{const existingInstruction=document.getElementById('quickadapt-scraping-instructions');if(existingInstruction){existingInstruction.remove();}};/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */export const initScrapingService=()=>{_isScrapingActive=false;_scrapedData=[];_elementMap.clear();_lastScrapedTimestamp='';_clickListener=null;// Check if we're in a Chrome extension environment\nif(typeof chrome!=='undefined'&&chrome.runtime&&chrome.runtime.onMessage){// Listen for messages from background script\nchrome.runtime.onMessage.addListener((message,_sender,sendResponse)=>{if(message.action==='updateScrapingState'){_isScrapingActive=message.isActive;sendResponse({success:true});return true;}if(message.action==='getScrapingState'){sendResponse({isActive:_isScrapingActive,lastTimestamp:_lastScrapedTimestamp,elementCount:_scrapedData.length});return true;}if(message.action==='getScrapedData'){sendResponse({data:_scrapedData,timestamp:_lastScrapedTimestamp});return true;}if(message.action==='clearScrapedData'){clearScrapedData();sendResponse({success:true});return true;}});}else{}};// Initialize the service\ninitScrapingService();", "map": {"version": 3, "names": ["i18n", "t", "bind", "_isScrapingActive", "_scrapedData", "_lastScrapedTimestamp", "_elementMap", "Map", "_clickListener", "_highlightedElements", "Set", "_overlayElements", "isScrapingActive", "setScrapingActive", "active", "generateXPath", "el", "nodeType", "Node", "ELEMENT_NODE", "path", "index", "sibling", "previousElementSibling", "tagName", "unshift", "parentElement", "join", "generateCssSelector", "selector", "toLowerCase", "id", "className", "getElementClassName", "trim", "replace", "baseVal", "getAttribute", "shouldIgnoreHighlight", "element", "classList", "contains", "closest", "startsWith", "shouldIgnoreEvents", "addPersistentHighlightWithoutBlocking", "style", "outline", "outlineOffset", "setAttribute", "add", "addPersistentHighlight", "_element$offsetParent", "overlay", "document", "createElement", "cssText", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "tooltipText", "title", "rect", "getBoundingClientRect", "parentRect", "offsetParent", "top", "left", "window", "scrollY", "scrollX", "parent", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "e", "preventDefault", "stopPropagation", "removeAllHighlights", "for<PERSON>ach", "removeAttribute", "clear", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showClickFeedback", "feedback", "scrapedText", "textContent", "setTimeout", "opacity", "error", "extractElementData", "_element$textContent", "xpath", "cssSelector", "text", "attributes", "Array", "from", "reduce", "acc", "attr", "name", "value", "width", "height", "children", "isVisible", "timestamp", "Date", "toISOString", "url", "location", "href", "handleElementClick", "event", "target", "hasAttribute", "clickedElementData", "elementsToStore", "setScrapedData", "elements", "data", "append", "arguments", "length", "undefined", "isArray", "has", "set", "push", "isDuplicate", "some", "existing", "getScrapedData", "getElementByXPath", "get", "getXPathData", "map", "exportScrapedDataToFile", "accountId", "storedData", "loadScrapedDataFromStorage", "scrapedData", "saveScrapedDataToFile", "saveScrapedDataToStorage", "getScrapedDataCount", "getLastScrapedTimestamp", "clearScrapedData", "chrome", "storage", "local", "storageData", "elementCount", "xpathData", "item", "console", "log", "localStorage", "setItem", "JSON", "stringify", "result", "getItem", "parse", "apiData", "metadata", "exportedAt", "uploadXPathsFile", "formData", "FormData", "jsonBlob", "Blob", "type", "pageTitle", "filename", "toString", "uploadXpathsFile", "Error", "response", "errorMessage", "message", "startScraping", "runtime", "sendMessage", "action", "dispatchEvent", "CustomEvent", "showScrapingInstructions", "stopScraping", "removeEventListener", "hideScrapingInstructions", "instructionDiv", "mainTitle", "clickElement", "onlyClicked", "originalClick", "redBorders", "dataSaved", "innerHTML", "existingInstruction", "getElementById", "remove", "initScrapingService", "onMessage", "addListener", "_sender", "sendResponse", "isActive", "success", "lastTimestamp"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/services/ScrapingService.ts"], "sourcesContent": ["// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality\r\n\r\nimport i18n from '../multilinguial/i18n';\r\n\r\n// Get the translation function directly from i18next\r\nconst t = i18n.t.bind(i18n);\r\n\r\n/**\r\n * Translation keys used in this service:\r\n * \r\n * \"Click Scraping Active\" - Main title for scraping instructions\r\n * \"Click any element to scrape its XPath data\" - Click element instruction\r\n * \"Only the clicked element is scraped no duplicates\" - Only clicked element instruction  \r\n * \"Original click functionality still works\" - Original click functionality instruction\r\n * \"Red borders show scraped elements\" - Red borders instruction\r\n * \"Data is saved to Chrome storage\" - Data saved instruction\r\n * \"Scraped\" - Feedback when element is scraped\r\n * \"Element already scraped click blocked\" - Tooltip for already scraped elements\r\n * \r\n * This service uses the i18n instance directly for translations.\r\n * The translations will automatically fallback to the English keys if translation is missing.\r\n */\r\n\r\n// Global state to track if scraping is active\r\nlet _isScrapingActive = false;\r\nlet _scrapedData: any[] = [];\r\nlet _lastScrapedTimestamp: string = '';\r\nlet _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath\r\nlet _clickListener: ((event: MouseEvent) => void) | null = null;\r\nlet _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements\r\nlet _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking\r\n// Interface for element data\r\nexport interface ElementData {\r\n  tagName: string;\r\n  id: string;\r\n  className: string;\r\n  text: string;\r\n  attributes: Record<string, string>;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  rect: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n  children: ElementData[];\r\n  isVisible: boolean;\r\n  timestamp: string;\r\n  url: string; // Add URL field to track which page the element is from\r\n}\r\n\r\n// Interface for scraped page data\r\nexport interface ScrapedPageData {\r\n  url: string;\r\n  title: string;\r\n  timestamp: string;\r\n  elements: ElementData[];\r\n}\r\n\r\n/**\r\n * Check if scraping is currently active\r\n */\r\nexport const isScrapingActive = (): boolean => {\r\n  return _isScrapingActive;\r\n};\r\n\r\n/**\r\n * Set the scraping active state\r\n */\r\nexport const setScrapingActive = (active: boolean): void => {\r\n  _isScrapingActive = active;\r\n};\r\n\r\n/**\r\n * Generate XPath for an element\r\n */\r\nconst generateXPath = (el: HTMLElement): string => {\r\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let index = 1;\r\n    let sibling = el.previousElementSibling;\r\n    while (sibling) {\r\n      if (sibling.tagName === el.tagName) index++;\r\n      sibling = sibling.previousElementSibling;\r\n    }\r\n    path.unshift(`${el.tagName}[${index}]`);\r\n    el = el.parentElement!;\r\n  }\r\n  return '/' + path.join('/');\r\n};\r\n\r\n/**\r\n * Generate CSS selector for an element\r\n */\r\nconst generateCssSelector = (el: HTMLElement): string => {\r\n  if (!el) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = el.tagName.toLowerCase();\r\n    if (el.id) {\r\n      selector += `#${el.id}`;\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      // Safely handle className - it might be a string or SVGAnimatedString\r\n      const className = getElementClassName(el);\r\n      if (className) {\r\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\r\n      }\r\n      path.unshift(selector);\r\n      el = el.parentElement!;\r\n    }\r\n  }\r\n  return path.join(' > ');\r\n};\r\n\r\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\r\nconst getElementClassName = (el: Element): string => {\r\n  if (!el) return '';\r\n\r\n  // For HTML elements, className is usually a string\r\n  if (typeof el.className === 'string') {\r\n    return el.className;\r\n  }\r\n\r\n  // For SVG elements, className might be an SVGAnimatedString\r\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\r\n    return (el.className as any).baseVal || '';\r\n  }\r\n\r\n  // Fallback: try to get class attribute directly\r\n  return el.getAttribute('class') || '';\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for highlighting\r\n */\r\nconst shouldIgnoreHighlight = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#my-react-drawer\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#image-popover\") ||\r\n    !!element.closest(\"#toggle-fit\") ||\r\n    !!element.closest(\"#color-popover\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#rte-alignment\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-font\") ||\r\n    !!element.closest(\"#rte-bold\") ||\r\n    !!element.closest(\"#rte-italic\") ||\r\n    !!element.closest(\"#rte-underline\") ||\r\n    !!element.closest(\"#rte-strke-through\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#rte-more\") ||\r\n    !!element.closest(\"#rte-text-color\") ||\r\n    !!element.closest(\"#rte-text-color-popover\") ||\r\n    !!element.closest(\"#rte-text-highlight\") ||\r\n    !!element.closest(\"#rte-text-highlight-pop\") ||\r\n    !!element.closest(\"#rte-text-heading\") ||\r\n    !!element.closest(\"#rte-text-heading-menu-items\") ||\r\n    !!element.closest(\"#rte-text-format\") ||\r\n    !!element.closest(\"#rte-text-ul\") ||\r\n    !!element.closest(\"#rte-text-hyperlink\") ||\r\n    !!element.closest(\"#rte-video\") ||\r\n    !!element.closest(\"#rte-clear-formatting\") ||\r\n    !!element.closest(\"#rte-hyperlink-popover\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#image-properties\") ||\r\n    !!element.closest(\"#rte-toolbar\") ||\r\n    !!element.closest(\"#tooltipdialog\") ||\r\n    !!element.closest(\"#rte-toolbar-paper\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\r\n  );\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for events\r\n */\r\nconst shouldIgnoreEvents = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") // Ignore our own instruction banner\r\n  );\r\n};\r\n\r\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\r\nconst addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // No overlay creation - allow clicks to pass through\r\n};\r\n\r\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\r\nconst addPersistentHighlight = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // Create click-blocking overlay\r\n  const overlay = document.createElement('div');\r\n  overlay.style.cssText = `\r\n    position: absolute;\r\n    top: ${element.offsetTop}px;\r\n    left: ${element.offsetLeft}px;\r\n    width: ${element.offsetWidth}px;\r\n    height: ${element.offsetHeight}px;\r\n    background: transparent;\r\n    z-index: 999999;\r\n    pointer-events: auto;\r\n    cursor: not-allowed;\r\n  `;\r\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\r\n  \r\n  const tooltipText = t('Element already scraped click blocked');\r\n  overlay.title = tooltipText;\r\n\r\n  // Position overlay relative to the element's parent\r\n  const rect = element.getBoundingClientRect();\r\n  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };\r\n\r\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\r\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\r\n\r\n  // Add overlay to the element's parent or body\r\n  const parent = element.offsetParent || document.body;\r\n  parent.appendChild(overlay);\r\n  _overlayElements.add(overlay);\r\n\r\n  // Block clicks on the overlay\r\n  overlay.addEventListener('click', (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  }, true);\r\n};\r\n\r\n/**\r\n * Remove all highlights and overlays\r\n */\r\nconst removeAllHighlights = (): void => {\r\n  // Remove highlights\r\n  _highlightedElements.forEach(element => {\r\n    if (element && element.style) {\r\n      element.style.outline = '';\r\n      element.style.outlineOffset = '';\r\n      element.removeAttribute('data-quickadapt-highlighted');\r\n    }\r\n  });\r\n  _highlightedElements.clear();\r\n\r\n  // Remove overlays\r\n  _overlayElements.forEach(overlay => {\r\n    if (overlay && overlay.parentNode) {\r\n      overlay.parentNode.removeChild(overlay);\r\n    }\r\n  });\r\n  _overlayElements.clear();\r\n};\r\n\r\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\r\nconst showClickFeedback = (element: HTMLElement): void => {\r\n  try {\r\n    // Create a temporary feedback indicator\r\n    const feedback = document.createElement('div');\r\n    feedback.style.cssText = `\r\n      position: absolute;\r\n      background: #4CAF50;\r\n      color: white;\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      font-weight: bold;\r\n      z-index: 10001;\r\n      pointer-events: none;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\r\n      opacity: 0;\r\n      transition: opacity 0.2s ease;\r\n    `;\r\n    \r\n    const scrapedText = `✓ ${t('Scraped')}`;\r\n    feedback.textContent = scrapedText;\r\n\r\n    // Position the feedback near the clicked element\r\n    const rect = element.getBoundingClientRect();\r\n    feedback.style.left = `${rect.left + window.scrollX}px`;\r\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\r\n\r\n    document.body.appendChild(feedback);\r\n\r\n    // Animate in\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '1';\r\n    }, 10);\r\n\r\n    // Remove after 2 seconds\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '0';\r\n      setTimeout(() => {\r\n        if (feedback.parentNode) {\r\n          feedback.parentNode.removeChild(feedback);\r\n        }\r\n      }, 200);\r\n    }, 2000);\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\r\nconst extractElementData = (element: HTMLElement): ElementData => {\r\n  const rect = element.getBoundingClientRect();\r\n  const xpath = generateXPath(element);\r\n  const cssSelector = generateCssSelector(element);\r\n\r\n  return {\r\n    tagName: element.tagName,\r\n    id: element.id || '',\r\n    className: getElementClassName(element),\r\n    text: element.textContent?.trim() || '',\r\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\r\n      acc[attr.name] = attr.value;\r\n      return acc;\r\n    }, {} as Record<string, string>),\r\n    xpath,\r\n    cssSelector,\r\n    rect: {\r\n      top: rect.top,\r\n      left: rect.left,\r\n      width: rect.width,\r\n      height: rect.height\r\n    },\r\n    children: [], // We don't need children for click-based scraping\r\n    isVisible: rect.width > 0 && rect.height > 0,\r\n    timestamp: new Date().toISOString(),\r\n    url: window.location.href // Add URL to each element\r\n  };\r\n};\r\n\r\n/**\r\n * Handle click events for element scraping\r\n */\r\nconst handleElementClick = (event: MouseEvent): void => {\r\n  try {\r\n    // IMPORTANT: Don't prevent default or stop propagation\r\n    // This allows the original click functionality to work normally\r\n    // (navigation, form submission, button clicks, etc.)\r\n\r\n    const target = event.target as HTMLElement;\r\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\r\n      return;\r\n    }\r\n\r\n    if (shouldIgnoreEvents(target)) {\r\n      return;\r\n    }\r\n\r\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\r\n      return; \r\n    }\r\n\r\n\r\n    // Extract data from clicked element ONLY\r\n    const clickedElementData = extractElementData(target);\r\n\r\n    // Store only the clicked element data (no parent element)\r\n    const elementsToStore = [clickedElementData];\r\n\r\n    // Add to scraped data\r\n    setScrapedData({ elements: elementsToStore }, true);\r\n\r\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\r\n    addPersistentHighlightWithoutBlocking(target);\r\n\r\n\r\n    // // Show brief success feedback\r\n    // showClickFeedback(target);\r\n\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\nexport const setScrapedData = (data: any, append: boolean = false): void => {\r\n  const timestamp = new Date().toISOString();\r\n  _lastScrapedTimestamp = timestamp;\r\n\r\n  if (!append) {\r\n    // Clear existing data if not appending\r\n    _scrapedData = [];\r\n    _elementMap.clear();\r\n  }\r\n\r\n  // Process each element in the data\r\n  if (data && data.elements && Array.isArray(data.elements)) {\r\n    data.elements.forEach((element: ElementData) => {\r\n      // Add timestamp to the element\r\n      element.timestamp = timestamp;\r\n\r\n      // Use XPath as a unique identifier for the element\r\n      if (element.xpath) {\r\n        // If element already exists in the map, don't add it again (prevent duplicates)\r\n        if (_elementMap.has(element.xpath)) {\r\n          return; // Skip this element\r\n        } else {\r\n          // New element, add to map and data array\r\n          _elementMap.set(element.xpath, element);\r\n          _scrapedData.push(element);\r\n        \r\n        }\r\n      } else {\r\n        // No XPath, check for duplicates by other means (tagName + id + className)\r\n        const isDuplicate = _scrapedData.some(existing =>\r\n          existing.tagName === element.tagName &&\r\n          existing.id === element.id &&\r\n          existing.className === element.className\r\n        );\r\n\r\n        if (!isDuplicate) {\r\n          _scrapedData.push(element);\r\n\r\n        } else {\r\n\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Get the currently scraped data\r\n */\r\nexport const getScrapedData = (): any[] => {\r\n  return _scrapedData;\r\n};\r\n\r\n/**\r\n * Get element by XPath\r\n */\r\nexport const getElementByXPath = (xpath: string): ElementData | undefined => {\r\n  return _elementMap.get(xpath);\r\n};\r\n\r\n\r\n\r\n/**\r\n * Get all xpath data from scraped elements\r\n */\r\nexport const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {\r\n  return _scrapedData.map(element => ({\r\n    xpath: element.xpath,\r\n    tagName: element.tagName,\r\n    id: element.id,\r\n    className: element.className,\r\n    text: element.text,\r\n    timestamp: element.timestamp,\r\n    url: element.url\r\n  }));\r\n};\r\n\r\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\r\nexport const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n    if (_scrapedData.length === 0) {\r\n      // Try to load from storage if no current data\r\n      const storedData = await loadScrapedDataFromStorage();\r\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\r\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\r\n        return;\r\n      }\r\n      // Use stored data for API call\r\n      await saveScrapedDataToFile(accountId);\r\n    } else {\r\n      // Save current data to storage first, then send to API\r\n      await saveScrapedDataToStorage();\r\n      await saveScrapedDataToFile(accountId);\r\n    }\r\n  } catch (error) {\r\n    // alert('Error sending scraped data to backend API. Check console for details.');\r\n  }\r\n};\r\n\r\n/**\r\n * Get scraped data count\r\n */\r\nexport const getScrapedDataCount = (): number => {\r\n  return _scrapedData.length;\r\n};\r\n\r\n/**\r\n * Get the timestamp of the last scrape\r\n */\r\nexport const getLastScrapedTimestamp = (): string => {\r\n  return _lastScrapedTimestamp;\r\n};\r\n\r\n/**\r\n * Clear scraped data\r\n */\r\nexport const clearScrapedData = (): void => {\r\n  _scrapedData = [];\r\n  _lastScrapedTimestamp = '';\r\n};\r\n\r\n/**\r\n * Save scraped data to Chrome storage\r\n */\r\nexport const saveScrapedDataToStorage = async (): Promise<void> => {\r\n  try {\r\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n        }))\r\n      };\r\n\r\n      await chrome.storage.local.set({\r\n        'quickadapt-scraped-data': storageData\r\n      });\r\n   storageData.xpathData.forEach((item, index) => {\r\n        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\r\n      });\r\n    } else {\r\n      // Fallback: save to localStorage\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n        }))\r\n      };\r\n      localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\r\n    }\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Load scraped data from Chrome storage\r\n */\r\nexport const loadScrapedDataFromStorage = async (): Promise<any> => {\r\n  try {\r\n    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {\r\n      const result = await chrome.storage.local.get('quickadapt-scraped-data');\r\n      return result['quickadapt-scraped-data'] || null;\r\n    } else {\r\n      // Fallback: load from localStorage\r\n      const data = localStorage.getItem('quickadapt-scraped-data');\r\n      return data ? JSON.parse(data) : null;\r\n    }\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\r\nexport const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    const storedData = await loadScrapedDataFromStorage();\r\n\r\n    if (!storedData) {\r\n      return;\r\n    }\r\n\r\n    const apiData = {\r\n      metadata: {\r\n        url: storedData.url || window.location.href,\r\n        title: storedData.title || document.title,\r\n        timestamp: storedData.timestamp || new Date().toISOString(),\r\n        elementCount: storedData.elementCount || 0,\r\n        exportedAt: new Date().toISOString()\r\n      },\r\n      elements: storedData.scrapedData || [],\r\n      xpathData: storedData.xpathData || []\r\n    };\r\n\r\n\r\n    // Send data to backend API\r\n    await uploadXPathsFile(apiData, accountId);\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\r\nexport const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    // Convert JSON data to FormData as expected by the existing API\r\n    const formData = new FormData();\r\n\r\n    // Create a JSON file blob\r\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\r\n      type: 'application/json'\r\n    });\r\n\r\n    // Generate filename with timestamp\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\r\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\r\n\r\n    // Add the file to FormData\r\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\r\n\r\n    // Add metadata as form fields if needed\r\n    formData.append('elementCount', data.metadata.elementCount.toString());\r\n    formData.append('url', data.metadata.url);\r\n    formData.append('timestamp', data.metadata.timestamp);\r\n\r\n   \r\n\r\n    // Import and use the existing uploadXpathsFile function\r\n    const { uploadXpathsFile } = await import('./FileService');\r\n\r\n    if (!accountId) {\r\n      throw new Error('Account ID is required to upload XPath data');\r\n    }\r\n\r\n    const response = await uploadXpathsFile(accountId, formData);\r\n\r\n  } catch (error) {\r\n\r\n    // Show error message to user\r\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\r\n\r\n    throw error; // Re-throw to be caught by calling function\r\n  }\r\n};\r\n\r\n/**\r\n * Start click-based scraping process\r\n */\r\nexport const startScraping = (): void => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  clearScrapedData();\r\n\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = handleElementClick;\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Show user instruction with translation support\r\n  showScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Stop click-based scraping process\r\n */\r\nexport const stopScraping = async (accountId: string): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n\r\n  _isScrapingActive = false;\r\n\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Save scraped data to Chrome storage\r\n  if (_scrapedData.length > 0) {\r\n    await saveScrapedDataToStorage();\r\n\r\n    // Get data from Chrome storage and save to file\r\n    await saveScrapedDataToFile(accountId);\r\n  }\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Show scraping instructions to user\r\n */\r\nconst showScrapingInstructions = (): void => {\r\n  // Remove existing instruction if any\r\n  hideScrapingInstructions();\r\n\r\n  const instructionDiv = document.createElement('div');\r\n  instructionDiv.id = 'quickadapt-scraping-instructions';\r\n  instructionDiv.style.cssText = `\r\n    position: fixed;\r\n    top: 20px;\r\n    right: 20px;\r\n    background: #4CAF50;\r\n    color: white;\r\n    padding: 15px 20px;\r\n    border-radius: 8px;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 14px;\r\n    font-weight: bold;\r\n    z-index: 10000;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n    max-width: 320px;\r\n    text-align: center;\r\n  `;\r\n  \r\n  // Use translations with i18n instance directly\r\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\r\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\r\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\r\n  const originalClick = `• ${t('Original click functionality still works')}`;\r\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\r\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\r\n  \r\n  instructionDiv.innerHTML = `\r\n    ${mainTitle}<br>\r\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\r\n      ${clickElement}<br>\r\n      ${onlyClicked}<br>\r\n      ${originalClick}<br>\r\n      ${redBorders}<br>\r\n      ${dataSaved}\r\n    </small>\r\n  `;\r\n\r\n  document.body.appendChild(instructionDiv);\r\n\r\n  // Auto-hide after 8 seconds\r\n  setTimeout(() => {\r\n    if (instructionDiv.parentNode) {\r\n      instructionDiv.style.opacity = '0.7';\r\n    }\r\n  }, 8000);\r\n};\r\n\r\n/**\r\n * Hide scraping instructions\r\n */\r\nconst hideScrapingInstructions = (): void => {\r\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\r\n  if (existingInstruction) {\r\n    existingInstruction.remove();\r\n  }\r\n};\r\n\r\n\r\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\r\nexport const initScrapingService = (): void => {\r\n  _isScrapingActive = false;\r\n  _scrapedData = [];\r\n  _elementMap.clear();\r\n  _lastScrapedTimestamp = '';\r\n  _clickListener = null;\r\n\r\n  // Check if we're in a Chrome extension environment\r\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\r\n    // Listen for messages from background script\r\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\r\n      if (message.action === 'updateScrapingState') {\r\n        _isScrapingActive = message.isActive;\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapingState') {\r\n        sendResponse({\r\n          isActive: _isScrapingActive,\r\n          lastTimestamp: _lastScrapedTimestamp,\r\n          elementCount: _scrapedData.length\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapedData') {\r\n        sendResponse({\r\n          data: _scrapedData,\r\n          timestamp: _lastScrapedTimestamp\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'clearScrapedData') {\r\n        clearScrapedData();\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n    });\r\n  } else {\r\n  }\r\n};\r\n\r\n\r\n// Initialize the service\r\ninitScrapingService();\r\n"], "mappings": "AAAA;AAEA,MAAO,CAAAA,IAAI,KAAM,uBAAuB,CAExC;AACA,KAAM,CAAAC,CAAC,CAAGD,IAAI,CAACC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC,CAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAEA;AACA,GAAI,CAAAG,iBAAiB,CAAG,KAAK,CAC7B,GAAI,CAAAC,YAAmB,CAAG,EAAE,CAC5B,GAAI,CAAAC,qBAA6B,CAAG,EAAE,CACtC,GAAI,CAAAC,WAAqC,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACvD,GAAI,CAAAC,cAAoD,CAAG,IAAI,CAC/D,GAAI,CAAAC,oBAAsC,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxD,GAAI,CAAAC,gBAAkC,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AACpD;AAqBA;AAQA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAe,CAC7C,MAAO,CAAAT,iBAAiB,CAC1B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAU,iBAAiB,CAAIC,MAAe,EAAW,CAC1DX,iBAAiB,CAAGW,MAAM,CAC5B,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,aAAa,CAAIC,EAAe,EAAa,CACjD,GAAI,CAACA,EAAE,EAAIA,EAAE,CAACC,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,MAAO,EAAE,CACvD,KAAM,CAAAC,IAAI,CAAG,EAAE,CACf,MAAOJ,EAAE,EAAIA,EAAE,CAACC,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CAC9C,GAAI,CAAAE,KAAK,CAAG,CAAC,CACb,GAAI,CAAAC,OAAO,CAAGN,EAAE,CAACO,sBAAsB,CACvC,MAAOD,OAAO,CAAE,CACd,GAAIA,OAAO,CAACE,OAAO,GAAKR,EAAE,CAACQ,OAAO,CAAEH,KAAK,EAAE,CAC3CC,OAAO,CAAGA,OAAO,CAACC,sBAAsB,CAC1C,CACAH,IAAI,CAACK,OAAO,CAAC,GAAGT,EAAE,CAACQ,OAAO,IAAIH,KAAK,GAAG,CAAC,CACvCL,EAAE,CAAGA,EAAE,CAACU,aAAc,CACxB,CACA,MAAO,GAAG,CAAGN,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC,CAC7B,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,mBAAmB,CAAIZ,EAAe,EAAa,CACvD,GAAI,CAACA,EAAE,CAAE,MAAO,EAAE,CAClB,KAAM,CAAAI,IAAI,CAAG,EAAE,CACf,MAAOJ,EAAE,EAAIA,EAAE,CAACC,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CAC9C,GAAI,CAAAU,QAAQ,CAAGb,EAAE,CAACQ,OAAO,CAACM,WAAW,CAAC,CAAC,CACvC,GAAId,EAAE,CAACe,EAAE,CAAE,CACTF,QAAQ,EAAI,IAAIb,EAAE,CAACe,EAAE,EAAE,CACvBX,IAAI,CAACK,OAAO,CAACI,QAAQ,CAAC,CACtB,MACF,CAAC,IAAM,CACL;AACA,KAAM,CAAAG,SAAS,CAAGC,mBAAmB,CAACjB,EAAE,CAAC,CACzC,GAAIgB,SAAS,CAAE,CACbH,QAAQ,EAAI,GAAG,CAAGG,SAAS,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CACzD,CACAf,IAAI,CAACK,OAAO,CAACI,QAAQ,CAAC,CACtBb,EAAE,CAAGA,EAAE,CAACU,aAAc,CACxB,CACF,CACA,MAAO,CAAAN,IAAI,CAACO,IAAI,CAAC,KAAK,CAAC,CACzB,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAM,mBAAmB,CAAIjB,EAAW,EAAa,CACnD,GAAI,CAACA,EAAE,CAAE,MAAO,EAAE,CAElB;AACA,GAAI,MAAO,CAAAA,EAAE,CAACgB,SAAS,GAAK,QAAQ,CAAE,CACpC,MAAO,CAAAhB,EAAE,CAACgB,SAAS,CACrB,CAEA;AACA,GAAIhB,EAAE,CAACgB,SAAS,EAAI,MAAO,CAAAhB,EAAE,CAACgB,SAAS,GAAK,QAAQ,EAAI,SAAS,EAAI,CAAAhB,EAAE,CAACgB,SAAS,CAAE,CACjF,MAAQ,CAAAhB,EAAE,CAACgB,SAAS,CAASI,OAAO,EAAI,EAAE,CAC5C,CAEA;AACA,MAAO,CAAApB,EAAE,CAACqB,YAAY,CAAC,OAAO,CAAC,EAAI,EAAE,CACvC,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,qBAAqB,CAAIC,OAAoB,EAAc,CAC/D,MACE,CAAAA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5DF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzCF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjDF,OAAO,CAACF,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C,CAAC,CAACE,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,0BAA0B,CAAC,EAC7C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,sBAAsB,CAAC,EACzC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,eAAe,CAAC,EAClC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,EAC9B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,yBAAyB,CAAC,EAC5C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,yBAAyB,CAAC,EAC5C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,8BAA8B,CAAC,EACjD,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,YAAY,CAAC,EAC/B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,uBAAuB,CAAC,EAC1C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,wBAAwB,CAAC,EAC3C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,UAAU,CAAC,EAC7B,CAAC,CAACH,OAAO,CAACG,OAAO,CAACH,OAAO,CAACR,EAAE,CAACY,UAAU,CAAC,WAAW,CAAC,CAAG,IAAIJ,OAAO,CAACR,EAAE,EAAE,CAAG,MAAM,CAAC,EACjF,CAAC,CAACQ,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC,EACnC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iCAAiC,CAAC,EACpD,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,qBAAqB,CAAC,EACxC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mCAAmC,CAAE;AAAA,CAE3D,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAE,kBAAkB,CAAIL,OAAoB,EAAc,CAC5D,MACE,CAAAA,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAC5DF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EACzCF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAChDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAClDF,OAAO,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EACjDF,OAAO,CAACF,YAAY,CAAC,MAAM,CAAC,GAAK,SAAS,EAC1C,CAAC,CAACE,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,0BAA0B,CAAC,EAC7C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,sBAAsB,CAAC,EACzC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iBAAiB,CAAC,EACpC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,eAAe,CAAC,EAClC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mBAAmB,CAAC,EACtC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,aAAa,CAAC,EAChC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC,EACjC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,iCAAiC,CAAC,EACpD,CAAC,CAACH,OAAO,CAACG,OAAO,CAACH,OAAO,CAACR,EAAE,CAACY,UAAU,CAAC,WAAW,CAAC,CAAG,IAAIJ,OAAO,CAACR,EAAE,EAAE,CAAG,MAAM,CAAC,EACjF,CAAC,CAACQ,OAAO,CAACG,OAAO,CAAC,UAAU,CAAC,EAC7B,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,kBAAkB,CAAC,EACrC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,2BAA2B,CAAC,EAC9C,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,oBAAoB,CAAC,EACvC,CAAC,CAACH,OAAO,CAACG,OAAO,CAAC,mCAAmC,CAAE;AAAA,CAE3D,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAG,qCAAqC,CAAIN,OAAoB,EAAW,CAC5E,GAAID,qBAAqB,CAACC,OAAO,CAAC,CAAE,OAEpC;AACAA,OAAO,CAACO,KAAK,CAACC,OAAO,CAAG,8BAA8B,CACtDR,OAAO,CAACO,KAAK,CAACE,aAAa,CAAG,KAAK,CACnCT,OAAO,CAACU,YAAY,CAAC,6BAA6B,CAAE,MAAM,CAAC,CAC3DxC,oBAAoB,CAACyC,GAAG,CAACX,OAAO,CAAC,CAEjC;AACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAY,sBAAsB,CAAIZ,OAAoB,EAAW,KAAAa,qBAAA,CAC7D,GAAId,qBAAqB,CAACC,OAAO,CAAC,CAAE,OAEpC;AACAA,OAAO,CAACO,KAAK,CAACC,OAAO,CAAG,8BAA8B,CACtDR,OAAO,CAACO,KAAK,CAACE,aAAa,CAAG,KAAK,CACnCT,OAAO,CAACU,YAAY,CAAC,6BAA6B,CAAE,MAAM,CAAC,CAC3DxC,oBAAoB,CAACyC,GAAG,CAACX,OAAO,CAAC,CAEjC;AACA,KAAM,CAAAc,OAAO,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CF,OAAO,CAACP,KAAK,CAACU,OAAO,CAAG;AAC1B;AACA,WAAWjB,OAAO,CAACkB,SAAS;AAC5B,YAAYlB,OAAO,CAACmB,UAAU;AAC9B,aAAanB,OAAO,CAACoB,WAAW;AAChC,cAAcpB,OAAO,CAACqB,YAAY;AAClC;AACA;AACA;AACA;AACA,GAAG,CACDP,OAAO,CAACJ,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CAEvD,KAAM,CAAAY,WAAW,CAAG5D,CAAC,CAAC,uCAAuC,CAAC,CAC9DoD,OAAO,CAACS,KAAK,CAAGD,WAAW,CAE3B;AACA,KAAM,CAAAE,IAAI,CAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,UAAU,CAAG,EAAAb,qBAAA,CAAAb,OAAO,CAAC2B,YAAY,UAAAd,qBAAA,iBAApBA,qBAAA,CAAsBY,qBAAqB,CAAC,CAAC,GAAI,CAAEG,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAE,CAAC,CAEvFf,OAAO,CAACP,KAAK,CAACqB,GAAG,CAAG,GAAGJ,IAAI,CAACI,GAAG,CAAGF,UAAU,CAACE,GAAG,CAAGE,MAAM,CAACC,OAAO,IAAI,CACrEjB,OAAO,CAACP,KAAK,CAACsB,IAAI,CAAG,GAAGL,IAAI,CAACK,IAAI,CAAGH,UAAU,CAACG,IAAI,CAAGC,MAAM,CAACE,OAAO,IAAI,CAExE;AACA,KAAM,CAAAC,MAAM,CAAGjC,OAAO,CAAC2B,YAAY,EAAIZ,QAAQ,CAACmB,IAAI,CACpDD,MAAM,CAACE,WAAW,CAACrB,OAAO,CAAC,CAC3B1C,gBAAgB,CAACuC,GAAG,CAACG,OAAO,CAAC,CAE7B;AACAA,OAAO,CAACsB,gBAAgB,CAAC,OAAO,CAAGC,CAAC,EAAK,CACvCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACrB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAY,CACtC;AACAtE,oBAAoB,CAACuE,OAAO,CAACzC,OAAO,EAAI,CACtC,GAAIA,OAAO,EAAIA,OAAO,CAACO,KAAK,CAAE,CAC5BP,OAAO,CAACO,KAAK,CAACC,OAAO,CAAG,EAAE,CAC1BR,OAAO,CAACO,KAAK,CAACE,aAAa,CAAG,EAAE,CAChCT,OAAO,CAAC0C,eAAe,CAAC,6BAA6B,CAAC,CACxD,CACF,CAAC,CAAC,CACFxE,oBAAoB,CAACyE,KAAK,CAAC,CAAC,CAE5B;AACAvE,gBAAgB,CAACqE,OAAO,CAAC3B,OAAO,EAAI,CAClC,GAAIA,OAAO,EAAIA,OAAO,CAAC8B,UAAU,CAAE,CACjC9B,OAAO,CAAC8B,UAAU,CAACC,WAAW,CAAC/B,OAAO,CAAC,CACzC,CACF,CAAC,CAAC,CACF1C,gBAAgB,CAACuE,KAAK,CAAC,CAAC,CAC1B,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAG,iBAAiB,CAAI9C,OAAoB,EAAW,CACxD,GAAI,CACF;AACA,KAAM,CAAA+C,QAAQ,CAAGhC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC9C+B,QAAQ,CAACxC,KAAK,CAACU,OAAO,CAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAED,KAAM,CAAA+B,WAAW,CAAG,KAAKtF,CAAC,CAAC,SAAS,CAAC,EAAE,CACvCqF,QAAQ,CAACE,WAAW,CAAGD,WAAW,CAElC;AACA,KAAM,CAAAxB,IAAI,CAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC,CAC5CsB,QAAQ,CAACxC,KAAK,CAACsB,IAAI,CAAG,GAAGL,IAAI,CAACK,IAAI,CAAGC,MAAM,CAACE,OAAO,IAAI,CACvDe,QAAQ,CAACxC,KAAK,CAACqB,GAAG,CAAG,GAAGJ,IAAI,CAACI,GAAG,CAAGE,MAAM,CAACC,OAAO,CAAG,EAAE,IAAI,CAE1DhB,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACY,QAAQ,CAAC,CAEnC;AACAG,UAAU,CAAC,IAAM,CACfH,QAAQ,CAACxC,KAAK,CAAC4C,OAAO,CAAG,GAAG,CAC9B,CAAC,CAAE,EAAE,CAAC,CAEN;AACAD,UAAU,CAAC,IAAM,CACfH,QAAQ,CAACxC,KAAK,CAAC4C,OAAO,CAAG,GAAG,CAC5BD,UAAU,CAAC,IAAM,CACf,GAAIH,QAAQ,CAACH,UAAU,CAAE,CACvBG,QAAQ,CAACH,UAAU,CAACC,WAAW,CAACE,QAAQ,CAAC,CAC3C,CACF,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOK,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,kBAAkB,CAAIrD,OAAoB,EAAkB,KAAAsD,oBAAA,CAChE,KAAM,CAAA9B,IAAI,CAAGxB,OAAO,CAACyB,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA8B,KAAK,CAAG/E,aAAa,CAACwB,OAAO,CAAC,CACpC,KAAM,CAAAwD,WAAW,CAAGnE,mBAAmB,CAACW,OAAO,CAAC,CAEhD,MAAO,CACLf,OAAO,CAAEe,OAAO,CAACf,OAAO,CACxBO,EAAE,CAAEQ,OAAO,CAACR,EAAE,EAAI,EAAE,CACpBC,SAAS,CAAEC,mBAAmB,CAACM,OAAO,CAAC,CACvCyD,IAAI,CAAE,EAAAH,oBAAA,CAAAtD,OAAO,CAACiD,WAAW,UAAAK,oBAAA,iBAAnBA,oBAAA,CAAqB3D,IAAI,CAAC,CAAC,GAAI,EAAE,CACvC+D,UAAU,CAAEC,KAAK,CAACC,IAAI,CAAC5D,OAAO,CAAC0D,UAAU,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CAC/DD,GAAG,CAACC,IAAI,CAACC,IAAI,CAAC,CAAGD,IAAI,CAACE,KAAK,CAC3B,MAAO,CAAAH,GAAG,CACZ,CAAC,CAAE,CAAC,CAA2B,CAAC,CAChCP,KAAK,CACLC,WAAW,CACXhC,IAAI,CAAE,CACJI,GAAG,CAAEJ,IAAI,CAACI,GAAG,CACbC,IAAI,CAAEL,IAAI,CAACK,IAAI,CACfqC,KAAK,CAAE1C,IAAI,CAAC0C,KAAK,CACjBC,MAAM,CAAE3C,IAAI,CAAC2C,MACf,CAAC,CACDC,QAAQ,CAAE,EAAE,CAAE;AACdC,SAAS,CAAE7C,IAAI,CAAC0C,KAAK,CAAG,CAAC,EAAI1C,IAAI,CAAC2C,MAAM,CAAG,CAAC,CAC5CG,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,GAAG,CAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAK;AAC5B,CAAC,CACH,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,kBAAkB,CAAIC,KAAiB,EAAW,CACtD,GAAI,CACF;AACA;AACA;AAEA,KAAM,CAAAC,MAAM,CAAGD,KAAK,CAACC,MAAqB,CAC1C,GAAI,CAACA,MAAM,EAAI,CAACA,MAAM,CAACpG,QAAQ,EAAIoG,MAAM,CAACpG,QAAQ,GAAKC,IAAI,CAACC,YAAY,CAAE,CACxE,OACF,CAEA,GAAIyB,kBAAkB,CAACyE,MAAM,CAAC,CAAE,CAC9B,OACF,CAEA,GAAIA,MAAM,CAACC,YAAY,CAAC,6BAA6B,CAAC,CAAE,CACtD,OACF,CAGA;AACA,KAAM,CAAAC,kBAAkB,CAAG3B,kBAAkB,CAACyB,MAAM,CAAC,CAErD;AACA,KAAM,CAAAG,eAAe,CAAG,CAACD,kBAAkB,CAAC,CAE5C;AACAE,cAAc,CAAC,CAAEC,QAAQ,CAAEF,eAAgB,CAAC,CAAE,IAAI,CAAC,CAEnD;AACA3E,qCAAqC,CAACwE,MAAM,CAAC,CAG7C;AACA;AAGF,CAAE,MAAO1B,KAAK,CAAE,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA8B,cAAc,CAAG,QAAAA,CAACE,IAAS,CAAoC,IAAlC,CAAAC,MAAe,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC/D,KAAM,CAAAhB,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC1C1G,qBAAqB,CAAGwG,SAAS,CAEjC,GAAI,CAACe,MAAM,CAAE,CACX;AACAxH,YAAY,CAAG,EAAE,CACjBE,WAAW,CAAC4E,KAAK,CAAC,CAAC,CACrB,CAEA;AACA,GAAIyC,IAAI,EAAIA,IAAI,CAACD,QAAQ,EAAIxB,KAAK,CAAC8B,OAAO,CAACL,IAAI,CAACD,QAAQ,CAAC,CAAE,CACzDC,IAAI,CAACD,QAAQ,CAAC1C,OAAO,CAAEzC,OAAoB,EAAK,CAC9C;AACAA,OAAO,CAACsE,SAAS,CAAGA,SAAS,CAE7B;AACA,GAAItE,OAAO,CAACuD,KAAK,CAAE,CACjB;AACA,GAAIxF,WAAW,CAAC2H,GAAG,CAAC1F,OAAO,CAACuD,KAAK,CAAC,CAAE,CAClC,OAAQ;AACV,CAAC,IAAM,CACL;AACAxF,WAAW,CAAC4H,GAAG,CAAC3F,OAAO,CAACuD,KAAK,CAAEvD,OAAO,CAAC,CACvCnC,YAAY,CAAC+H,IAAI,CAAC5F,OAAO,CAAC,CAE5B,CACF,CAAC,IAAM,CACL;AACA,KAAM,CAAA6F,WAAW,CAAGhI,YAAY,CAACiI,IAAI,CAACC,QAAQ,EAC5CA,QAAQ,CAAC9G,OAAO,GAAKe,OAAO,CAACf,OAAO,EACpC8G,QAAQ,CAACvG,EAAE,GAAKQ,OAAO,CAACR,EAAE,EAC1BuG,QAAQ,CAACtG,SAAS,GAAKO,OAAO,CAACP,SACjC,CAAC,CAED,GAAI,CAACoG,WAAW,CAAE,CAChBhI,YAAY,CAAC+H,IAAI,CAAC5F,OAAO,CAAC,CAE5B,CAAC,IAAM,CAEP,CACF,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAgG,cAAc,CAAGA,CAAA,GAAa,CACzC,MAAO,CAAAnI,YAAY,CACrB,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAoI,iBAAiB,CAAI1C,KAAa,EAA8B,CAC3E,MAAO,CAAAxF,WAAW,CAACmI,GAAG,CAAC3C,KAAK,CAAC,CAC/B,CAAC,CAID;AACA;AACA,GACA,MAAO,MAAM,CAAA4C,YAAY,CAAGA,CAAA,GAA4H,CACtJ,MAAO,CAAAtI,YAAY,CAACuI,GAAG,CAACpG,OAAO,GAAK,CAClCuD,KAAK,CAAEvD,OAAO,CAACuD,KAAK,CACpBtE,OAAO,CAAEe,OAAO,CAACf,OAAO,CACxBO,EAAE,CAAEQ,OAAO,CAACR,EAAE,CACdC,SAAS,CAAEO,OAAO,CAACP,SAAS,CAC5BgE,IAAI,CAAEzD,OAAO,CAACyD,IAAI,CAClBa,SAAS,CAAEtE,OAAO,CAACsE,SAAS,CAC5BG,GAAG,CAAEzE,OAAO,CAACyE,GACf,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA4B,uBAAuB,CAAG,KAAO,CAAAC,SAAkB,EAAoB,CAClF,GAAI,CACF,GAAIzI,YAAY,CAAC0H,MAAM,GAAK,CAAC,CAAE,CAC7B;AACA,KAAM,CAAAgB,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CACrD,GAAI,CAACD,UAAU,EAAI,CAACA,UAAU,CAACE,WAAW,EAAIF,UAAU,CAACE,WAAW,CAAClB,MAAM,GAAK,CAAC,CAAE,CACjF;AACA,OACF,CACA;AACA,KAAM,CAAAmB,qBAAqB,CAACJ,SAAS,CAAC,CACxC,CAAC,IAAM,CACL;AACA,KAAM,CAAAK,wBAAwB,CAAC,CAAC,CAChC,KAAM,CAAAD,qBAAqB,CAACJ,SAAS,CAAC,CACxC,CACF,CAAE,MAAOlD,KAAK,CAAE,CACd;AAAA,CAEJ,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAwD,mBAAmB,CAAGA,CAAA,GAAc,CAC/C,MAAO,CAAA/I,YAAY,CAAC0H,MAAM,CAC5B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAsB,uBAAuB,CAAGA,CAAA,GAAc,CACnD,MAAO,CAAA/I,qBAAqB,CAC9B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAgJ,gBAAgB,CAAGA,CAAA,GAAY,CAC1CjJ,YAAY,CAAG,EAAE,CACjBC,qBAAqB,CAAG,EAAE,CAC5B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA6I,wBAAwB,CAAG,KAAAA,CAAA,GAA2B,CACjE,GAAI,CACF,GAAI,MAAO,CAAAI,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,EAAID,MAAM,CAACC,OAAO,CAACC,KAAK,CAAE,CAC3E,KAAM,CAAAC,WAAW,CAAG,CAClBT,WAAW,CAAE5I,YAAY,CACzByG,SAAS,CAAExG,qBAAqB,CAChC2G,GAAG,CAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI,CACzBpD,KAAK,CAAER,QAAQ,CAACQ,KAAK,CACrB4F,YAAY,CAAEtJ,YAAY,CAAC0H,MAAM,CACjC6B,SAAS,CAAEvJ,YAAY,CAACuI,GAAG,CAACpG,OAAO,GAAK,CACtCuD,KAAK,CAAEvD,OAAO,CAACuD,KAAK,CACpBtE,OAAO,CAAEe,OAAO,CAACf,OAAO,CACxBO,EAAE,CAAEQ,OAAO,CAACR,EAAE,CACdC,SAAS,CAAEO,OAAO,CAACP,SAAS,CAC5BgE,IAAI,CAAEzD,OAAO,CAACyD,IAAI,CAClBC,UAAU,CAAE1D,OAAO,CAAC0D,UAAU,CAC9BY,SAAS,CAAEtE,OAAO,CAACsE,SAAS,CAC5BG,GAAG,CAAEzE,OAAO,CAACyE,GACf,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAsC,MAAM,CAACC,OAAO,CAACC,KAAK,CAACtB,GAAG,CAAC,CAC7B,yBAAyB,CAAEuB,WAC7B,CAAC,CAAC,CACLA,WAAW,CAACE,SAAS,CAAC3E,OAAO,CAAC,CAAC4E,IAAI,CAAEvI,KAAK,GAAK,CAC1CwI,OAAO,CAACC,GAAG,CAAC,GAAGzI,KAAK,CAAG,CAAC,KAAKuI,IAAI,CAACpI,OAAO,MAAMoI,IAAI,CAAC9D,KAAK,EAAE,CAAC,CAC9D,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAA2D,WAAW,CAAG,CAClBT,WAAW,CAAE5I,YAAY,CACzByG,SAAS,CAAExG,qBAAqB,CAChC2G,GAAG,CAAE3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI,CACzBpD,KAAK,CAAER,QAAQ,CAACQ,KAAK,CACrB4F,YAAY,CAAEtJ,YAAY,CAAC0H,MAAM,CACjC6B,SAAS,CAAEvJ,YAAY,CAACuI,GAAG,CAACpG,OAAO,GAAK,CACtCuD,KAAK,CAAEvD,OAAO,CAACuD,KAAK,CACpBtE,OAAO,CAAEe,OAAO,CAACf,OAAO,CACxBO,EAAE,CAAEQ,OAAO,CAACR,EAAE,CACdC,SAAS,CAAEO,OAAO,CAACP,SAAS,CAC5BgE,IAAI,CAAEzD,OAAO,CAACyD,IAAI,CAClBC,UAAU,CAAE1D,OAAO,CAAC0D,UAAU,CAC9BY,SAAS,CAAEtE,OAAO,CAACsE,SAAS,CAC5BG,GAAG,CAAEzE,OAAO,CAACyE,GACf,CAAC,CAAC,CACJ,CAAC,CACD+C,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAEC,IAAI,CAACC,SAAS,CAACT,WAAW,CAAC,CAAC,CAC9E,CACF,CAAE,MAAO9D,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAoD,0BAA0B,CAAG,KAAAA,CAAA,GAA0B,CAClE,GAAI,CACF,GAAI,MAAO,CAAAO,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACC,OAAO,EAAID,MAAM,CAACC,OAAO,CAACC,KAAK,CAAE,CAC3E,KAAM,CAAAW,MAAM,CAAG,KAAM,CAAAb,MAAM,CAACC,OAAO,CAACC,KAAK,CAACf,GAAG,CAAC,yBAAyB,CAAC,CACxE,MAAO,CAAA0B,MAAM,CAAC,yBAAyB,CAAC,EAAI,IAAI,CAClD,CAAC,IAAM,CACL;AACA,KAAM,CAAAxC,IAAI,CAAGoC,YAAY,CAACK,OAAO,CAAC,yBAAyB,CAAC,CAC5D,MAAO,CAAAzC,IAAI,CAAGsC,IAAI,CAACI,KAAK,CAAC1C,IAAI,CAAC,CAAG,IAAI,CACvC,CACF,CAAE,MAAOhC,KAAK,CAAE,CACd,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAsD,qBAAqB,CAAG,KAAO,CAAAJ,SAAkB,EAAoB,CAChF,GAAI,CAEF,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAC,0BAA0B,CAAC,CAAC,CAErD,GAAI,CAACD,UAAU,CAAE,CACf,OACF,CAEA,KAAM,CAAAwB,OAAO,CAAG,CACdC,QAAQ,CAAE,CACRvD,GAAG,CAAE8B,UAAU,CAAC9B,GAAG,EAAI3C,MAAM,CAAC4C,QAAQ,CAACC,IAAI,CAC3CpD,KAAK,CAAEgF,UAAU,CAAChF,KAAK,EAAIR,QAAQ,CAACQ,KAAK,CACzC+C,SAAS,CAAEiC,UAAU,CAACjC,SAAS,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC3D2C,YAAY,CAAEZ,UAAU,CAACY,YAAY,EAAI,CAAC,CAC1Cc,UAAU,CAAE,GAAI,CAAA1D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACrC,CAAC,CACDW,QAAQ,CAAEoB,UAAU,CAACE,WAAW,EAAI,EAAE,CACtCW,SAAS,CAAEb,UAAU,CAACa,SAAS,EAAI,EACrC,CAAC,CAGD;AACA,KAAM,CAAAc,gBAAgB,CAACH,OAAO,CAAEzB,SAAS,CAAC,CAE5C,CAAE,MAAOlD,KAAK,CAAE,CAChB,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA8E,gBAAgB,CAAG,KAAAA,CAAO9C,IAAS,CAAEkB,SAAkB,GAAoB,CACtF,GAAI,CAEF;AACA,KAAM,CAAA6B,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACZ,IAAI,CAACC,SAAS,CAACvC,IAAI,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAAE,CACzDmD,IAAI,CAAE,kBACR,CAAC,CAAC,CAEF;AACA,KAAM,CAAAjE,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC5E,OAAO,CAAC,OAAO,CAAE,GAAG,CAAC,CAChE,KAAM,CAAA4I,SAAS,CAAG,CAACpD,IAAI,CAAC4C,QAAQ,CAACzG,KAAK,EAAI,cAAc,EAAE3B,OAAO,CAAC,eAAe,CAAE,GAAG,CAAC,CACvF,KAAM,CAAA6I,QAAQ,CAAG,yBAAyBD,SAAS,IAAIlE,SAAS,OAAO,CAEvE;AACA6D,QAAQ,CAAC9C,MAAM,CAAC,SAAS,CAAEgD,QAAQ,CAAEI,QAAQ,CAAC,CAAE;AAEhD;AACAN,QAAQ,CAAC9C,MAAM,CAAC,cAAc,CAAED,IAAI,CAAC4C,QAAQ,CAACb,YAAY,CAACuB,QAAQ,CAAC,CAAC,CAAC,CACtEP,QAAQ,CAAC9C,MAAM,CAAC,KAAK,CAAED,IAAI,CAAC4C,QAAQ,CAACvD,GAAG,CAAC,CACzC0D,QAAQ,CAAC9C,MAAM,CAAC,WAAW,CAAED,IAAI,CAAC4C,QAAQ,CAAC1D,SAAS,CAAC,CAIrD;AACA,KAAM,CAAEqE,gBAAiB,CAAC,CAAG,KAAM,OAAM,CAAC,eAAe,CAAC,CAE1D,GAAI,CAACrC,SAAS,CAAE,CACd,KAAM,IAAI,CAAAsC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CAEA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAF,gBAAgB,CAACrC,SAAS,CAAE6B,QAAQ,CAAC,CAE9D,CAAE,MAAO/E,KAAK,CAAE,CAEd;AACA,KAAM,CAAA0F,YAAY,CAAG1F,KAAK,WAAY,CAAAwF,KAAK,CAAGxF,KAAK,CAAC2F,OAAO,CAAG,wBAAwB,CAEtF,KAAM,CAAA3F,KAAK,CAAE;AACf,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA4F,aAAa,CAAGA,CAAA,GAAY,CACvC,GAAIpL,iBAAiB,CAAE,OAEvBA,iBAAiB,CAAG,IAAI,CACxBkJ,gBAAgB,CAAC,CAAC,CAGlB;AACA,GAAI,CAAC7I,cAAc,CAAE,CACnBA,cAAc,CAAG2G,kBAAkB,CACnC7D,QAAQ,CAACqB,gBAAgB,CAAC,OAAO,CAAEnE,cAAc,CAAE,IAAI,CAAC,CAAE;AAC5D,CAEA;AACA,GAAI,MAAO,CAAA8I,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACkC,OAAO,CAAE,CACnD,GAAI,CACFlC,MAAM,CAACkC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,oBACV,CAAC,CAAC,CACJ,CAAE,MAAO/F,KAAK,CAAE,CACd;AACAtB,MAAM,CAACsH,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAC1E,CACF,CAEA;AACAC,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,YAAY,CAAG,KAAO,CAAAjD,SAAiB,EAAoB,CACtE,GAAI,CAAC1I,iBAAiB,CAAE,OAExBA,iBAAiB,CAAG,KAAK,CAGzB;AACA,GAAIK,cAAc,CAAE,CAClB8C,QAAQ,CAACyI,mBAAmB,CAAC,OAAO,CAAEvL,cAAc,CAAE,IAAI,CAAC,CAC3DA,cAAc,CAAG,IAAI,CACvB,CAEA;AACA,GAAIJ,YAAY,CAAC0H,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAoB,wBAAwB,CAAC,CAAC,CAEhC;AACA,KAAM,CAAAD,qBAAqB,CAACJ,SAAS,CAAC,CACxC,CAEA;AACA9D,mBAAmB,CAAC,CAAC,CAErB;AACA,GAAI,MAAO,CAAAuE,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACkC,OAAO,CAAE,CACnD,GAAI,CACFlC,MAAM,CAACkC,OAAO,CAACC,WAAW,CAAC,CACzBC,MAAM,CAAE,mBACV,CAAC,CAAC,CACJ,CAAE,MAAO/F,KAAK,CAAE,CACd;AACAtB,MAAM,CAACsH,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,gCAAgC,CAAC,CAAC,CACzE,CACF,CAEA;AACAI,wBAAwB,CAAC,CAAC,CAC5B,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAH,wBAAwB,CAAGA,CAAA,GAAY,CAC3C;AACAG,wBAAwB,CAAC,CAAC,CAE1B,KAAM,CAAAC,cAAc,CAAG3I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CACpD0I,cAAc,CAAClK,EAAE,CAAG,kCAAkC,CACtDkK,cAAc,CAACnJ,KAAK,CAACU,OAAO,CAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAED;AACA,KAAM,CAAA0I,SAAS,CAAG,MAAMjM,CAAC,CAAC,uBAAuB,CAAC,EAAE,CACpD,KAAM,CAAAkM,YAAY,CAAG,KAAKlM,CAAC,CAAC,4CAA4C,CAAC,EAAE,CAC3E,KAAM,CAAAmM,WAAW,CAAG,KAAKnM,CAAC,CAAC,mDAAmD,CAAC,EAAE,CACjF,KAAM,CAAAoM,aAAa,CAAG,KAAKpM,CAAC,CAAC,0CAA0C,CAAC,EAAE,CAC1E,KAAM,CAAAqM,UAAU,CAAG,KAAKrM,CAAC,CAAC,mCAAmC,CAAC,EAAE,CAChE,KAAM,CAAAsM,SAAS,CAAG,KAAKtM,CAAC,CAAC,iCAAiC,CAAC,EAAE,CAE7DgM,cAAc,CAACO,SAAS,CAAG;AAC7B,MAAMN,SAAS;AACf;AACA,QAAQC,YAAY;AACpB,QAAQC,WAAW;AACnB,QAAQC,aAAa;AACrB,QAAQC,UAAU;AAClB,QAAQC,SAAS;AACjB;AACA,GAAG,CAEDjJ,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACuH,cAAc,CAAC,CAEzC;AACAxG,UAAU,CAAC,IAAM,CACf,GAAIwG,cAAc,CAAC9G,UAAU,CAAE,CAC7B8G,cAAc,CAACnJ,KAAK,CAAC4C,OAAO,CAAG,KAAK,CACtC,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAsG,wBAAwB,CAAGA,CAAA,GAAY,CAC3C,KAAM,CAAAS,mBAAmB,CAAGnJ,QAAQ,CAACoJ,cAAc,CAAC,kCAAkC,CAAC,CACvF,GAAID,mBAAmB,CAAE,CACvBA,mBAAmB,CAACE,MAAM,CAAC,CAAC,CAC9B,CACF,CAAC,CAGD;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAY,CAC7CzM,iBAAiB,CAAG,KAAK,CACzBC,YAAY,CAAG,EAAE,CACjBE,WAAW,CAAC4E,KAAK,CAAC,CAAC,CACnB7E,qBAAqB,CAAG,EAAE,CAC1BG,cAAc,CAAG,IAAI,CAErB;AACA,GAAI,MAAO,CAAA8I,MAAM,GAAK,WAAW,EAAIA,MAAM,CAACkC,OAAO,EAAIlC,MAAM,CAACkC,OAAO,CAACqB,SAAS,CAAE,CAC/E;AACAvD,MAAM,CAACkC,OAAO,CAACqB,SAAS,CAACC,WAAW,CAAC,CAACxB,OAAO,CAAEyB,OAAO,CAAEC,YAAY,GAAK,CACvE,GAAI1B,OAAO,CAACI,MAAM,GAAK,qBAAqB,CAAE,CAC5CvL,iBAAiB,CAAGmL,OAAO,CAAC2B,QAAQ,CACpCD,YAAY,CAAC,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAC/B,MAAO,KAAI,CACb,CAEA,GAAI5B,OAAO,CAACI,MAAM,GAAK,kBAAkB,CAAE,CACzCsB,YAAY,CAAC,CACXC,QAAQ,CAAE9M,iBAAiB,CAC3BgN,aAAa,CAAE9M,qBAAqB,CACpCqJ,YAAY,CAAEtJ,YAAY,CAAC0H,MAC7B,CAAC,CAAC,CACF,MAAO,KAAI,CACb,CAEA,GAAIwD,OAAO,CAACI,MAAM,GAAK,gBAAgB,CAAE,CACvCsB,YAAY,CAAC,CACXrF,IAAI,CAAEvH,YAAY,CAClByG,SAAS,CAAExG,qBACb,CAAC,CAAC,CACF,MAAO,KAAI,CACb,CAEA,GAAIiL,OAAO,CAACI,MAAM,GAAK,kBAAkB,CAAE,CACzCrC,gBAAgB,CAAC,CAAC,CAClB2D,YAAY,CAAC,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAC/B,MAAO,KAAI,CACb,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACP,CACF,CAAC,CAGD;AACAN,mBAAmB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}