{"ast": null, "code": "import React from\"react\";import{Dialog,<PERSON>alogActions,<PERSON><PERSON>,<PERSON>alogTitle,DialogContent,DialogContentText}from\"@mui/material\";import WarningIcon from\"@mui/icons-material/Warning\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AlertPopup=_ref=>{let{openWarning,setopenWarning,handleLeave}=_ref;const{t:translate}=useTranslation();return/*#__PURE__*/_jsxs(Dialog,{sx:{zIndex:9999999},open:openWarning,onClose:()=>setopenWarning(false),PaperProps:{style:{borderRadius:\"4px\",maxWidth:\"400px\",textAlign:\"center\",maxHeight:\"300px\",boxShadow:\"none\"}},className:\"qadpt-alertpopup\",children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{padding:0},children:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\",padding:\"10px\"},children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:\"#e4b6b0\",borderRadius:\"50%\",width:\"40px\",height:\"40px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(WarningIcon,{sx:{color:\"#F44336\",height:\"20px\",width:\"20px\"}})})})}),/*#__PURE__*/_jsx(DialogContent,{sx:{padding:\"20px !important\"},children:/*#__PURE__*/_jsx(DialogContentText,{style:{fontSize:\"14px\",color:\"#000\"},children:translate(\"You Will Loose Changes, If you Leave With Out Saving\")})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:\"space-between\",borderTop:\"1px solid var(--border-color)\"},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setopenWarning(false),sx:{color:\"#9E9E9E\",border:\"1px solid #9E9E9E\",borderRadius:\"8px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"},children:translate(\"Go Back\")}),/*#__PURE__*/_jsx(Button,{onClick:handleLeave,sx:{backgroundColor:\"var(--error-color)\",color:\"#FFF\",borderRadius:\"8px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"// \"&:hover\": {\n// \tbackgroundColor: \"#D32F2F\",\n// },\n},children:translate(\"Close\")})]})]});};export default AlertPopup;", "map": {"version": 3, "names": ["React", "Dialog", "DialogActions", "<PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "WarningIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "openWarning", "setopenWarning", "handleLeave", "t", "translate", "sx", "zIndex", "open", "onClose", "PaperProps", "style", "borderRadius", "max<PERSON><PERSON><PERSON>", "textAlign", "maxHeight", "boxShadow", "className", "children", "padding", "display", "justifyContent", "backgroundColor", "width", "height", "alignItems", "color", "fontSize", "borderTop", "onClick", "border", "textTransform", "lineHeight"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/drawer/AlertPopup.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Box, Typography, Dialog, DialogActions, IconButton, Button, DialogTitle, DialogContent,DialogContentText } from \"@mui/material\";\r\nimport WarningIcon from \"@mui/icons-material/Warning\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst AlertPopup = ({\r\n    openWarning,\r\n    setopenWarning,\r\n    handleLeave,\r\n}: {\r\n  openWarning: boolean; setopenWarning: (params: boolean) => void; handleLeave: () => void;\r\n    }) => { \r\n\tconst { t: translate } = useTranslation();\r\n    return (\r\n\t\t\t<Dialog\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tzIndex: 9999999,\t\t\t\t}}\r\n\t\t\t\topen={openWarning}\r\n\t\t\t\tonClose={() => setopenWarning(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t}}\r\n\t\t\tclassName=\"qadpt-alertpopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<WarningIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t{/* <Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n                Delete {GuideTypetoDelete}\r\n            </Typography> */}\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t{translate(\"You Will Loose Changes, If you Leave With Out Saving\")}\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setopenWarning(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t{translate(\"Go Back\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleLeave}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t{translate(\"Close\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t);\r\n}\r\nexport default AlertPopup;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAA0BC,MAAM,CAAEC,aAAa,CAAcC,MAAM,CAAEC,WAAW,CAAEC,aAAa,CAACC,iBAAiB,KAAQ,eAAe,CACxI,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAC/C,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAMT,IANU,CAChBC,WAAW,CACXC,cAAc,CACdC,WAGA,CAAC,CAAAH,IAAA,CACJ,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAGX,cAAc,CAAC,CAAC,CACtC,mBACDI,KAAA,CAACX,MAAM,EACNmB,EAAE,CAAE,CACHC,MAAM,CAAE,OAAY,CAAE,CACvBC,IAAI,CAAEP,WAAY,CAClBQ,OAAO,CAAEA,CAAA,GAAMP,cAAc,CAAC,KAAK,CAAE,CACrCQ,UAAU,CAAE,CACXC,KAAK,CAAE,CACNC,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,MACZ,CACF,CAAE,CACFC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE3BtB,IAAA,CAACN,WAAW,EAACgB,EAAE,CAAE,CAAEa,OAAO,CAAE,CAAE,CAAE,CAAAD,QAAA,cAC/BtB,IAAA,QAAKe,KAAK,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEF,OAAO,CAAE,MAAO,CAAE,CAAAD,QAAA,cAC1EtB,IAAA,QACCe,KAAK,CAAE,CACNW,eAAe,CAAE,SAAS,CAC1BV,YAAY,CAAE,KAAK,CACnBW,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdJ,OAAO,CAAE,MAAM,CACfK,UAAU,CAAE,QAAQ,CACpBJ,cAAc,CAAE,QACjB,CAAE,CAAAH,QAAA,cAEFtB,IAAA,CAACH,WAAW,EAACa,EAAE,CAAE,CAAEoB,KAAK,CAAE,SAAS,CAAEF,MAAM,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACpE,CAAC,CACF,CAAC,CAIM,CAAC,cAEd3B,IAAA,CAACL,aAAa,EAACe,EAAE,CAAE,CAAEa,OAAO,CAAE,iBAAkB,CAAE,CAAAD,QAAA,cACjDtB,IAAA,CAACJ,iBAAiB,EAACmB,KAAK,CAAE,CAAEgB,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,CAC7Db,SAAS,CAAC,sDAAsD,CAAC,CAC/C,CAAC,CACN,CAAC,cAEhBP,KAAA,CAACV,aAAa,EAACkB,EAAE,CAAE,CAAEe,cAAc,CAAE,eAAe,CAAEO,SAAS,CAAE,+BAAgC,CAAE,CAAAV,QAAA,eAClGtB,IAAA,CAACP,MAAM,EACNwC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,KAAK,CAAE,CACrCI,EAAE,CAAE,CACHoB,KAAK,CAAE,SAAS,CAChBI,MAAM,CAAE,mBAAmB,CAC3BlB,YAAY,CAAE,KAAK,CACnBmB,aAAa,CAAE,YAAY,CAC3BZ,OAAO,CAAE,uBAAuB,CAChCa,UAAU,CAAE,0BACb,CAAE,CAAAd,QAAA,CAEFb,SAAS,CAAC,SAAS,CAAC,CACb,CAAC,cACTT,IAAA,CAACP,MAAM,EACNwC,OAAO,CAAE1B,WAAY,CACrBG,EAAE,CAAE,CACHgB,eAAe,CAAE,oBAAoB,CACrCI,KAAK,CAAE,MAAM,CACbd,YAAY,CAAE,KAAK,CACnBmB,aAAa,CAAE,YAAY,CAC3BZ,OAAO,CAAE,uBAAuB,CAChCa,UAAU,CAAE,0BACZ;AACA;AACA;AACD,CAAE,CAAAd,QAAA,CAEFb,SAAS,CAAC,OAAO,CAAC,CACX,CAAC,EACK,CAAC,EACT,CAAC,CAEZ,CAAC,CACD,cAAe,CAAAN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}