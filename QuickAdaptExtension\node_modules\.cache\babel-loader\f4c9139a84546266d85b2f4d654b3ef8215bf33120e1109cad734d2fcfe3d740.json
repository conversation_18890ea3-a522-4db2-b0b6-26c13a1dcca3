{"ast": null, "code": "import React,{useState}from'react';import'./EnableAIButton.css';import{useTranslation}from'react-i18next';import{Dialog,DialogContent,DialogContentText,Button,TextField,DialogActions}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TrainingField=_ref=>{let{setShowtrainingField,showtrainingField,handleEnableAI}=_ref;const{t:translate}=useTranslation();const[data,setData]=useState('');const handleClick=()=>{setShowtrainingField(false);handleEnableAI();};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(Dialog,{open:showtrainingField,onClose:()=>setShowtrainingField(false),PaperProps:{style:{borderRadius:\"4px\",width:\"400px\",textAlign:\"center\",height:\"188px\",boxShadow:\"none\"}},children:[/*#__PURE__*/_jsx(DialogContent,{sx:{padding:\"20px !important\"},children:/*#__PURE__*/_jsxs(DialogContentText,{style:{fontSize:\"14px\",color:\"#000\"},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"DAsdasd\",style:{fontSize:\"17px\"},children:translate('Training Name')}),/*#__PURE__*/_jsx(TextField,{style:{marginTop:\"16px\"},label:translate(\"Name\"),value:data,onChange:e=>{setData(e.target.value);},fullWidth:true,required:true})]})}),/*#__PURE__*/_jsx(DialogActions,{sx:{justifyContent:\"center\"},children:/*#__PURE__*/_jsx(Button,{onClick:handleClick,sx:{backgroundColor:\"var(--primarycolor)\",color:\"#FFF\",borderRadius:\"8px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"// \"&:hover\": {\n// \tbackgroundColor: \"#D32F2F\",\n// },\n},children:translate(\"Start\")})})]})});};export default TrainingField;", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "<PERSON><PERSON>", "TextField", "DialogActions", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TrainingField", "_ref", "setShowtrainingField", "showtrainingField", "handleEnableAI", "t", "translate", "data", "setData", "handleClick", "children", "open", "onClose", "PaperProps", "style", "borderRadius", "width", "textAlign", "height", "boxShadow", "sx", "padding", "fontSize", "color", "htmlFor", "marginTop", "label", "value", "onChange", "e", "target", "fullWidth", "required", "justifyContent", "onClick", "backgroundColor", "textTransform", "lineHeight"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/AI/TrainingField.tsx"], "sourcesContent": ["import React, { useContext,useState } from 'react';\r\nimport { stopScraping } from '../../services/ScrapingService';\r\nimport './EnableAIButton.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { useTranslation } from 'react-i18next';\r\nimport {\r\n    Dialog,\r\n\tDialogContent,\r\n\tInputAdornment,\r\n\tDialogContentText,\r\n    Grid,\r\n    Box,\r\n    Button,\r\n    Container,\r\n    TextField,\r\n    DialogTitle,\r\n    DialogActions,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    IconButton,\r\n    Tooltip,\r\n    Alert,\r\n    Chip\r\n} from '@mui/material';\r\n\r\nconst TrainingField = ({ setShowtrainingField, showtrainingField, handleEnableAI }: { setShowtrainingField: any; showtrainingField: any; handleEnableAI:any}) => {\r\n    const { t: translate } = useTranslation()\r\n    const [data, setData] = useState('');\r\n    const handleClick = () =>\r\n    {\r\n        setShowtrainingField(false);\r\n        handleEnableAI();\r\n\r\n        }\r\n    return (\r\n      <>\r\n    \r\n            \r\n            <Dialog\r\n\t\t\t\topen={showtrainingField}\r\n\t\t\t\tonClose={() => setShowtrainingField(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\twidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\theight: \"188px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\r\n                \r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n                    <DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n                        <label htmlFor=\"DAsdasd\" style={{ fontSize: \"17px\" }}>{translate('Training Name')}</label>\r\n                        <TextField\r\n                            style={{marginTop:\"16px\"}}\r\n                            label={translate(\"Name\")}\r\n                            value={data}\r\n              onChange={(e) => {\r\n                  setData(e.target.value)\r\n              }\r\n                  } fullWidth\r\n                            required\r\n                        />\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"center\"}}>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleClick}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n                        {translate(\"Start\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n            </>\r\n  );\r\n};\r\n\r\nexport default TrainingField;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,QAAQ,KAAQ,OAAO,CAElD,MAAO,sBAAsB,CAE7B,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACIC,MAAM,CACTC,aAAa,CAEbC,iBAAiB,CAGdC,MAAM,CAENC,SAAS,CAETC,aAAa,KASV,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvB,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAA2I,IAA1I,CAAEC,oBAAoB,CAAEC,iBAAiB,CAAEC,cAAyF,CAAC,CAAAH,IAAA,CACxJ,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAGnB,cAAc,CAAC,CAAC,CACzC,KAAM,CAACoB,IAAI,CAAEC,OAAO,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAAuB,WAAW,CAAGA,CAAA,GACpB,CACIP,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,cAAc,CAAC,CAAC,CAEhB,CAAC,CACL,mBACET,IAAA,CAAAI,SAAA,EAAAW,QAAA,cAGMb,KAAA,CAACT,MAAM,EACfuB,IAAI,CAAER,iBAAkB,CACxBS,OAAO,CAAEA,CAAA,GAAMV,oBAAoB,CAAC,KAAK,CAAE,CAC3CW,UAAU,CAAE,CACXC,KAAK,CAAE,CACNC,YAAY,CAAE,KAAK,CACnBC,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,QAAQ,CACnBC,MAAM,CAAE,OAAO,CACfC,SAAS,CAAE,MACZ,CACD,CAAE,CAAAT,QAAA,eAIFf,IAAA,CAACN,aAAa,EAAC+B,EAAE,CAAE,CAAEC,OAAO,CAAE,iBAAkB,CAAE,CAAAX,QAAA,cAClCb,KAAA,CAACP,iBAAiB,EAACwB,KAAK,CAAE,CAAEQ,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAb,QAAA,eAC1Df,IAAA,UAAO6B,OAAO,CAAC,SAAS,CAACV,KAAK,CAAE,CAAEQ,QAAQ,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAAEJ,SAAS,CAAC,eAAe,CAAC,CAAQ,CAAC,cAC1FX,IAAA,CAACH,SAAS,EACNsB,KAAK,CAAE,CAACW,SAAS,CAAC,MAAM,CAAE,CAC1BC,KAAK,CAAEpB,SAAS,CAAC,MAAM,CAAE,CACzBqB,KAAK,CAAEpB,IAAK,CAC1BqB,QAAQ,CAAGC,CAAC,EAAK,CACbrB,OAAO,CAACqB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC3B,CACK,CAACI,SAAS,MACDC,QAAQ,MACX,CAAC,EACF,CAAC,CACN,CAAC,cAEhBrC,IAAA,CAACF,aAAa,EAAC2B,EAAE,CAAE,CAAEa,cAAc,CAAE,QAAQ,CAAE,CAAAvB,QAAA,cAE9Cf,IAAA,CAACJ,MAAM,EACN2C,OAAO,CAAEzB,WAAY,CACrBW,EAAE,CAAE,CACHe,eAAe,CAAE,qBAAqB,CACtCZ,KAAK,CAAE,MAAM,CACbR,YAAY,CAAE,KAAK,CACnBqB,aAAa,CAAE,YAAY,CAC3Bf,OAAO,CAAE,uBAAuB,CAChCgB,UAAU,CAAE,0BACZ;AACA;AACA;AACD,CAAE,CAAA3B,QAAA,CAEiBJ,SAAS,CAAC,OAAO,CAAC,CAC9B,CAAC,CACK,CAAC,EACT,CAAC,CACE,CAAC,CAEf,CAAC,CAED,cAAe,CAAAN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}