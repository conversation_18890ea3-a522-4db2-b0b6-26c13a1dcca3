{"ast": null, "code": "import React,{useState,useEffect,useContext}from\"react\";import{<PERSON><PERSON>,DialogContent,TextField,InputAdornment,IconButton,Tab,Tabs,Tooltip,DialogTitle,DialogContentText,DialogActions,Button,Typography}from\"@mui/material\";import{DataGrid}from\"@mui/x-data-grid\";import SearchIcon from\"@mui/icons-material/Search\";import ClearIcon from\"@mui/icons-material/Clear\";import{getAllGuides,DeleteGuideByGuideId}from\"../../../services/GuideListServices\";import{ListEditIcon,CopyListIcon,DeleteIconList,NoData}from\"../../../assets/icons/icons\";import DeleteOutlineOutlinedIcon from\"@mui/icons-material/DeleteOutlineOutlined\";import\"./GuideMenuOptions.css\";import AddIcon from\"@mui/icons-material/Add\";import CloneInteractionDialog from\"./CloneGuidePopUp\";import{AccountContext}from\"../../login/AccountContext\";import{useSnackbar}from\"./SnackbarContext\";import{formatDateTime}from\"../../guideSetting/guideList/TimeZoneConversion\";import useDrawerStore from\"../../../store/drawerStore\";import useUserSession from\"../../../store/userSession\";import{useTranslation}from'react-i18next';import useInfoStore from\"../../../store/UserInfoStore\";import{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";let editedguide;const PopupModal=_ref=>{let{Open,onClose,title,searchText,onAddClick}=_ref;const{setCurrentGuideId,currentGuideId}=useUserSession(state=>state);const{t:translate}=useTranslation();const{setBannerPopup,setOpenTooltip,setElementSelected,setBannerButtonSelected,selectedTemplateTour,isUnSavedChanges,setIsUnSavedChanges,openWarning,setOpenWarning,setEditClicked,setActiveMenu,setSearchText}=useDrawerStore(state=>state);const[activeTab,setActiveTab]=useState(0);const[searchQuery,setSearchQuery]=useState(\"\");const[filteredData,setFilteredData]=useState([]);const[isCloneDialogOpen,setIsCloneDialogOpen]=useState(false);const[cloneAnnouncementName,setCloneAnnouncementName]=useState(null);const[guideIdToDelete,setGuideIdToDelete]=useState(null);const[GuidenametoDelete,setGuideNametoDelete]=useState(\"\");const[GuideTypetoDelete,setGuideTypetoDelete]=useState(\"\");const[openDialog,setOpenDialog]=useState(false);const userType=useInfoStore(state=>state.userType);const[paginationModel,setPaginationModel]=useState({page:0,pageSize:15});const{accountId,roles}=useContext(AccountContext);const{openSnackbar}=useSnackbar();const[totalCount,setTotalCount]=useState(0);const[name,setName]=useState(\"Announcement\");const handleEditClick=guide=>{setBannerButtonSelected(true);setIsUnSavedChanges(false);setEditClicked(true);setOpenWarning(false);let targetUrl=\"\";editedguide=true;if(guide.GuideType.toLowerCase()==\"announcement\"||guide.GuideType.toLowerCase()===\"tooltip\"||guide.GuideType.toLowerCase()===\"hotspot\"||guide.GuideType.toLowerCase()===\"tour\"||guide.GuideType.toLowerCase()===\"checklist\"){if(guide.GuideType.toLowerCase()===\"tooltip\"||guide.GuideType.toLowerCase()===\"hotspot\"||guide.GuideType.toLowerCase()===\"banner\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Banner\"||selectedTemplateTour===\"Hotspot\"){setOpenTooltip(true);setElementSelected(true);let styleTag=document.getElementById(\"dynamic-body-style\");const bodyElement=document.body;// Add a dynamic class to the body\nbodyElement.classList.add(\"dynamic-body-style\");if(!styleTag){styleTag=document.createElement(\"style\");styleTag.id=\"dynamic-body-style\";// Add styles for body and nested elements\nlet styles=`\n\t\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t`;styleTag.innerHTML=styles;document.head.appendChild(styleTag);}}targetUrl=`${guide===null||guide===void 0?void 0:guide.TargetUrl}`;if(targetUrl!==window.location.href){setCurrentGuideId(guide.GuideId);window.open(targetUrl);}else{setCurrentGuideId(guide.GuideId);}return;}else if(guide.GuideType.toLowerCase()==\"banner\"||selectedTemplateTour===\"Banner\"){//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\nsetCurrentGuideId(guide.GuideId);setBannerPopup(true);}if(targetUrl){//onAddClick(guide.GuideType, true, guide);\nwindow.open(targetUrl);}};const handleCopyClick=announcement=>{setCloneAnnouncementName(announcement);setIsCloneDialogOpen(true);};const handleDeleteConfirmation=guideId=>{setGuideIdToDelete(guideId);setOpenDialog(true);};const handleKeyDown=event=>{if(event.key===\"Enter\"){handleSearch();}};const columns=[{field:\"Name\",headerName:translate(\"Name\"),// width: 300,\nhideable:true,resizable:false},{field:\"UpdatedDate\",headerName:translate(\"Last Edited\"),// width: 250,\nhideable:true,renderCell:params=>/*#__PURE__*/_jsxs(\"span\",{children:[\" \",`${formatDateTime(params.row.UpdatedDate,\"dd-MM-yyyy\")}`]}),resizable:false},{field:\"actions\",headerName:translate(\"Actions\"),// width: 302,\nhideable:true,renderCell:params=>/*#__PURE__*/_jsx(_Fragment,{children:roles!=null&&roles&&[\"Account Admin\",\"Editor\"].some(role=>roles.includes(role))&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"Edit\"),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleEditClick(params.row),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ListEditIcon},style:{zoom:0.7}})})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"Clone\"),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleCopyClick(params.row),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:CopyListIcon},style:{zoom:0.7}})})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"Delete\"),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>{handleDeleteConfirmation(params.row.GuideId);setGuideNametoDelete(params.row.Name);setGuideTypetoDelete(params.row.GuideType);},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:DeleteIconList},style:{zoom:0.7}})})})]})}),resizable:false}];const fetchAnnouncements=async()=>{var _data$results;const{page,pageSize}=paginationModel;const offset=page*pageSize;const statusFilter=activeTab===0?\"Active\":activeTab===1?\"InActive\":\"Draft\";const filters=[{FieldName:\"GuideType\",ElementType:\"string\",Condition:\"equals\",Value:title===\"Product Tours\"?\"Tour\":title,IsCustomField:false},{FieldName:\"GuideStatus\",ElementType:\"string\",Condition:\"equals\",Value:statusFilter,IsCustomField:false},{FieldName:\"Name\",ElementType:\"string\",Condition:\"contains\",Value:searchQuery,IsCustomField:false},{FieldName:\"AccountId\",ElementType:\"string\",Condition:\"contains\",Value:accountId,IsCustomField:false}];const data=await getAllGuides(offset,pageSize,filters,\"\");const rowsWithIds=data===null||data===void 0?void 0:(_data$results=data.results)===null||_data$results===void 0?void 0:_data$results.map(item=>({...item,id:item.GuideId}));setFilteredData(rowsWithIds||[]);setTotalCount(data===null||data===void 0?void 0:data._count);};useEffect(()=>{if(Open||accountId){fetchAnnouncements();}},[paginationModel,activeTab,Open,accountId]);// useEffect(() => {\n//     if (accountId) {\n//       fetchAnnouncements();\n//     }\n//   }, [paginationModel, activeTab,accountId]);\nconst handleSearch=()=>{fetchAnnouncements();};useEffect(()=>{if(searchQuery.trim()===\"\"){fetchAnnouncements();}},[searchQuery]);const handleClearSearch=()=>{setSearchQuery(\"\");fetchAnnouncements();};const handleTabChange=(event,newValue)=>{setActiveTab(newValue);setPaginationModel(prev=>({...prev,page:0}));// Reset pagination when the tab changes\n};const getRowSpacing=React.useCallback(params=>{return{top:params.isFirstVisible?0:5,bottom:params.isLastVisible?0:5};},[]);const handleDelete=async()=>{if(guideIdToDelete){try{const response=await DeleteGuideByGuideId(guideIdToDelete);if(response.Success){openSnackbar(`${GuidenametoDelete} ${translate(GuideTypetoDelete)} ${translate(\"Deleted Successfully\")}`,\"success\");await fetchAnnouncements();}else{openSnackbar(response.ErrorMessage,\"error\");}}catch(error){}}setOpenDialog(false);setGuideIdToDelete(null);setGuideNametoDelete(\"\");};const handleCloneSuccess=async()=>{await fetchAnnouncements();};const getNoRowsLabel=()=>{const tabLabels=[translate(\"Active\"),translate(\"Inactive\"),translate(\"Draft\")];const currentTabLabel=tabLabels[activeTab]||searchText;return`${translate('No')} ${translate(currentTabLabel,{defaultValue:currentTabLabel})} ${translate(searchText,{defaultValue:`${searchText}s`})}`;};const NoRowsOverlay=()=>/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-hotsicon\",dangerouslySetInnerHTML:{__html:NoData}}),/*#__PURE__*/_jsx(Typography,{sx:{fontWeight:\"600\"},children:getNoRowsLabel()})]});const handleClosePopup=()=>{setActiveMenu(null);setSearchText(\"\");onClose();};return/*#__PURE__*/_jsxs(\"div\",{id:\"popuplistmenu\",children:[/*#__PURE__*/_jsx(Dialog,{slotProps:{root:{id:\"tooltipdialog\"},backdrop:{sx:{position:\"absolute !important\"}}},open:Open,onClose:handleClosePopup,fullWidth:true,maxWidth:\"md\",className:\"qadpt-gud-menupopup\",children:/*#__PURE__*/_jsxs(DialogContent,{className:\"qadpt-gud-menupopup-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-subhead\",id:\"tablesubhead\",children:/*#__PURE__*/_jsx(\"span\",{className:\"title\",style:{fontWeight:\"600 !important\"},children:translate(`${searchText}`,{defaultValue:`${translate(searchText)}s`})})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-head\",id:\"table-head\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-titsection\",children:[/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",placeholder:translate(\"Search\")+\" \"+translate(title),value:searchQuery,onChange:e=>{const newValue=e.target.value;setSearchQuery(newValue);if(newValue===\"\"){handleClearSearch();}},onKeyDown:e=>{if(e.key===\"Enter\"){handleSearch();}},className:\"qadpt-extsearch\",InputProps:{sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{borderColor:\"#a8a8a8\"},// Prevents color change on hover\n\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"1px solid #a8a8a8\"}},startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"search\",onClick:()=>handleSearch(),onMouseDown:event=>event.preventDefault(),children:/*#__PURE__*/_jsx(SearchIcon,{})})}),endAdornment:searchQuery&&/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"clear\",onClick:()=>{setSearchQuery(\"\");handleClearSearch();},children:/*#__PURE__*/_jsx(ClearIcon,{sx:{zoom:\"1.2\"}})})})}}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-right-part\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onAddClick(searchText),className:\"qadpt-memberButton\",disabled:userType.toLocaleLowerCase()!=\"admin\"?roles==null||!roles||![\"Account Admin\",\"Editor\"].some(role=>roles.includes(role)):false,children:[/*#__PURE__*/_jsx(AddIcon,{}),/*#__PURE__*/_jsx(\"span\",{children:`${translate(\"Create\")} ${translate(searchText)}`})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-tabs-container\",children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,children:[/*#__PURE__*/_jsx(Tab,{label:translate(\"Active\"),sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}}),/*#__PURE__*/_jsx(Tab,{label:translate(\"Inactive\"),sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}}),/*#__PURE__*/_jsx(Tab,{label:translate(\"Draft\"),sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-webgird\",children:/*#__PURE__*/_jsx(DataGrid,{rows:filteredData,columns:columns,getRowId:row=>row.GuideId,getRowSpacing:getRowSpacing,pagination:true,paginationModel:paginationModel,paginationMode:\"server\",onPaginationModelChange:setPaginationModel,rowCount:totalCount,pageSizeOptions:[15,25,50,100],localeText:{MuiTablePagination:{labelRowsPerPage:translate(\"Records Per Page\")},noRowsLabel:getNoRowsLabel()},disableColumnMenu:true,disableRowSelectionOnClick:true,className:\"qadpt-grdcont\",slots:{noRowsOverlay:NoRowsOverlay// Using the 'slots' prop for NoRowsOverlay\n},sx:{\"& .MuiDataGrid-row\":{maxWidth:\"calc(100% - 30px)\",\"--rowBorderColor\":\"transparent\"//   marginTop: \"17px\",\n// marginBottom:\"0 !important\"\n},\"& .MuiDataGrid-cell\":{padding:\"0 15px !important\"},\".MuiTablePagination-toolbar\":{display:\"flex !important\",alignItems:\"baseline !important\"},\".MuiTablePagination-actions button\":{border:\"none !important\",color:\"inherit !important\",backgroundColor:\"initial !important\",\"&:hover\":{backgroundColor:\"initial !important\"// Hover background\n}},\"& .MuiDataGrid-columnHeader\":{background:\"linear-gradient(to right, #f6eeee, #f6eeee)\",padding:\"0 15px !important\",borderRight:\"1px solid #f6eeee\",height:\"40px !important\"},\"& .MuiDataGrid-columnHeaderTitle\":{fontWeight:\"600\"},\"& .MuiDataGrid-filler\":{backgroundColor:\"var(--ext-background)\",\"--rowBorderColor\":\"transparent !important\"},\"& .MuiDataGrid-scrollbarFiller\":{backgroundColor:\"var(--ext-background)\",display:\"none\"}},rowHeight:38})})]})}),/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:()=>setOpenDialog(false),PaperProps:{style:{borderRadius:\"4px\",maxWidth:\"400px\",textAlign:\"center\",maxHeight:\"300px\",boxShadow:\"none\"}},children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{padding:0},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\",padding:\"10px\"},children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:\"#e4b6b0\",borderRadius:\"50%\",width:\"40px\",height:\"40px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(DeleteOutlineOutlinedIcon,{sx:{color:\"#F44336\",height:\"20px\",width:\"20px\"}})})}),/*#__PURE__*/_jsxs(Typography,{sx:{fontSize:\"16px !important\",fontWeight:600,padding:\"0 10px\"},children:[translate(\"Delete\"),\" \",translate(GuideTypetoDelete)]})]}),/*#__PURE__*/_jsx(DialogContent,{sx:{padding:\"20px !important\"},children:/*#__PURE__*/_jsx(DialogContentText,{style:{fontSize:\"14px\",color:\"#000\"},children:translate(`${translate('The')} ${GuidenametoDelete} ${translate(\"cannot be restored once it is deleted.\")}`)})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:\"space-between\",borderTop:\"1px solid var(--border-color)\"},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setOpenDialog(false),sx:{color:\"#9E9E9E\",border:\"1px solid #9E9E9E\",borderRadius:\"4px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"},children:translate(\"Cancel\")}),/*#__PURE__*/_jsx(Button,{onClick:handleDelete,sx:{backgroundColor:\"var(--error-color)\",color:\"#FFF\",borderRadius:\"4px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"// \"&:hover\": {\n// \tbackgroundColor: \"#D32F2F\",\n// },\n},children:translate(\"Delete\")})]})]}),isCloneDialogOpen&&cloneAnnouncementName&&/*#__PURE__*/_jsx(CloneInteractionDialog,{open:isCloneDialogOpen,handleClose:()=>setIsCloneDialogOpen(false),initialName:cloneAnnouncementName,onCloneSuccess:handleCloneSuccess,name:name})]});};export{editedguide};export default PopupModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "IconButton", "Tab", "Tabs", "<PERSON><PERSON><PERSON>", "DialogTitle", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "Typography", "DataGrid", "SearchIcon", "ClearIcon", "getAllGuides", "DeleteGuideByGuideId", "ListEditIcon", "CopyListIcon", "DeleteIconList", "NoData", "DeleteOutlineOutlinedIcon", "AddIcon", "CloneInteractionDialog", "AccountContext", "useSnackbar", "formatDateTime", "useDrawerStore", "useUserSession", "useTranslation", "useInfoStore", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "editedguide", "PopupModal", "_ref", "Open", "onClose", "title", "searchText", "onAddClick", "setCurrentGuideId", "currentGuideId", "state", "t", "translate", "setBannerPopup", "setOpenTooltip", "setElementSelected", "setBannerButtonSelected", "selectedTemplateTour", "isUnSavedChanges", "setIsUnSavedChanges", "openWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditClicked", "setActiveMenu", "setSearchText", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "filteredData", "setFilteredData", "isCloneDialogOpen", "setIsCloneDialogOpen", "cloneAnnouncementName", "setCloneAnnouncementName", "guideIdToDelete", "setGuideIdToDelete", "GuidenametoDelete", "setGuideNametoDelete", "GuideTypetoDelete", "setGuideTypetoDelete", "openDialog", "setOpenDialog", "userType", "paginationModel", "setPaginationModel", "page", "pageSize", "accountId", "roles", "openSnackbar", "totalCount", "setTotalCount", "name", "setName", "handleEditClick", "guide", "targetUrl", "GuideType", "toLowerCase", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "TargetUrl", "window", "location", "href", "GuideId", "open", "handleCopyClick", "announcement", "handleDeleteConfirmation", "guideId", "handleKeyDown", "event", "key", "handleSearch", "columns", "field", "headerName", "hideable", "resizable", "renderCell", "params", "children", "row", "UpdatedDate", "some", "role", "includes", "arrow", "onClick", "dangerouslySetInnerHTML", "__html", "style", "zoom", "Name", "fetchAnnouncements", "_data$results", "offset", "statusFilter", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "data", "rowsWithIds", "results", "map", "item", "_count", "trim", "handleClearSearch", "handleTabChange", "newValue", "prev", "getRowSpacing", "useCallback", "top", "isFirstVisible", "bottom", "isLastVisible", "handleDelete", "response", "Success", "ErrorMessage", "error", "handleCloneSuccess", "getNoRowsLabel", "tabLabels", "currentTabLabel", "defaultValue", "NoRowsOverlay", "display", "alignItems", "flexDirection", "className", "sx", "fontWeight", "handleClosePopup", "slotProps", "root", "backdrop", "position", "fullWidth", "max<PERSON><PERSON><PERSON>", "variant", "placeholder", "value", "onChange", "e", "target", "onKeyDown", "InputProps", "borderColor", "border", "startAdornment", "onMouseDown", "preventDefault", "endAdornment", "disabled", "toLocaleLowerCase", "label", "backgroundColor", "color", "fontSize", "rows", "getRowId", "pagination", "paginationMode", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "noRowsLabel", "disableColumnMenu", "disableRowSelectionOnClick", "slots", "noRowsOverlay", "padding", "background", "borderRight", "height", "rowHeight", "PaperProps", "borderRadius", "textAlign", "maxHeight", "boxShadow", "justifyContent", "width", "borderTop", "textTransform", "lineHeight", "handleClose", "initialName", "onCloneSuccess"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/PopupList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport {\r\n\t<PERSON><PERSON>,\r\n\tDialogContent,\r\n\tTextField,\r\n\tInputAdornment,\r\n\tIconButton,\r\n\tTab,\r\n\tTabs,\r\n\tTooltip,\r\n\tDialogTitle,\r\n\tDialogContentText,\r\n\tDialogActions,\r\n\tButton,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from \"@mui/x-data-grid\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\n\r\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\r\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport \"./GuideMenuOptions.css\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\r\nimport { AccountContext } from \"../../login/AccountContext\";\r\nimport { useSnackbar } from \"./SnackbarContext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport useUserSession from \"../../../store/userSession\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useAuth } from \"../../auth/AuthProvider\";\r\nimport useInfoStore from \"../../../store/UserInfoStore\";\r\n\r\nlet editedguide: any;\r\ninterface PopupModalProps {\r\n\ttitle: string;\r\n\tOpen: boolean;\r\n\tonClose: () => void;\r\n\tsearchText: string;\r\n\tonAddClick: (searchText: string, isEditing?: boolean, guideDetails?: any) => void;\r\n}\r\ninterface Announcement {\r\n\tAccountId: string;\r\n\tContent: string;\r\n\tCreatedBy: string;\r\n\tCreatedDate: string;\r\n\tFrequency: string;\r\n\tGuideId: string;\r\n\tGuideStatus: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tOrganizationId: string;\r\n\tSegment: string;\r\n\tTargetUrl: string;\r\n\tTemplateId: string;\r\n\tUpdatedBy: string;\r\n\tUpdatedDate: string;\r\n}\r\n\r\nconst PopupModal: React.FC<PopupModalProps> = ({ Open, onClose, title, searchText, onAddClick }) => {\r\n\tconst { setCurrentGuideId, currentGuideId } = useUserSession((state) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetBannerPopup,\r\n\t\tsetOpenTooltip,\r\n\t\tsetElementSelected,\r\n\t\tsetBannerButtonSelected,\r\n\t\tselectedTemplateTour,\r\n\t\tisUnSavedChanges,\r\n\t\tsetIsUnSavedChanges,\r\n\t\topenWarning,\r\n\t\tsetOpenWarning,\r\n\t\tsetEditClicked,\r\n\t\tsetActiveMenu,\r\n\t\tsetSearchText,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [activeTab, setActiveTab] = useState(0);\r\n\tconst [searchQuery, setSearchQuery] = useState(\"\");\r\n\tconst [filteredData, setFilteredData] = useState<any[]>([]);\r\n\tconst [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\r\n\tconst [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);\r\n\tconst [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);\r\n\tconst [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\r\n\tconst [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\r\n\tconst [openDialog, setOpenDialog] = useState(false);\r\n\tconst userType = useInfoStore((state) => state.userType); \r\n\tconst [paginationModel, setPaginationModel] = useState({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst { accountId,roles } = useContext(AccountContext);\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [totalCount, setTotalCount] = useState(0);\r\n\tconst [name, setName] = useState(\"Announcement\");\r\n\tconst handleEditClick = (guide: Announcement) => {\r\n\t\tsetBannerButtonSelected(true);\r\n\t\tsetIsUnSavedChanges(false);\r\n\t\tsetEditClicked(true);\r\n\t\tsetOpenWarning(false);\r\n\t\tlet targetUrl = \"\";\r\n\t\teditedguide = true;\r\n\t\tif (\r\n\t\t\tguide.GuideType.toLowerCase() == \"announcement\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tour\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"checklist\"\r\n\t\t) {\r\n\t\t\tif (\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t) {\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t\t// Add a dynamic class to the body\r\n\t\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\t\tif (!styleTag) {\r\n\t\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\t\tlet styles = `\r\n\t\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t`;\r\n\r\n\t\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttargetUrl = `${guide?.TargetUrl}`;\r\n\t\t\tif (targetUrl !== window.location.href) {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t\twindow.open(targetUrl);\r\n\t\t\t} else {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t} else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\t//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\r\n\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\tsetBannerPopup(true);\r\n\t\t}\r\n\t\tif (targetUrl) {\r\n\t\t\t//onAddClick(guide.GuideType, true, guide);\r\n\t\t\twindow.open(targetUrl);\r\n\t\t}\r\n\t};\r\n\t\r\n\tconst handleCopyClick = (announcement: Announcement) => {\r\n\t\tsetCloneAnnouncementName(announcement);\r\n\t\tsetIsCloneDialogOpen(true);\r\n\t};\r\n\tconst handleDeleteConfirmation = (guideId: string) => {\r\n\t\tsetGuideIdToDelete(guideId);\r\n\t\tsetOpenDialog(true);\r\n\t};\r\n\tconst handleKeyDown = (event: React.KeyboardEvent) => {\r\n\t\tif (event.key === \"Enter\") {\r\n\t\t\thandleSearch();\r\n\t\t}\r\n\t};\r\n\tconst columns: GridColDef[] = [\r\n\t\t{\r\n\t\t\tfield: \"Name\",\r\n\t\t\theaderName: translate(\"Name\"),\r\n\t\t\t// width: 300,\r\n\t\t\thideable: true,\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"UpdatedDate\",\r\n\t\t\theaderName: translate(\"Last Edited\"),\r\n\t\t\t// width: 250,\r\n\t\t\thideable: true,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<span> {`${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`}</span>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: translate(\"Actions\"),\r\n\t\t\t// width: 302,\r\n\t\t\thideable: true,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<>\r\n\t\t\t\t\t{ roles != null && roles && [\"Account Admin\",\"Editor\"].some(role => roles.includes(role)) &&<>\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\t\ttitle={translate(\"Edit\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton onClick={() => handleEditClick(params.row)}>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: ListEditIcon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate(\"Clone\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleCopyClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CopyListIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate(\"Delete\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\thandleDeleteConfirmation(params.row.GuideId);\r\n\t\t\t\t\t\t\t\tsetGuideNametoDelete(params.row.Name);\r\n\t\t\t\t\t\t\t\tsetGuideTypetoDelete(params.row.GuideType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: DeleteIconList }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t}\r\n\t\t\t\t</>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t];\r\n\r\n\tconst fetchAnnouncements = async () => {\r\n\t\tconst { page, pageSize } = paginationModel;\r\n\t\tconst offset = page * pageSize;\r\n\t\tconst statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\r\n\r\n\t\tconst filters = [\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideType\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: title === \"Product Tours\" ? \"Tour\" : title,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideStatus\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: statusFilter,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"Name\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: searchQuery,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: accountId,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t];\r\n\t\tconst data = await getAllGuides(offset, pageSize, filters, \"\");\r\n\t\tconst rowsWithIds = data?.results?.map((item: any) => ({\r\n\t\t\t...item,\r\n\t\t\tid: item.GuideId,\r\n\t\t}));\r\n\r\n\t\tsetFilteredData(rowsWithIds || []);\r\n\t\tsetTotalCount(data?._count);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (Open || accountId) {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [paginationModel, activeTab, Open, accountId]);\r\n\r\n\t// useEffect(() => {\r\n\t//     if (accountId) {\r\n\t//       fetchAnnouncements();\r\n\t//     }\r\n\t//   }, [paginationModel, activeTab,accountId]);\r\n\r\n\tconst handleSearch = () => {\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (searchQuery.trim() === \"\") {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [searchQuery]);\r\n\tconst handleClearSearch = () => {\r\n\t\tsetSearchQuery(\"\");\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tconst handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n\t\tsetActiveTab(newValue);\r\n\t\tsetPaginationModel((prev) => ({ ...prev, page: 0 })); // Reset pagination when the tab changes\r\n\t};\r\n\tconst getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {\r\n\t\treturn {\r\n\t\t\ttop: params.isFirstVisible ? 0 : 5,\r\n\t\t\tbottom: params.isLastVisible ? 0 : 5,\r\n\t\t};\r\n\t}, []);\r\n\r\n\tconst handleDelete = async () => {\r\n\t\tif (guideIdToDelete) {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await DeleteGuideByGuideId(guideIdToDelete);\r\n\t\t\t\tif (response.Success) {\r\n\t\t\t\t\topenSnackbar(\r\n\t\t\t\t\t\t`${GuidenametoDelete} ${translate(GuideTypetoDelete)} ${translate(\"Deleted Successfully\")}`,\r\n\t\t\t\t\t\t\"success\"\r\n\t\t\t\t\t);\r\n\t\t\t\t\tawait fetchAnnouncements();\r\n\t\t\t\t} else {\r\n\t\t\t\t\topenSnackbar(response.ErrorMessage, \"error\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t}\r\n\t\tsetOpenDialog(false);\r\n\t\tsetGuideIdToDelete(null);\r\n\t\tsetGuideNametoDelete(\"\");\r\n\t};\r\n\tconst handleCloneSuccess = async () => {\r\n\t\tawait fetchAnnouncements();\r\n\t};\r\n\tconst getNoRowsLabel = () => {\r\n\t\tconst tabLabels = [translate(\"Active\"), translate(\"Inactive\"), translate(\"Draft\")];\r\n\t\tconst currentTabLabel = tabLabels[activeTab] || searchText;\r\n\t\treturn `${translate('No')} ${translate(currentTabLabel, { defaultValue: currentTabLabel })} ${translate(searchText, { defaultValue: `${searchText}s` })}`;\r\n\t};\r\n\tconst NoRowsOverlay = () => (\r\n\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n\t\t\t<span\r\n\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: NoData }}\r\n\t\t\t/>\r\n\t\t\t<Typography sx={{ fontWeight: \"600\" }}>{getNoRowsLabel()}</Typography>\r\n\t\t</div>\r\n\t);\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\tsetActiveMenu(null);\r\n\t\tsetSearchText(\"\");\r\n\t\tonClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div id=\"popuplistmenu\">\r\n\t\t\t<Dialog\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tid: \"tooltipdialog\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbackdrop: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tposition: \"absolute !important\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\topen={Open}\r\n\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\tfullWidth\r\n\t\t\t\tmaxWidth=\"md\"\r\n\t\t\t\tclassName=\"qadpt-gud-menupopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogContent className=\"qadpt-gud-menupopup-content\">\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-subhead\"\r\n\t\t\t\t\t\tid=\"tablesubhead\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span\r\n\tclassName=\"title\"\r\n\tstyle={{ fontWeight: \"600 !important\" }}\r\n>\r\n\t\t\t\t\t\t\t{translate(`${searchText}`, { defaultValue: `${translate(searchText)}s` })}\r\n</span>\r\n\t\t\t\t\t\t{/* <IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\tborderWidth: \"1px\",\r\n\t\t\t\t\t\t\t\tborderStyle: \"solid\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon style={{ color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton> */}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-head\"\r\n\t\t\t\t\t\tid=\"table-head\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-titsection\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Search\") + \" \" + translate(title)}\r\n\t\t\t\t\t\t\t\tvalue={searchQuery}\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newValue = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetSearchQuery(newValue);\r\n\t\t\t\t\t\t\t\t\tif (newValue === \"\") {\r\n\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\r\n\t\t\t\t\t\t\t\t\t\thandleSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-extsearch\"\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"1px solid #a8a8a8\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSearch()}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonMouseDown={(event) => event.preventDefault()}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\tendAdornment: searchQuery && (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"clear\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetSearchQuery(\"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ClearIcon sx={{ zoom: \"1.2\" }} />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={() => onAddClick(searchText)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t\tdisabled={userType.toLocaleLowerCase()!=\"admin\" ? roles==null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)): false}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<AddIcon />\r\n\t\t\t\t\t\t\t\t\t<span>{`${translate(\"Create\")} ${translate(searchText)}`}</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-tabs-container\">\r\n\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\tvalue={activeTab}\r\n\t\t\t\t\t\t\tonChange={handleTabChange}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Active\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Inactive\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel={translate(\"Draft\")}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-webgird\">\r\n\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\trows={filteredData}\r\n\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\tgetRowId={(row) => row.GuideId}\r\n\t\t\t\t\t\t\tgetRowSpacing={getRowSpacing}\r\n\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\t\tonPaginationModelChange={setPaginationModel}\r\n\t\t\t\t\t\t\trowCount={totalCount}\r\n\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\t\tlabelRowsPerPage: translate(\"Records Per Page\"),\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tnoRowsLabel: getNoRowsLabel(),\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableColumnMenu\r\n\t\t\t\t\t\t\tdisableRowSelectionOnClick\r\n\t\t\t\t\t\t\tclassName=\"qadpt-grdcont\"\r\n\t\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\t\tnoRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: \"calc(100% - 30px)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent\",\r\n\t\t\t\t\t\t\t\t\t//   marginTop: \"17px\",\r\n\t\t\t\t\t\t\t\t\t// marginBottom:\"0 !important\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-toolbar\": {\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex !important\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"baseline !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-actions button\": {\r\n\t\t\t\t\t\t\t\t\tborder: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\", // Hover background\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t\t\tbackground: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #f6eeee\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"600\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-filler\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-scrollbarFiller\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\trowHeight={38}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</DialogContent>\r\n\t\t\t</Dialog>\r\n\t\t\t<Dialog\r\n\t\t\t\topen={openDialog}\r\n\t\t\t\tonClose={() => setOpenDialog(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<DeleteOutlineOutlinedIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n\t\t\t\t\t\t{translate(\"Delete\")} {translate(GuideTypetoDelete)}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t\t{translate(`${translate('The')} ${GuidenametoDelete} ${translate(\"cannot be restored once it is deleted.\")}`)}\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenDialog(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Cancel\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleDelete}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Delete\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t\t\t{isCloneDialogOpen && cloneAnnouncementName && (\r\n\t\t\t\t<CloneInteractionDialog\r\n\t\t\t\t\topen={isCloneDialogOpen}\r\n\t\t\t\t\thandleClose={() => setIsCloneDialogOpen(false)}\r\n\t\t\t\t\tinitialName={cloneAnnouncementName}\r\n\t\t\t\t\tonCloneSuccess={handleCloneSuccess}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport { editedguide };\r\nexport default PopupModal;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CAC9D,OACCC,MAAM,CACNC,aAAa,CACbC,SAAS,CACTC,cAAc,CACdC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,OAAO,CACPC,WAAW,CACXC,iBAAiB,CACjBC,aAAa,CACbC,MAAM,CACNC,UAAU,KACJ,eAAe,CACtB,OAASC,QAAQ,KAAgE,kBAAkB,CACnG,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAEjD,OAASC,YAAY,CAAEC,oBAAoB,KAAQ,qCAAqC,CACxF,OAASC,YAAY,CAAEC,YAAY,CAAEC,cAAc,CAAEC,MAAM,KAAQ,6BAA6B,CAChG,MAAO,CAAAC,yBAAyB,KAAM,2CAA2C,CACjF,MAAO,wBAAwB,CAC/B,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,sBAAsB,KAAM,mBAAmB,CACtD,OAASC,cAAc,KAAQ,4BAA4B,CAC3D,OAASC,WAAW,KAAQ,mBAAmB,CAE/C,OAASC,cAAc,KAAQ,iDAAiD,CAChF,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,OAASC,cAAc,KAAQ,eAAe,CAE9C,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExD,GAAI,CAAAC,WAAgB,CA0BpB,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAAsD,IAArD,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAAL,IAAA,CAC9F,KAAM,CAAEM,iBAAiB,CAAEC,cAAe,CAAC,CAAGlB,cAAc,CAAEmB,KAAK,EAAKA,KAAK,CAAC,CAC9E,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGpB,cAAc,CAAC,CAAC,CACzC,KAAM,CACLqB,cAAc,CACdC,cAAc,CACdC,kBAAkB,CAClBC,uBAAuB,CACvBC,oBAAoB,CACpBC,gBAAgB,CAChBC,mBAAmB,CACnBC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,aAAa,CACbC,aACD,CAAC,CAAGlC,cAAc,CAAEoB,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGnE,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACoE,WAAW,CAAEC,cAAc,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsE,YAAY,CAAEC,eAAe,CAAC,CAAGvE,QAAQ,CAAQ,EAAE,CAAC,CAC3D,KAAM,CAACwE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC0E,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG3E,QAAQ,CAAsB,IAAI,CAAC,CAC7F,KAAM,CAAC4E,eAAe,CAAEC,kBAAkB,CAAC,CAAG7E,QAAQ,CAAgB,IAAI,CAAC,CAC3E,KAAM,CAAC8E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACgF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACkF,UAAU,CAAEC,aAAa,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAAoF,QAAQ,CAAGlD,YAAY,CAAEiB,KAAK,EAAKA,KAAK,CAACiC,QAAQ,CAAC,CACxD,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGtF,QAAQ,CAAC,CACtDuF,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAAEC,SAAS,CAACC,KAAM,CAAC,CAAGxF,UAAU,CAAC0B,cAAc,CAAC,CACtD,KAAM,CAAE+D,YAAa,CAAC,CAAG9D,WAAW,CAAC,CAAC,CACtC,KAAM,CAAC+D,UAAU,CAAEC,aAAa,CAAC,CAAG7F,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC8F,IAAI,CAAEC,OAAO,CAAC,CAAG/F,QAAQ,CAAC,cAAc,CAAC,CAChD,KAAM,CAAAgG,eAAe,CAAIC,KAAmB,EAAK,CAChDxC,uBAAuB,CAAC,IAAI,CAAC,CAC7BG,mBAAmB,CAAC,KAAK,CAAC,CAC1BG,cAAc,CAAC,IAAI,CAAC,CACpBD,cAAc,CAAC,KAAK,CAAC,CACrB,GAAI,CAAAoC,SAAS,CAAG,EAAE,CAClBzD,WAAW,CAAG,IAAI,CAClB,GACCwD,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,EAAI,cAAc,EAC/CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,MAAM,EACxCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,WAAW,CAC5C,CACD,GACCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,QAAQ,EAC1C1C,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,QAAQ,EACjCA,oBAAoB,GAAK,SAAS,CACjC,CACDH,cAAc,CAAC,IAAI,CAAC,CACpBC,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CAAA6C,QAAQ,CAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB,CAChF,KAAM,CAAAC,WAAW,CAAGF,QAAQ,CAACG,IAAI,CAEjC;AACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAE/C,GAAI,CAACN,QAAQ,CAAE,CACdA,QAAQ,CAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC,CAC1CP,QAAQ,CAACQ,EAAE,CAAG,oBAAoB,CAElC;AACA,GAAI,CAAAC,MAAM,CAAG;AAClB;AACA;AACA;AACA;AACA;AACA,MAAM,CAEDT,QAAQ,CAACU,SAAS,CAAGD,MAAM,CAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC,CACpC,CACD,CACAH,SAAS,CAAG,GAAGD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEiB,SAAS,EAAE,CACjC,GAAIhB,SAAS,GAAKiB,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAE,CACvCpE,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC,CAChCH,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC,CACvB,CAAC,IAAM,CACNjD,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC,CACjC,CAEA,OACD,CAAC,IAAM,IAAIrB,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,EAAI,QAAQ,EAAI1C,oBAAoB,GAAK,QAAQ,CAAE,CAC1F;AACAT,iBAAiB,CAACgD,KAAK,CAACqB,OAAO,CAAC,CAChChE,cAAc,CAAC,IAAI,CAAC,CACrB,CACA,GAAI4C,SAAS,CAAE,CACd;AACAiB,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC,CACvB,CACD,CAAC,CAED,KAAM,CAAAsB,eAAe,CAAIC,YAA0B,EAAK,CACvD9C,wBAAwB,CAAC8C,YAAY,CAAC,CACtChD,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAC,CACD,KAAM,CAAAiD,wBAAwB,CAAIC,OAAe,EAAK,CACrD9C,kBAAkB,CAAC8C,OAAO,CAAC,CAC3BxC,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,CACD,KAAM,CAAAyC,aAAa,CAAIC,KAA0B,EAAK,CACrD,GAAIA,KAAK,CAACC,GAAG,GAAK,OAAO,CAAE,CAC1BC,YAAY,CAAC,CAAC,CACf,CACD,CAAC,CACD,KAAM,CAAAC,OAAqB,CAAG,CAC7B,CACCC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE7E,SAAS,CAAC,MAAM,CAAC,CAC7B;AACA8E,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,KACZ,CAAC,CACD,CACCH,KAAK,CAAE,aAAa,CACpBC,UAAU,CAAE7E,SAAS,CAAC,aAAa,CAAC,CACpC;AACA8E,QAAQ,CAAE,IAAI,CACdE,UAAU,CAAGC,MAA4B,eACxClG,KAAA,SAAAmG,QAAA,EAAM,GAAC,CAAC,GAAGzG,cAAc,CAACwG,MAAM,CAACE,GAAG,CAACC,WAAW,CAAE,YAAY,CAAC,EAAE,EAAO,CACxE,CACDL,SAAS,CAAE,KACZ,CAAC,CACD,CACCH,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE7E,SAAS,CAAC,SAAS,CAAC,CAChC;AACA8E,QAAQ,CAAE,IAAI,CACdE,UAAU,CAAGC,MAA4B,eACxChG,IAAA,CAAAE,SAAA,EAAA+F,QAAA,CACG7C,KAAK,EAAI,IAAI,EAAIA,KAAK,EAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAACgD,IAAI,CAACC,IAAI,EAAIjD,KAAK,CAACkD,QAAQ,CAACD,IAAI,CAAC,CAAC,eAAGvG,KAAA,CAAAI,SAAA,EAAA+F,QAAA,eAC3FjG,IAAA,CAAC5B,OAAO,EACPmI,KAAK,MACL/F,KAAK,CAAEO,SAAS,CAAC,MAAM,CAAE,CAAAkF,QAAA,cAEzBjG,IAAA,CAAC/B,UAAU,EAACuI,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACsC,MAAM,CAACE,GAAG,CAAE,CAAAD,QAAA,cACtDjG,IAAA,SACCyG,uBAAuB,CAAE,CAAEC,MAAM,CAAE3H,YAAa,CAAE,CAClD4H,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACL,CAAC,cAEX5G,IAAA,CAAC5B,OAAO,EACPmI,KAAK,MACL/F,KAAK,CAAEO,SAAS,CAAC,OAAO,CAAE,CAAAkF,QAAA,cAE1BjG,IAAA,CAAC/B,UAAU,EAACuI,OAAO,CAAEA,CAAA,GAAMtB,eAAe,CAACc,MAAM,CAACE,GAAG,CAAE,CAAAD,QAAA,cACtDjG,IAAA,SACCyG,uBAAuB,CAAE,CAAEC,MAAM,CAAE1H,YAAa,CAAE,CAClD2H,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACL,CAAC,cACV5G,IAAA,CAAC5B,OAAO,EACPmI,KAAK,MACL/F,KAAK,CAAEO,SAAS,CAAC,QAAQ,CAAE,CAAAkF,QAAA,cAE3BjG,IAAA,CAAC/B,UAAU,EACVuI,OAAO,CAAEA,CAAA,GAAM,CACdpB,wBAAwB,CAACY,MAAM,CAACE,GAAG,CAAClB,OAAO,CAAC,CAC5CvC,oBAAoB,CAACuD,MAAM,CAACE,GAAG,CAACW,IAAI,CAAC,CACrClE,oBAAoB,CAACqD,MAAM,CAACE,GAAG,CAACrC,SAAS,CAAC,CAC3C,CAAE,CAAAoC,QAAA,cAEFjG,IAAA,SACCyG,uBAAuB,CAAE,CAAEC,MAAM,CAAEzH,cAAe,CAAE,CACpD0H,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACJ,CAAC,EACR,CAAC,CAEH,CACF,CACDd,SAAS,CAAE,KACZ,CAAC,CACD,CAED,KAAM,CAAAgB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,KAAAC,aAAA,CACtC,KAAM,CAAE9D,IAAI,CAAEC,QAAS,CAAC,CAAGH,eAAe,CAC1C,KAAM,CAAAiE,MAAM,CAAG/D,IAAI,CAAGC,QAAQ,CAC9B,KAAM,CAAA+D,YAAY,CAAGrF,SAAS,GAAK,CAAC,CAAG,QAAQ,CAAGA,SAAS,GAAK,CAAC,CAAG,UAAU,CAAG,OAAO,CAExF,KAAM,CAAAsF,OAAO,CAAG,CACf,CACCC,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAE9G,KAAK,GAAK,eAAe,CAAG,MAAM,CAAGA,KAAK,CACjD+G,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,aAAa,CACxBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAEL,YAAY,CACnBM,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAExF,WAAW,CAClByF,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAEnE,SAAS,CAChBoE,aAAa,CAAE,KAChB,CAAC,CACD,CACD,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAA3I,YAAY,CAACmI,MAAM,CAAE9D,QAAQ,CAAEgE,OAAO,CAAE,EAAE,CAAC,CAC9D,KAAM,CAAAO,WAAW,CAAGD,IAAI,SAAJA,IAAI,kBAAAT,aAAA,CAAJS,IAAI,CAAEE,OAAO,UAAAX,aAAA,iBAAbA,aAAA,CAAeY,GAAG,CAAEC,IAAS,GAAM,CACtD,GAAGA,IAAI,CACPrD,EAAE,CAAEqD,IAAI,CAAC5C,OACV,CAAC,CAAC,CAAC,CAEH/C,eAAe,CAACwF,WAAW,EAAI,EAAE,CAAC,CAClClE,aAAa,CAACiE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,MAAM,CAAC,CAC5B,CAAC,CAEDlK,SAAS,CAAC,IAAM,CACf,GAAI2C,IAAI,EAAI6C,SAAS,CAAE,CACtB2D,kBAAkB,CAAC,CAAC,CACrB,CACD,CAAC,CAAE,CAAC/D,eAAe,CAAEnB,SAAS,CAAEtB,IAAI,CAAE6C,SAAS,CAAC,CAAC,CAEjD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAsC,YAAY,CAAGA,CAAA,GAAM,CAC1BqB,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAEDnJ,SAAS,CAAC,IAAM,CACf,GAAImE,WAAW,CAACgG,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9BhB,kBAAkB,CAAC,CAAC,CACrB,CACD,CAAC,CAAE,CAAChF,WAAW,CAAC,CAAC,CACjB,KAAM,CAAAiG,iBAAiB,CAAGA,CAAA,GAAM,CAC/BhG,cAAc,CAAC,EAAE,CAAC,CAClB+E,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAGA,CAACzC,KAA2B,CAAE0C,QAAgB,GAAK,CAC1EpG,YAAY,CAACoG,QAAQ,CAAC,CACtBjF,kBAAkB,CAAEkF,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEjF,IAAI,CAAE,CAAE,CAAC,CAAC,CAAC,CAAE;AACvD,CAAC,CACD,KAAM,CAAAkF,aAAa,CAAG1K,KAAK,CAAC2K,WAAW,CAAEpC,MAA4B,EAAK,CACzE,MAAO,CACNqC,GAAG,CAAErC,MAAM,CAACsC,cAAc,CAAG,CAAC,CAAG,CAAC,CAClCC,MAAM,CAAEvC,MAAM,CAACwC,aAAa,CAAG,CAAC,CAAG,CACpC,CAAC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAInG,eAAe,CAAE,CACpB,GAAI,CACH,KAAM,CAAAoG,QAAQ,CAAG,KAAM,CAAA5J,oBAAoB,CAACwD,eAAe,CAAC,CAC5D,GAAIoG,QAAQ,CAACC,OAAO,CAAE,CACrBtF,YAAY,CACX,GAAGb,iBAAiB,IAAIzB,SAAS,CAAC2B,iBAAiB,CAAC,IAAI3B,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC3F,SACD,CAAC,CACD,KAAM,CAAA+F,kBAAkB,CAAC,CAAC,CAC3B,CAAC,IAAM,CACNzD,YAAY,CAACqF,QAAQ,CAACE,YAAY,CAAE,OAAO,CAAC,CAC7C,CACD,CAAE,MAAOC,KAAK,CAAE,CAAC,CAClB,CACAhG,aAAa,CAAC,KAAK,CAAC,CACpBN,kBAAkB,CAAC,IAAI,CAAC,CACxBE,oBAAoB,CAAC,EAAE,CAAC,CACzB,CAAC,CACD,KAAM,CAAAqG,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACtC,KAAM,CAAAhC,kBAAkB,CAAC,CAAC,CAC3B,CAAC,CACD,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,SAAS,CAAG,CAACjI,SAAS,CAAC,QAAQ,CAAC,CAAEA,SAAS,CAAC,UAAU,CAAC,CAAEA,SAAS,CAAC,OAAO,CAAC,CAAC,CAClF,KAAM,CAAAkI,eAAe,CAAGD,SAAS,CAACpH,SAAS,CAAC,EAAInB,UAAU,CAC1D,MAAO,GAAGM,SAAS,CAAC,IAAI,CAAC,IAAIA,SAAS,CAACkI,eAAe,CAAE,CAAEC,YAAY,CAAED,eAAgB,CAAC,CAAC,IAAIlI,SAAS,CAACN,UAAU,CAAE,CAAEyI,YAAY,CAAE,GAAGzI,UAAU,GAAI,CAAC,CAAC,EAAE,CAC1J,CAAC,CACD,KAAM,CAAA0I,aAAa,CAAGA,CAAA,gBACrBrJ,KAAA,QAAK6G,KAAK,CAAE,CAAEyC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAArD,QAAA,eAC9EjG,IAAA,SACCuJ,SAAS,CAAC,gBAAgB,CAC1B9C,uBAAuB,CAAE,CAAEC,MAAM,CAAExH,MAAO,CAAE,CAC5C,CAAC,cACFc,IAAA,CAACvB,UAAU,EAAC+K,EAAE,CAAE,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAxD,QAAA,CAAE8C,cAAc,CAAC,CAAC,CAAa,CAAC,EAClE,CACL,CAED,KAAM,CAAAW,gBAAgB,CAAGA,CAAA,GAAM,CAC9BhI,aAAa,CAAC,IAAI,CAAC,CACnBC,aAAa,CAAC,EAAE,CAAC,CACjBpB,OAAO,CAAC,CAAC,CACV,CAAC,CAED,mBACCT,KAAA,QAAKyE,EAAE,CAAC,eAAe,CAAA0B,QAAA,eACtBjG,IAAA,CAACnC,MAAM,EACN8L,SAAS,CAAE,CACVC,IAAI,CAAE,CACLrF,EAAE,CAAE,eACL,CAAC,CACDsF,QAAQ,CAAE,CACTL,EAAE,CAAE,CACHM,QAAQ,CAAE,qBACX,CACD,CACD,CAAE,CACF7E,IAAI,CAAE3E,IAAK,CACXC,OAAO,CAAEmJ,gBAAiB,CAC1BK,SAAS,MACTC,QAAQ,CAAC,IAAI,CACbT,SAAS,CAAC,qBAAqB,CAAAtD,QAAA,cAE/BnG,KAAA,CAAChC,aAAa,EAACyL,SAAS,CAAC,6BAA6B,CAAAtD,QAAA,eACrDjG,IAAA,QACCuJ,SAAS,CAAC,eAAe,CACzBhF,EAAE,CAAC,cAAc,CAAA0B,QAAA,cAEjBjG,IAAA,SACLuJ,SAAS,CAAC,OAAO,CACjB5C,KAAK,CAAE,CAAE8C,UAAU,CAAE,gBAAiB,CAAE,CAAAxD,QAAA,CAEjClF,SAAS,CAAC,GAAGN,UAAU,EAAE,CAAE,CAAEyI,YAAY,CAAE,GAAGnI,SAAS,CAACN,UAAU,CAAC,GAAI,CAAC,CAAC,CAC3E,CAAC,CAaG,CAAC,cACNT,IAAA,QACCuJ,SAAS,CAAC,YAAY,CACtBhF,EAAE,CAAC,YAAY,CAAA0B,QAAA,cAEfnG,KAAA,QAAKyJ,SAAS,CAAC,kBAAkB,CAAAtD,QAAA,eAChCjG,IAAA,CAACjC,SAAS,EACTkM,OAAO,CAAC,UAAU,CAClBC,WAAW,CAAEnJ,SAAS,CAAC,QAAQ,CAAC,CAAG,GAAG,CAAGA,SAAS,CAACP,KAAK,CAAE,CAC1D2J,KAAK,CAAErI,WAAY,CACnBsI,QAAQ,CAAGC,CAAC,EAAK,CAChB,KAAM,CAAApC,QAAQ,CAAGoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAC/BpI,cAAc,CAACkG,QAAQ,CAAC,CACxB,GAAIA,QAAQ,GAAK,EAAE,CAAE,CACpBF,iBAAiB,CAAC,CAAC,CACpB,CACD,CAAE,CACFwC,SAAS,CAAGF,CAAC,EAAK,CACjB,GAAIA,CAAC,CAAC7E,GAAG,GAAK,OAAO,CAAE,CACtBC,YAAY,CAAC,CAAC,CACf,CACD,CAAE,CACF8D,SAAS,CAAC,iBAAiB,CAC3BiB,UAAU,CAAE,CACXhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEiB,WAAW,CAAE,SAAU,CAAC,CAAE;AACxE,gDAAgD,CAAE,CAAEC,MAAM,CAAE,mBAAoB,CACjF,CAAC,CACDC,cAAc,cACb3K,IAAA,CAAChC,cAAc,EAAC8L,QAAQ,CAAC,OAAO,CAAA7D,QAAA,cAC/BjG,IAAA,CAAC/B,UAAU,EACV,aAAW,QAAQ,CACnBuI,OAAO,CAAEA,CAAA,GAAMf,YAAY,CAAC,CAAE,CAC9BmF,WAAW,CAAGrF,KAAK,EAAKA,KAAK,CAACsF,cAAc,CAAC,CAAE,CAAA5E,QAAA,cAE/CjG,IAAA,CAACrB,UAAU,GAAE,CAAC,CACH,CAAC,CACE,CAChB,CACDmM,YAAY,CAAEhJ,WAAW,eACxB9B,IAAA,CAAChC,cAAc,EAAC8L,QAAQ,CAAC,KAAK,CAAA7D,QAAA,cAC7BjG,IAAA,CAAC/B,UAAU,EACV,aAAW,OAAO,CAClBuI,OAAO,CAAEA,CAAA,GAAM,CACdzE,cAAc,CAAC,EAAE,CAAC,CAClBgG,iBAAiB,CAAC,CAAC,CACpB,CAAE,CAAA9B,QAAA,cAEFjG,IAAA,CAACpB,SAAS,EAAC4K,EAAE,CAAE,CAAE5C,IAAI,CAAE,KAAM,CAAE,CAAE,CAAC,CACvB,CAAC,CACE,CAElB,CAAE,CACF,CAAC,cACF5G,IAAA,QAAKuJ,SAAS,CAAC,kBAAkB,CAAAtD,QAAA,cAChCnG,KAAA,WACC0G,OAAO,CAAEA,CAAA,GAAM9F,UAAU,CAACD,UAAU,CAAE,CACtC8I,SAAS,CAAC,oBAAoB,CAC9BwB,QAAQ,CAAEjI,QAAQ,CAACkI,iBAAiB,CAAC,CAAC,EAAE,OAAO,CAAG5H,KAAK,EAAE,IAAI,EAAI,CAACA,KAAK,EAAI,CAAC,CAAC,eAAe,CAAE,QAAQ,CAAC,CAACgD,IAAI,CAACC,IAAI,EAAIjD,KAAK,CAACkD,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAE,KAAM,CAAAJ,QAAA,eAElJjG,IAAA,CAACZ,OAAO,GAAE,CAAC,cACXY,IAAA,SAAAiG,QAAA,CAAO,GAAGlF,SAAS,CAAC,QAAQ,CAAC,IAAIA,SAAS,CAACN,UAAU,CAAC,EAAE,CAAO,CAAC,EACzD,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,cAENT,IAAA,QAAKuJ,SAAS,CAAC,sBAAsB,CAAAtD,QAAA,cACpCnG,KAAA,CAAC3B,IAAI,EACJgM,KAAK,CAAEvI,SAAU,CACjBwI,QAAQ,CAAEpC,eAAgB,CAAA/B,QAAA,eAE1BjG,IAAA,CAAC9B,GAAG,EACH+M,KAAK,CAAElK,SAAS,CAAC,QAAQ,CAAE,CAC3ByI,EAAE,CAAE,CACH0B,eAAe,CAAE,oBAAoB,CACrCR,MAAM,CAAE,oBAAoB,CAC5BS,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,cACFpL,IAAA,CAAC9B,GAAG,EACH+M,KAAK,CAAElK,SAAS,CAAC,UAAU,CAAE,CAC7ByI,EAAE,CAAE,CACH0B,eAAe,CAAE,oBAAoB,CACrCR,MAAM,CAAE,oBAAoB,CAC5BS,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,cACFpL,IAAA,CAAC9B,GAAG,EACH+M,KAAK,CAAElK,SAAS,CAAC,OAAO,CAAE,CAC1ByI,EAAE,CAAE,CACH0B,eAAe,CAAE,oBAAoB,CACrCR,MAAM,CAAE,oBAAoB,CAC5BS,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,EACG,CAAC,CACH,CAAC,cAENpL,IAAA,QAAKuJ,SAAS,CAAC,eAAe,CAAAtD,QAAA,cAC7BjG,IAAA,CAACtB,QAAQ,EACR2M,IAAI,CAAErJ,YAAa,CACnB0D,OAAO,CAAEA,OAAQ,CACjB4F,QAAQ,CAAGpF,GAAG,EAAKA,GAAG,CAAClB,OAAQ,CAC/BmD,aAAa,CAAEA,aAAc,CAC7BoD,UAAU,MACVxI,eAAe,CAAEA,eAAgB,CACjCyI,cAAc,CAAC,QAAQ,CACvBC,uBAAuB,CAAEzI,kBAAmB,CAC5C0I,QAAQ,CAAEpI,UAAW,CACrBqI,eAAe,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,CACnCC,UAAU,CAAE,CACXC,kBAAkB,CAAE,CACnBC,gBAAgB,CAAE/K,SAAS,CAAC,kBAAkB,CAC/C,CAAC,CACDgL,WAAW,CAAEhD,cAAc,CAAC,CAC7B,CAAE,CACFiD,iBAAiB,MACjBC,0BAA0B,MAC1B1C,SAAS,CAAC,eAAe,CACzB2C,KAAK,CAAE,CACNC,aAAa,CAAEhD,aAAe;AAC/B,CAAE,CACFK,EAAE,CAAE,CACH,oBAAoB,CAAE,CACrBQ,QAAQ,CAAE,mBAAmB,CAC7B,kBAAkB,CAAE,aACpB;AACA;AACD,CAAC,CACD,qBAAqB,CAAE,CACtBoC,OAAO,CAAE,mBACV,CAAC,CACD,6BAA6B,CAAE,CAC9BhD,OAAO,CAAE,iBAAiB,CAC1BC,UAAU,CAAE,qBACb,CAAC,CACD,oCAAoC,CAAE,CACrCqB,MAAM,CAAE,iBAAiB,CACzBS,KAAK,CAAE,oBAAoB,CAC3BD,eAAe,CAAE,oBAAoB,CACrC,SAAS,CAAE,CACVA,eAAe,CAAE,oBAAsB;AACxC,CACD,CAAC,CACD,6BAA6B,CAAE,CAC9BmB,UAAU,CAAE,6CAA6C,CACzDD,OAAO,CAAE,mBAAmB,CAC5BE,WAAW,CAAE,mBAAmB,CAChCC,MAAM,CAAE,iBACT,CAAC,CACD,kCAAkC,CAAE,CACnC9C,UAAU,CAAE,KACb,CAAC,CACD,uBAAuB,CAAE,CACxByB,eAAe,CAAE,uBAAuB,CACxC,kBAAkB,CAAE,wBACrB,CAAC,CACD,gCAAgC,CAAE,CACjCA,eAAe,CAAE,uBAAuB,CACxC9B,OAAO,CAAE,MACV,CACD,CAAE,CACFoD,SAAS,CAAE,EAAG,CACd,CAAC,CACE,CAAC,EACQ,CAAC,CACT,CAAC,cACT1M,KAAA,CAACjC,MAAM,EACNoH,IAAI,CAAErC,UAAW,CACjBrC,OAAO,CAAEA,CAAA,GAAMsC,aAAa,CAAC,KAAK,CAAE,CACpC4J,UAAU,CAAE,CACX9F,KAAK,CAAE,CACN+F,YAAY,CAAE,KAAK,CACnB1C,QAAQ,CAAE,OAAO,CACjB2C,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,MACZ,CACD,CAAE,CAAA5G,QAAA,eAEFnG,KAAA,CAACzB,WAAW,EAACmL,EAAE,CAAE,CAAE4C,OAAO,CAAE,CAAE,CAAE,CAAAnG,QAAA,eAC/BjG,IAAA,QAAK2G,KAAK,CAAE,CAAEyC,OAAO,CAAE,MAAM,CAAE0D,cAAc,CAAE,QAAQ,CAAEV,OAAO,CAAE,MAAO,CAAE,CAAAnG,QAAA,cAC1EjG,IAAA,QACC2G,KAAK,CAAE,CACNuE,eAAe,CAAE,SAAS,CAC1BwB,YAAY,CAAE,KAAK,CACnBK,KAAK,CAAE,MAAM,CACbR,MAAM,CAAE,MAAM,CACdnD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpByD,cAAc,CAAE,QACjB,CAAE,CAAA7G,QAAA,cAEFjG,IAAA,CAACb,yBAAyB,EAACqK,EAAE,CAAE,CAAE2B,KAAK,CAAE,SAAS,CAAEoB,MAAM,CAAE,MAAM,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClF,CAAC,CACF,CAAC,cACNjN,KAAA,CAACrB,UAAU,EAAC+K,EAAE,CAAE,CAAE4B,QAAQ,CAAE,iBAAiB,CAAE3B,UAAU,CAAE,GAAG,CAAE2C,OAAO,CAAE,QAAS,CAAE,CAAAnG,QAAA,EAClFlF,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAC,CAACA,SAAS,CAAC2B,iBAAiB,CAAC,EACxC,CAAC,EACD,CAAC,cAEd1C,IAAA,CAAClC,aAAa,EAAC0L,EAAE,CAAE,CAAE4C,OAAO,CAAE,iBAAkB,CAAE,CAAAnG,QAAA,cACjDjG,IAAA,CAAC1B,iBAAiB,EAACqI,KAAK,CAAE,CAAEyE,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAAlF,QAAA,CAC5DlF,SAAS,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,IAAIyB,iBAAiB,IAAIzB,SAAS,CAAC,wCAAwC,CAAC,EAAE,CAAC,CAC3F,CAAC,CACN,CAAC,cAEhBjB,KAAA,CAACvB,aAAa,EAACiL,EAAE,CAAE,CAAEsD,cAAc,CAAE,eAAe,CAAEE,SAAS,CAAE,+BAAgC,CAAE,CAAA/G,QAAA,eAClGjG,IAAA,CAACxB,MAAM,EACNgI,OAAO,CAAEA,CAAA,GAAM3D,aAAa,CAAC,KAAK,CAAE,CACpC2G,EAAE,CAAE,CACH2B,KAAK,CAAE,SAAS,CAChBT,MAAM,CAAE,mBAAmB,CAC3BgC,YAAY,CAAE,KAAK,CACnBO,aAAa,CAAE,YAAY,CAC3Bb,OAAO,CAAE,uBAAuB,CAChCc,UAAU,CAAE,0BACb,CAAE,CAAAjH,QAAA,CAEDlF,SAAS,CAAC,QAAQ,CAAC,CACb,CAAC,cACTf,IAAA,CAACxB,MAAM,EACNgI,OAAO,CAAEiC,YAAa,CACtBe,EAAE,CAAE,CACH0B,eAAe,CAAE,oBAAoB,CACrCC,KAAK,CAAE,MAAM,CACbuB,YAAY,CAAE,KAAK,CACnBO,aAAa,CAAE,YAAY,CAC3Bb,OAAO,CAAE,uBAAuB,CAChCc,UAAU,CAAE,0BACZ;AACA;AACA;AACD,CAAE,CAAAjH,QAAA,CAEDlF,SAAS,CAAC,QAAQ,CAAC,CACb,CAAC,EACK,CAAC,EACT,CAAC,CACRmB,iBAAiB,EAAIE,qBAAqB,eAC1CpC,IAAA,CAACX,sBAAsB,EACtB4F,IAAI,CAAE/C,iBAAkB,CACxBiL,WAAW,CAAEA,CAAA,GAAMhL,oBAAoB,CAAC,KAAK,CAAE,CAC/CiL,WAAW,CAAEhL,qBAAsB,CACnCiL,cAAc,CAAEvE,kBAAmB,CACnCtF,IAAI,CAAEA,IAAK,CACX,CACD,EACG,CAAC,CAER,CAAC,CACD,OAASrD,WAAW,EACpB,cAAe,CAAAC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}