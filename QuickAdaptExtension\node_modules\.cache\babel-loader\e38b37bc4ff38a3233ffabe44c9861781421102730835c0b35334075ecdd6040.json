{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{<PERSON>,Button,Popover,Typography,IconButton,Tooltip}from\"@mui/material\";import{ChromePicker}from\"react-color\";import{deleteicon,copyicon,settingsicon}from\"../../../assets/icons/icons\";import useDrawerStore from\"../../../store/drawerStore\";import AddIcon from\"@mui/icons-material/Add\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ButtonSection=_ref=>{var _buttonsContainer$fin,_buttonsContainer$fin2;let{buttonColor,setButtonColor,isBanner,isCloneDisabled,onDelete,onClone}=_ref;const{buttonsContainer,cloneButtonContainer,updateButton,addNewButton,deleteButton,deleteButtonContainer,updateContainer,setSettingAnchorEl,selectedTemplate,selectedTemplateTour,setSelectedTemplate,buttonProperty,setButtonProperty,btnBgColor,btnBorderColor,btnTextColor,setButtonId,setCuntainerId,createWithAI,currentStep,ensureAnnouncementButtonContainer,closeAllButtonPopups}=useDrawerStore(state=>state);const{t:translate}=useTranslation();const[isEditingPrevious,setIsEditingPrevious]=useState(false);const[isEditingContinue,setIsEditingContinue]=useState(false);const[previousButtonText,setPreviousButtonText]=useState(\"Previous\");const[continueButtonText,setContinueButtonText]=useState(\"Continue\");const[anchorEl,setAnchorEl]=useState(null);const[selectedColor,setSelectedColor]=useState(\"#313030\");const[colorPickerAnchorEl,setColorPickerAnchorEl]=useState(null);const[buttonText,setButtonText]=useState(\"Continue\");const[buttonToEdit,setButtonToEdit]=useState(null);const[isDeleteIcon,setIsDeleteIcon]=useState(\"\");const[currentContainerId,setCurrentContainerId]=useState(\"\");const[currentButtonId,setCurrentButtonId]=useState(\"\");const[isEditingButton,setIsEditingButton]=useState(false);const[isEditing,setIsEditing]=useState(null);// Default button color\nlet clickTimeout;useEffect(()=>{setAnchorEl(null);setButtonProperty(false);},[]);// Empty dependency array ensures it runs only once\n// Listen for global close all button popups trigger\nuseEffect(()=>{if(closeAllButtonPopups>0){setAnchorEl(null);setColorPickerAnchorEl(null);setButtonToEdit(null);}},[closeAllButtonPopups]);const handleClick=(event,buttonId)=>{const target=event.currentTarget;clickTimeout=setTimeout(()=>{setAnchorEl(target);setCurrentButtonId(buttonId);setIsEditingButton(false);handleEditButtonName(currentContainerId,buttonId,\"isEditing\",false);},200);};const handleClose=()=>{setAnchorEl(null);setSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});setButtonToEdit(null);};const handlePreviousTextChange=event=>{setPreviousButtonText(event.target.value);};const handleContinueTextChange=event=>{setContinueButtonText(event.target.value);};const handleBackgroundColorClick=event=>{setColorPickerAnchorEl(event.currentTarget);};const handleColorChange=color=>{setSelectedColor(color.hex);// Update the backgroundColor in the container's style\nupdateContainer(currentContainerId,\"style\",{backgroundColor:color.hex});// Also update the BackgroundColor property at the ButtonSection level\nupdateContainer(currentContainerId,\"BackgroundColor\",color.hex);};const handleCloseColorPicker=()=>{setColorPickerAnchorEl(null);};const open=Boolean(anchorEl);// const open = Boolean(anchorEl && !isEditingButton);\nconst id=open?\"button-popover\":undefined;const colorPickerOpen=Boolean(colorPickerAnchorEl);const toggleEdit=button=>{if(button===\"Previous\"){setIsEditingPrevious(true);}else if(button===\"Continue\"){setIsEditingContinue(true);}};const handlePreviousBlur=()=>{setIsEditingPrevious(false);};const handleContinueBlur=()=>{setIsEditingContinue(false);};const handleEditButtonName=(containerId,buttonId,isEditing,value)=>{clearTimeout(clickTimeout);setIsEditingButton(true);updateButton(containerId,buttonId,isEditing,value);};const handleChangeButton=(containerId,buttonId,value)=>{updateButton(containerId,buttonId,\"type\",value);};const handleEditButtonText=(containerId,buttonId,newText)=>{updateButton(containerId,buttonId,\"name\",newText);setButtonToEdit(null);// Exit edit mode after saving\n};//   const [buttonCount, setButtonCount] = useState(0);\nconst handleAddIconClick=containerId=>{//  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\naddNewButton({id:crypto.randomUUID(),name:\"Button 1\",position:\"center\",type:\"primary\",isEditing:false,index:0,style:{backgroundColor:\"#5F9EA0\"}},containerId);// setButtonCount(buttonCount + 1);\n};// shouldShowAddBtn will be calculated per section inside the map\nconst currentContainerColor=((_buttonsContainer$fin=buttonsContainer.find(item=>item.id===currentContainerId))===null||_buttonsContainer$fin===void 0?void 0:(_buttonsContainer$fin2=_buttonsContainer$fin.style)===null||_buttonsContainer$fin2===void 0?void 0:_buttonsContainer$fin2.backgroundColor)||\"#5f9ea0\";const handleDelteContainer=()=>{deleteButtonContainer(currentContainerId);setAnchorEl(null);// Call the onDelete callback if provided\nif(onDelete){onDelete();}};setButtonId(currentButtonId);setCuntainerId(currentButtonId);const handleSettingIconClick=event=>{if(selectedTemplate===\"Banner\"||selectedTemplateTour===\"Banner\"){setSettingAnchorEl({containerId:currentContainerId,buttonId:currentButtonId,// @ts-ignore\nvalue:event.currentTarget});setButtonProperty(true);}else{setSettingAnchorEl({containerId:currentContainerId,buttonId:currentButtonId,// @ts-ignore\nvalue:event.currentTarget});}setAnchorEl(null);setButtonToEdit(null);//setAnchorEl(null);\n};// Determine which containers to use based on whether this is an AI announcement\nconst isAIAnnouncement=createWithAI&&(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\");const isTourAnnouncement=createWithAI&&selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Announcement\";const currentStepIndex=currentStep-1;let containersToRender=[];if(isAIAnnouncement){// Use the store function to ensure Button containers exist\ncontainersToRender=ensureAnnouncementButtonContainer(currentStepIndex,isTourAnnouncement);}else{// For banners and non-AI content, use buttonsContainer\ncontainersToRender=buttonsContainer;}return/*#__PURE__*/_jsxs(_Fragment,{children:[containersToRender.map(buttonItem=>{return/*#__PURE__*/_jsxs(Box,{component:\"div\",id:buttonItem.id// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n,sx:{height:isBanner?\"40px !important\":\"60px !important\",width:\"100%\",display:\"flex\",alignItems:\"center\",gap:\"8px\",padding:\"0 16px\",boxSizing:\"border-box\",backgroundColor:buttonItem.style.backgroundColor?buttonItem.style.backgroundColor:btnBgColor,justifyContent:\"center\",// \"&.qadpt-banner-btn\": { height: \"40px\" },\n\"&:hover .add-button-icon\":{display:\"flex\"}},onMouseEnter:e=>setCurrentContainerId(e.currentTarget.id),children:[buttonItem.buttons.map(item=>/*#__PURE__*/_jsxs(Box,{sx:{position:\"relative\",display:\"flex\",justifyContent:\"center\",alignItems:\"center\",\"&:hover .delete-icon\":{// Add this hover effect to display the delete icon when the button is hovered\nopacity:1}},onMouseEnter:()=>setCurrentContainerId(buttonItem.id),onMouseLeave:()=>setIsDeleteIcon(\"\"),children:[/*#__PURE__*/_jsx(Button,{id:item.id,variant:\"contained\",sx:{lineHeight:\"var(--button-lineheight)\",padding:\"var(--button-padding)\",borderRadius:\"8px\",color:item.style.color||\"#fff\",border:item.style.borderColor?`2px solid ${item.style.borderColor}`:\"none\",textTransform:\"none\",backgroundColor:item.style.backgroundColor,boxShadow:\"none\",\"&:hover\":{boxShadow:\"none !important\"}},onClick:e=>handleClick(e,item.id),children:/*#__PURE__*/_jsx(Typography,{style:{color:btnTextColor},children:item.name})}),buttonItem.buttons.length>1&&/*#__PURE__*/_jsx(IconButton,{size:\"small\",className:\"delete-icon\",sx:{position:\"absolute\",right:\"-10px\",top:\"0\",transform:\"translateY(-50%)\",backgroundColor:\"#fff\",boxShadow:\"rgba(0, 0, 0, 0.4) 0px 2px 6px\",opacity:0,// Initially hidden\ntransition:\"opacity 0.3s ease\",// Smooth transition\nzIndex:1,padding:\"3px !important\",\"&:hover\":{backgroundColor:\"#fff\",boxShadow:\"none !important\"},span:{height:\"16px\"},\"& svg\":{width:\"14px\",// Set the width of the SVG\nheight:\"14px\",// Set the height of the SVG\npath:{fill:\"#ff0000\"}}},onClick:()=>deleteButton(buttonItem.id,item.id),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon}})})]},item.id)),(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\")&&buttonItem.buttons.length<4?/*#__PURE__*/_jsx(IconButton,{className:\"add-button-icon\",sx:{backgroundColor:\"#5F9EA0\",cursor:\"pointer\",zIndex:1000,padding:\"6px !important\",display:\"none\",\"&:hover\":{backgroundColor:\"#70afaf\"}}// sx={sideAddButtonStyle}\n,onClick:()=>{handleAddIconClick(buttonItem.id);if(onClone){onClone();}},children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\",sx:{color:\"#fff\"}})}):null]});}),/*#__PURE__*/_jsx(Popover,{className:\"qadpt-bunprop\",id:id,open:open,anchorEl:anchorEl,onClose:handleClose,anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"},sx:{marginTop:isBanner?\"100px !important\":\"-5px\"}// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n,children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:\"8px\",padding:\"8px\"},children:[/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Settings\"),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSettingIconClick,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:settingsicon}})})}),(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\")&&/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Background Color\"),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleBackgroundColorClick,children:/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:selectedColor,borderRadius:\"100%\",width:\"20px\",height:\"20px\",display:\"inline-block\",marginTop:\"-3px\"}})})}),(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\")&&/*#__PURE__*/_jsx(Tooltip,{title:isCloneDisabled?translate(\"Maximum limit of 3 Button sections reached\"):translate(\"Clone Button\"),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{cloneButtonContainer(currentContainerId);if(onClone){onClone();}},disabled:isCloneDisabled,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:isCloneDisabled?0.5:1}})})}),(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\")&&/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Delete Button\"),arrow:true,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleDelteContainer,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{marginTop:\"-3px\"}})})})]})}),/*#__PURE__*/_jsx(Popover,{open:colorPickerOpen,anchorEl:colorPickerAnchorEl,onClose:handleCloseColorPicker,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(ChromePicker,{color:currentContainerColor,onChange:handleColorChange}),/*#__PURE__*/_jsx(\"style\",{children:`\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `})]})})]});};export default ButtonSection;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Popover", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ButtonSection", "_ref", "_buttonsContainer$fin", "_buttonsContainer$fin2", "buttonColor", "setButtonColor", "isBanner", "isCloneDisabled", "onDelete", "onClone", "buttonsContainer", "cloneButtonContainer", "updateButton", "addNewButton", "deleteButton", "deleteButtonContainer", "updateContainer", "setSettingAnchorEl", "selectedTemplate", "selectedTemplateTour", "setSelectedTemplate", "buttonProperty", "setButtonProperty", "btnBgColor", "btnBorderColor", "btnTextColor", "setButtonId", "setCuntainerId", "createWithAI", "currentStep", "ensureAnnouncementButtonContainer", "closeAllButtonPopups", "state", "t", "translate", "isEditingPrevious", "setIsEditingPrevious", "isEditingContinue", "setIsEditingContinue", "previousButtonText", "setPreviousButtonText", "continueButtonText", "setContinueButtonText", "anchorEl", "setAnchorEl", "selectedColor", "setSelectedColor", "colorPickerAnchorEl", "setColorPickerAnchorEl", "buttonText", "setButtonText", "buttonToEdit", "setButtonToEdit", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "isEditingButton", "setIsEditingButton", "isEditing", "setIsEditing", "clickTimeout", "handleClick", "event", "buttonId", "target", "currentTarget", "setTimeout", "handleEditButtonName", "handleClose", "containerId", "value", "handlePreviousTextChange", "handleContinueTextChange", "handleBackgroundColorClick", "handleColorChange", "color", "hex", "backgroundColor", "handleCloseColorPicker", "open", "Boolean", "id", "undefined", "colorPickerOpen", "toggleEdit", "button", "handlePreviousBlur", "handleContinueBlur", "clearTimeout", "handleChangeButton", "handleEditButtonText", "newText", "handleAddIconClick", "crypto", "randomUUID", "name", "position", "type", "index", "style", "currentContainerColor", "find", "item", "handleDelteContainer", "handleSettingIconClick", "isAIAnnouncement", "isTourAnnouncement", "currentStepIndex", "containersToRender", "children", "map", "buttonItem", "component", "sx", "height", "width", "display", "alignItems", "gap", "padding", "boxSizing", "justifyContent", "onMouseEnter", "e", "buttons", "opacity", "onMouseLeave", "variant", "lineHeight", "borderRadius", "border", "borderColor", "textTransform", "boxShadow", "onClick", "length", "size", "className", "right", "top", "transform", "transition", "zIndex", "span", "path", "fill", "dangerouslySetInnerHTML", "__html", "cursor", "fontSize", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "marginTop", "title", "arrow", "disabled", "onChange"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Button.tsx"], "sourcesContent": ["import React, { useState,useEffect } from \"react\";\r\nimport { Box, Button, Popover, Typography, Text<PERSON>ield, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ButtonSettings from \"../../guideBanners/selectedpopupfields/ImageProperties\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSection: React.FC<{ buttonColor: string; setButtonColor: (str: string) => void; isBanner: boolean; index?: number; isCloneDisabled?: boolean; onDelete?: () => void; onClone?: () => void;}> = ({\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tisBanner,\r\n\tisCloneDisabled,\r\n\tonDelete,\r\n\tonClone\r\n}) => {\r\n\tconst {\r\n\t\tbuttonsContainer,\r\n\t\tcloneButtonContainer,\r\n\t\tupdateButton,\r\n\t\taddNewButton,\r\n\t\tdeleteButton,\r\n\t\tdeleteButtonContainer,\r\n\t\tupdateContainer,\r\n\t\tsetSettingAnchorEl,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetSelectedTemplate,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tbtnBgColor,\r\n\t\tbtnBorderColor,\r\n\t\tbtnTextColor,\r\n\t\tsetButtonId,\r\n\t\tsetCuntainerId,\r\n\t\tcreateWithAI,\r\n\t\tcurrentStep,\r\n\t\tensureAnnouncementButtonContainer,\r\n\t\tcloseAllButtonPopups,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isEditingPrevious, setIsEditingPrevious] = useState<boolean>(false);\r\n\tconst [isEditingContinue, setIsEditingContinue] = useState<boolean>(false);\r\n\tconst [previousButtonText, setPreviousButtonText] = useState<string>(\"Previous\");\r\n\tconst [continueButtonText, setContinueButtonText] = useState<string>(\"Continue\");\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [buttonText, setButtonText] = useState<string>(\"Continue\");\r\n\tconst [buttonToEdit, setButtonToEdit] = useState<string | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\tconst [isEditingButton, setIsEditingButton] = useState(false);\r\n\tconst [isEditing, setIsEditing] = useState<string | null>(null);\r\n\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\r\n\tuseEffect(() => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonProperty(false);\r\n\t}, []); // Empty dependency array ensures it runs only once\r\n\r\n\t// Listen for global close all button popups trigger\r\n\tuseEffect(() => {\r\n\t\tif (closeAllButtonPopups > 0) {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\tsetColorPickerAnchorEl(null);\r\n\t\t\tsetButtonToEdit(null);\r\n\t\t}\r\n\t}, [closeAllButtonPopups]);\r\n\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\r\n\t\tclickTimeout = setTimeout(() => {\r\n\t\t\tsetAnchorEl(target);\r\n\t\t\tsetCurrentButtonId(buttonId);\r\n\t\t\tsetIsEditingButton(false);\r\n\t\t\thandleEditButtonName(currentContainerId, buttonId, \"isEditing\", false);\r\n\t\t}, 200);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetButtonToEdit(null);\r\n\t};\r\n\r\n\tconst handlePreviousTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetPreviousButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleContinueTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetContinueButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(currentContainerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(currentContainerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\tconst id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\tconst toggleEdit = (button: \"Previous\" | \"Continue\") => {\r\n\t\tif (button === \"Previous\") {\r\n\t\t\tsetIsEditingPrevious(true);\r\n\t\t} else if (button === \"Continue\") {\r\n\t\t\tsetIsEditingContinue(true);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handlePreviousBlur = () => {\r\n\t\tsetIsEditingPrevious(false);\r\n\t};\r\n\r\n\tconst handleContinueBlur = () => {\r\n\t\tsetIsEditingContinue(false);\r\n\t};\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\tclearTimeout(clickTimeout);\r\n\t\tsetIsEditingButton(true);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t};\r\n\tconst handleEditButtonText = (containerId: string, buttonId: string, newText: string) => {\r\n\t\tupdateButton(containerId, buttonId, \"name\", newText);\r\n\t\tsetButtonToEdit(null); // Exit edit mode after saving\r\n\t};\r\n\t//   const [buttonCount, setButtonCount] = useState(0);\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\t//  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\r\n\t\taddNewButton(\r\n\t\t\t{\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\tname: \"Button 1\",\r\n\t\t\t\tposition: \"center\",\r\n\t\t\t\ttype: \"primary\",\r\n\t\t\t\tisEditing: false,\r\n\t\t\t\tindex: 0,\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tcontainerId\r\n\t\t);\r\n\t\t// setButtonCount(buttonCount + 1);\r\n\t};\r\n\r\n\t// shouldShowAddBtn will be calculated per section inside the map\r\n\r\n\tconst currentContainerColor =\r\n\t\tbuttonsContainer.find((item: any) => item.id === currentContainerId)?.style?.backgroundColor || \"#5f9ea0\";\r\n\r\n\tconst handleDelteContainer = () => {\r\n\t\tdeleteButtonContainer(currentContainerId);\r\n\t\tsetAnchorEl(null);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\tsetButtonId(currentButtonId);\r\n\tsetCuntainerId(currentButtonId);\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tif (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t\tsetButtonProperty(true);\r\n\t\t} else {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t}\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonToEdit(null);\r\n\r\n\t\t//setAnchorEl(null);\r\n\t};\r\n\r\n\t// Determine which containers to use based on whether this is an AI announcement\r\n\tconst isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\tconst isTourAnnouncement = createWithAI && selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\";\r\n\tconst currentStepIndex = currentStep - 1;\r\n\r\n\tlet containersToRender: any[] = [];\r\n\r\n\tif (isAIAnnouncement) {\r\n\t\t// Use the store function to ensure Button containers exist\r\n\t\tcontainersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);\r\n\t} else {\r\n\t\t// For banners and non-AI content, use buttonsContainer\r\n\t\tcontainersToRender = buttonsContainer;\r\n\t}\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{containersToRender.map((buttonItem: any) => {\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tcomponent=\"div\"\r\n\t\t\t\t\t\tid={buttonItem.id}\r\n\t\t\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\theight: isBanner ? \"40px !important\" : \"60px !important\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\tpadding: \"0 16px\",\r\n\t\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\t\tbackgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,\r\n\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t// \"&.qadpt-banner-btn\": { height: \"40px\" },\r\n\t\t\t\t\t\t\t\"&:hover .add-button-icon\": {\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{buttonItem.buttons.map((item: any) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover .delete-icon\": {\r\n\t\t\t\t\t\t\t\t\t\t// Add this hover effect to display the delete icon when the button is hovered\r\n\t\t\t\t\t\t\t\t\t\topacity: 1,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonMouseEnter={() => setCurrentContainerId(buttonItem.id)}\r\n\t\t\t\t\t\t\t\tonMouseLeave={() => setIsDeleteIcon(\"\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tcolor: item.style.color || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\tborder: item.style.borderColor\r\n\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${item.style.borderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, item.id)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography style={{ color: btnTextColor }}>{item.name}</Typography>\r\n\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t{/* Delete Icon - Right Side, only visible on hover */}\r\n\t\t\t\t\t\t\t\t{buttonItem.buttons.length > 1 && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"delete-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttop: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\ttransform: \"translateY(-50%)\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0, // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\t\ttransition: \"opacity 0.3s ease\", // Smooth transition\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"16px\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", // Set the width of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", // Set the height of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => deleteButton(buttonItem.id, item.id)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") &&\r\n\t\t\t\t\t\tbuttonItem.buttons.length < 4  ? (\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tclassName=\"add-button-icon\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\thandleAddIconClick(buttonItem.id);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t<Popover\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-bunprop\"\r\n\t\t\t\tid={id}\r\n\t\t\t\topen={open}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tmarginTop: isBanner ? \"100px !important\" : \"-5px\",\r\n\t\t\t\t}}\r\n\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\tPopperProps={{\r\n\t\t\t\t\tsx: {\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<span>  */}\r\n\t\t\t\t\t{/* <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\", opacity: 0.5 }}\r\n\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\tonClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t{/* </span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\t\t//onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n\t\t\t\t\t*/}\r\n\r\n\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t<Tooltip title={translate(\"Settings\")} arrow>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Button sections reached\") : translate(\"Clone Button\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\tcloneButtonContainer(currentContainerId);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Button\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ marginTop: \"-3px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAACC,SAAS,KAAQ,OAAO,CACjD,OAASC,GAAG,CAAEC,MAAM,CAAEC,OAAO,CAAEC,UAAU,CAAaC,UAAU,CAAEC,OAAO,KAAQ,eAAe,CAChG,OAASC,YAAY,KAAqB,aAAa,CACvD,OAASC,UAAU,CAAEC,QAAQ,CAAEC,YAAY,KAAuC,6BAA6B,CAC/G,MAAO,CAAAC,cAAc,KAAmB,4BAA4B,CACpE,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAG7C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,aAAkM,CAAGC,IAAA,EAOrM,KAAAC,qBAAA,CAAAC,sBAAA,IAPsM,CAC3MC,WAAW,CACXC,cAAc,CACdC,QAAQ,CACRC,eAAe,CACfC,QAAQ,CACRC,OACD,CAAC,CAAAR,IAAA,CACA,KAAM,CACLS,gBAAgB,CAChBC,oBAAoB,CACpBC,YAAY,CACZC,YAAY,CACZC,YAAY,CACZC,qBAAqB,CACrBC,eAAe,CACfC,kBAAkB,CAClBC,gBAAgB,CAChBC,oBAAoB,CACpBC,mBAAmB,CACnBC,cAAc,CACdC,iBAAiB,CACjBC,UAAU,CACVC,cAAc,CACdC,YAAY,CACZC,WAAW,CACXC,cAAc,CACdC,YAAY,CACZC,WAAW,CACXC,iCAAiC,CACjCC,oBACD,CAAC,CAAGxC,cAAc,CAAEyC,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGzC,cAAc,CAAC,CAAC,CACzC,KAAM,CAAC0C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzD,QAAQ,CAAU,KAAK,CAAC,CAC1E,KAAM,CAAC0D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3D,QAAQ,CAAU,KAAK,CAAC,CAC1E,KAAM,CAAC4D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG7D,QAAQ,CAAS,UAAU,CAAC,CAChF,KAAM,CAAC8D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/D,QAAQ,CAAS,UAAU,CAAC,CAChF,KAAM,CAACgE,QAAQ,CAAEC,WAAW,CAAC,CAAGjE,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACkE,aAAa,CAAEC,gBAAgB,CAAC,CAAGnE,QAAQ,CAAS,SAAS,CAAC,CACrE,KAAM,CAACoE,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrE,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAACsE,UAAU,CAAEC,aAAa,CAAC,CAAGvE,QAAQ,CAAS,UAAU,CAAC,CAChE,KAAM,CAACwE,YAAY,CAAEC,eAAe,CAAC,CAAGzE,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAAC0E,YAAY,CAAEC,eAAe,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC4E,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAAC8E,eAAe,CAAEC,kBAAkB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACkF,SAAS,CAAEC,YAAY,CAAC,CAAGnF,QAAQ,CAAgB,IAAI,CAAC,CAE/D;AACA,GAAI,CAAAoF,YAA4B,CAEhCnF,SAAS,CAAC,IAAM,CACfgE,WAAW,CAAC,IAAI,CAAC,CACjBtB,iBAAiB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA1C,SAAS,CAAC,IAAM,CACf,GAAImD,oBAAoB,CAAG,CAAC,CAAE,CAC7Ba,WAAW,CAAC,IAAI,CAAC,CACjBI,sBAAsB,CAAC,IAAI,CAAC,CAC5BI,eAAe,CAAC,IAAI,CAAC,CACtB,CACD,CAAC,CAAE,CAACrB,oBAAoB,CAAC,CAAC,CAE1B,KAAM,CAAAiC,WAAW,CAAGA,CAACC,KAAoC,CAAEC,QAAgB,GAAK,CAC/E,KAAM,CAAAC,MAAM,CAAGF,KAAK,CAACG,aAAa,CAElCL,YAAY,CAAGM,UAAU,CAAC,IAAM,CAC/BzB,WAAW,CAACuB,MAAM,CAAC,CACnBT,kBAAkB,CAACQ,QAAQ,CAAC,CAC5BN,kBAAkB,CAAC,KAAK,CAAC,CACzBU,oBAAoB,CAACf,kBAAkB,CAAEW,QAAQ,CAAE,WAAW,CAAE,KAAK,CAAC,CACvE,CAAC,CAAE,GAAG,CAAC,CACR,CAAC,CAED,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACzB3B,WAAW,CAAC,IAAI,CAAC,CACjB3B,kBAAkB,CAAC,CAAEuD,WAAW,CAAE,EAAE,CAAEN,QAAQ,CAAE,EAAE,CAAEO,KAAK,CAAE,IAAK,CAAC,CAAC,CAClErB,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAAsB,wBAAwB,CAAIT,KAA0C,EAAK,CAChFzB,qBAAqB,CAACyB,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAE,wBAAwB,CAAIV,KAA0C,EAAK,CAChFvB,qBAAqB,CAACuB,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAG,0BAA0B,CAAIX,KAAoC,EAAK,CAC5EjB,sBAAsB,CAACiB,KAAK,CAACG,aAAa,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAS,iBAAiB,CAAIC,KAAkB,EAAK,CACjDhC,gBAAgB,CAACgC,KAAK,CAACC,GAAG,CAAC,CAC3B;AACA/D,eAAe,CAACuC,kBAAkB,CAAE,OAAO,CAAE,CAC5CyB,eAAe,CAAEF,KAAK,CAACC,GACxB,CAAC,CAAC,CAEF;AACA/D,eAAe,CAACuC,kBAAkB,CAAE,iBAAiB,CAAEuB,KAAK,CAACC,GAAG,CAAC,CAClE,CAAC,CAED,KAAM,CAAAE,sBAAsB,CAAGA,CAAA,GAAM,CACpCjC,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAkC,IAAI,CAAGC,OAAO,CAACxC,QAAQ,CAAC,CAC9B;AACA,KAAM,CAAAyC,EAAE,CAAGF,IAAI,CAAG,gBAAgB,CAAGG,SAAS,CAC9C,KAAM,CAAAC,eAAe,CAAGH,OAAO,CAACpC,mBAAmB,CAAC,CACpD,KAAM,CAAAwC,UAAU,CAAIC,MAA+B,EAAK,CACvD,GAAIA,MAAM,GAAK,UAAU,CAAE,CAC1BpD,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAC,IAAM,IAAIoD,MAAM,GAAK,UAAU,CAAE,CACjClD,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CACD,CAAC,CAED,KAAM,CAAAmD,kBAAkB,CAAGA,CAAA,GAAM,CAChCrD,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAsD,kBAAkB,CAAGA,CAAA,GAAM,CAChCpD,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CAAC,CACD,KAAM,CAAAgC,oBAAoB,CAAGA,CAC5BE,WAAmB,CACnBN,QAAgB,CAChBL,SAAwB,CACxBY,KAA6B,GACzB,CACJkB,YAAY,CAAC5B,YAAY,CAAC,CAC1BH,kBAAkB,CAAC,IAAI,CAAC,CACxBhD,YAAY,CAAC4D,WAAW,CAAEN,QAAQ,CAAEL,SAAS,CAAEY,KAAK,CAAC,CACtD,CAAC,CAED,KAAM,CAAAmB,kBAAkB,CAAGA,CAACpB,WAAmB,CAAEN,QAAgB,CAAEO,KAA6B,GAAK,CACpG7D,YAAY,CAAC4D,WAAW,CAAEN,QAAQ,CAAE,MAAM,CAAEO,KAAK,CAAC,CACnD,CAAC,CACD,KAAM,CAAAoB,oBAAoB,CAAGA,CAACrB,WAAmB,CAAEN,QAAgB,CAAE4B,OAAe,GAAK,CACxFlF,YAAY,CAAC4D,WAAW,CAAEN,QAAQ,CAAE,MAAM,CAAE4B,OAAO,CAAC,CACpD1C,eAAe,CAAC,IAAI,CAAC,CAAE;AACxB,CAAC,CACD;AACA,KAAM,CAAA2C,kBAAkB,CAAIvB,WAAmB,EAAK,CACnD;AACA3D,YAAY,CACX,CACCuE,EAAE,CAAEY,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,QAAQ,CAClBC,IAAI,CAAE,SAAS,CACfvC,SAAS,CAAE,KAAK,CAChBwC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,CACNtB,eAAe,CAAE,SAClB,CACD,CAAC,CACDR,WACD,CAAC,CACD;AACD,CAAC,CAED;AAEA,KAAM,CAAA+B,qBAAqB,CAC1B,EAAArG,qBAAA,CAAAQ,gBAAgB,CAAC8F,IAAI,CAAEC,IAAS,EAAKA,IAAI,CAACrB,EAAE,GAAK7B,kBAAkB,CAAC,UAAArD,qBAAA,kBAAAC,sBAAA,CAApED,qBAAA,CAAsEoG,KAAK,UAAAnG,sBAAA,iBAA3EA,sBAAA,CAA6E6E,eAAe,GAAI,SAAS,CAE1G,KAAM,CAAA0B,oBAAoB,CAAGA,CAAA,GAAM,CAClC3F,qBAAqB,CAACwC,kBAAkB,CAAC,CACzCX,WAAW,CAAC,IAAI,CAAC,CAEjB;AACA,GAAIpC,QAAQ,CAAE,CACbA,QAAQ,CAAC,CAAC,CACX,CACD,CAAC,CACDkB,WAAW,CAAC+B,eAAe,CAAC,CAC5B9B,cAAc,CAAC8B,eAAe,CAAC,CAE/B,KAAM,CAAAkD,sBAAsB,CAAI1C,KAAoC,EAAK,CACxE,GAAI/C,gBAAgB,GAAK,QAAQ,EAAIC,oBAAoB,GAAK,QAAQ,CAAE,CACvEF,kBAAkB,CAAC,CAClBuD,WAAW,CAAEjB,kBAAkB,CAC/BW,QAAQ,CAAET,eAAe,CACzB;AACAgB,KAAK,CAAER,KAAK,CAACG,aACd,CAAC,CAAC,CACF9C,iBAAiB,CAAC,IAAI,CAAC,CACxB,CAAC,IAAM,CACNL,kBAAkB,CAAC,CAClBuD,WAAW,CAAEjB,kBAAkB,CAC/BW,QAAQ,CAAET,eAAe,CACzB;AACAgB,KAAK,CAAER,KAAK,CAACG,aACd,CAAC,CAAC,CACH,CACAxB,WAAW,CAAC,IAAI,CAAC,CACjBQ,eAAe,CAAC,IAAI,CAAC,CAErB;AACD,CAAC,CAED;AACA,KAAM,CAAAwD,gBAAgB,CAAGhF,YAAY,GAAKV,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,CAAC,CACzH,KAAM,CAAA0F,kBAAkB,CAAGjF,YAAY,EAAIV,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,cAAc,CACjH,KAAM,CAAA2F,gBAAgB,CAAGjF,WAAW,CAAG,CAAC,CAExC,GAAI,CAAAkF,kBAAyB,CAAG,EAAE,CAElC,GAAIH,gBAAgB,CAAE,CACrB;AACAG,kBAAkB,CAAGjF,iCAAiC,CAACgF,gBAAgB,CAAED,kBAAkB,CAAC,CAC7F,CAAC,IAAM,CACN;AACAE,kBAAkB,CAAGrG,gBAAgB,CACtC,CAEA,mBACCb,KAAA,CAAAE,SAAA,EAAAiH,QAAA,EACED,kBAAkB,CAACE,GAAG,CAAEC,UAAe,EAAK,CAC5C,mBACCrH,KAAA,CAAChB,GAAG,EACHsI,SAAS,CAAC,KAAK,CACf/B,EAAE,CAAE8B,UAAU,CAAC9B,EACf;AAAA,CACAgC,EAAE,CAAE,CACHC,MAAM,CAAE/G,QAAQ,CAAG,iBAAiB,CAAG,iBAAiB,CACxDgH,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,YAAY,CACvB3C,eAAe,CAAEkC,UAAU,CAACZ,KAAK,CAACtB,eAAe,CAAGkC,UAAU,CAACZ,KAAK,CAACtB,eAAe,CAAGzD,UAAU,CACjGqG,cAAc,CAAE,QAAQ,CACxB;AACA,0BAA0B,CAAE,CAC3BL,OAAO,CAAE,MACV,CACD,CAAE,CACFM,YAAY,CAAGC,CAAC,EAAKtE,qBAAqB,CAACsE,CAAC,CAAC1D,aAAa,CAACgB,EAAE,CAAE,CAAA4B,QAAA,EAE9DE,UAAU,CAACa,OAAO,CAACd,GAAG,CAAER,IAAS,eACjC5G,KAAA,CAAChB,GAAG,EAEHuI,EAAE,CAAE,CACHjB,QAAQ,CAAE,UAAU,CACpBoB,OAAO,CAAE,MAAM,CACfK,cAAc,CAAE,QAAQ,CACxBJ,UAAU,CAAE,QAAQ,CACpB,sBAAsB,CAAE,CACvB;AACAQ,OAAO,CAAE,CACV,CACD,CAAE,CACFH,YAAY,CAAEA,CAAA,GAAMrE,qBAAqB,CAAC0D,UAAU,CAAC9B,EAAE,CAAE,CACzD6C,YAAY,CAAEA,CAAA,GAAM3E,eAAe,CAAC,EAAE,CAAE,CAAA0D,QAAA,eAExCrH,IAAA,CAACb,MAAM,EACNsG,EAAE,CAAEqB,IAAI,CAACrB,EAAG,CACZ8C,OAAO,CAAE,WAAY,CACrBd,EAAE,CAAE,CACHe,UAAU,CAAE,0BAA0B,CACtCT,OAAO,CAAE,uBAAuB,CAChCU,YAAY,CAAE,KAAK,CACnBtD,KAAK,CAAE2B,IAAI,CAACH,KAAK,CAACxB,KAAK,EAAI,MAAM,CACjCuD,MAAM,CAAE5B,IAAI,CAACH,KAAK,CAACgC,WAAW,CAC3B,aAAa7B,IAAI,CAACH,KAAK,CAACgC,WAAW,EAAE,CACrC,MAAM,CACTC,aAAa,CAAE,MAAM,CACrBvD,eAAe,CAAEyB,IAAI,CAACH,KAAK,CAACtB,eAAe,CAC3CwD,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,CACVA,SAAS,CAAE,iBACZ,CACD,CAAE,CACFC,OAAO,CAAGX,CAAC,EAAK9D,WAAW,CAAC8D,CAAC,CAAErB,IAAI,CAACrB,EAAE,CAAE,CAAA4B,QAAA,cAExCrH,IAAA,CAACX,UAAU,EAACsH,KAAK,CAAE,CAAExB,KAAK,CAAErD,YAAa,CAAE,CAAAuF,QAAA,CAAEP,IAAI,CAACP,IAAI,CAAa,CAAC,CAC7D,CAAC,CAGRgB,UAAU,CAACa,OAAO,CAACW,MAAM,CAAG,CAAC,eAC7B/I,IAAA,CAACV,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZC,SAAS,CAAC,aAAa,CACvBxB,EAAE,CAAE,CACHjB,QAAQ,CAAE,UAAU,CACpB0C,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,kBAAkB,CAC7B/D,eAAe,CAAE,MAAM,CACvBwD,SAAS,CAAE,gCAAgC,CAC3CR,OAAO,CAAE,CAAC,CAAE;AACZgB,UAAU,CAAE,mBAAmB,CAAE;AACjCC,MAAM,CAAE,CAAC,CACTvB,OAAO,CAAE,gBAAgB,CACzB,SAAS,CAAE,CACV1C,eAAe,CAAE,MAAM,CACvBwD,SAAS,CAAE,iBACZ,CAAC,CACDU,IAAI,CAAE,CACL7B,MAAM,CAAE,MACT,CAAC,CACD,OAAO,CAAE,CACRC,KAAK,CAAE,MAAM,CAAE;AACfD,MAAM,CAAE,MAAM,CAAE;AAChB8B,IAAI,CAAE,CACLC,IAAI,CAAC,SACN,CACD,CACD,CAAE,CACFX,OAAO,CAAEA,CAAA,GAAM3H,YAAY,CAACoG,UAAU,CAAC9B,EAAE,CAAEqB,IAAI,CAACrB,EAAE,CAAE,CAAA4B,QAAA,cAEpDrH,IAAA,SAAM0J,uBAAuB,CAAE,CAAEC,MAAM,CAAElK,UAAW,CAAE,CAAE,CAAC,CAC9C,CACZ,GAxEIqH,IAAI,CAACrB,EAyEN,CACL,CAAC,CAED,CAAClE,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,GAChF+F,UAAU,CAACa,OAAO,CAACW,MAAM,CAAG,CAAC,cAC5B/I,IAAA,CAACV,UAAU,EACV2J,SAAS,CAAC,iBAAiB,CAC3BxB,EAAE,CAAE,CACHpC,eAAe,CAAE,SAAS,CAC1BuE,MAAM,CAAE,SAAS,CACjBN,MAAM,CAAE,IAAI,CACZvB,OAAO,CAAE,gBAAgB,CACzBH,OAAO,CAAE,MAAM,CACf,SAAS,CAAE,CACVvC,eAAe,CAAE,SAClB,CACD,CACA;AAAA,CACAyD,OAAO,CAAEA,CAAA,GAAM,CACd1C,kBAAkB,CAACmB,UAAU,CAAC9B,EAAE,CAAC,CACjC,GAAI3E,OAAO,CAAE,CACZA,OAAO,CAAC,CAAC,CACV,CACD,CAAE,CAAAuG,QAAA,cAEFrH,IAAA,CAACH,OAAO,EACPgK,QAAQ,CAAC,OAAO,CAChBpC,EAAE,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACtB,CAAC,CACS,CAAC,CACV,IAAI,EACJ,CAAC,CAER,CAAC,CAAC,cACFnF,IAAA,CAACZ,OAAO,EACH6J,SAAS,CAAC,eAAe,CAC7BxD,EAAE,CAAEA,EAAG,CACPF,IAAI,CAAEA,IAAK,CACXvC,QAAQ,CAAEA,QAAS,CACnB8G,OAAO,CAAElF,WAAY,CACrBmF,YAAY,CAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,MACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACb,CAAE,CACFxC,EAAE,CAAE,CACH0C,SAAS,CAAExJ,QAAQ,CAAG,kBAAkB,CAAG,MAC5C,CACA;AAAA,CAAA0G,QAAA,cAEAnH,KAAA,CAAChB,GAAG,EACHuI,EAAE,CAAE,CACHG,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KACV,CAAE,CAAAV,QAAA,eA+CFrH,IAAA,CAACT,OAAO,EAAC6K,KAAK,CAAE7H,SAAS,CAAC,UAAU,CAAE,CAAC8H,KAAK,MAAAhD,QAAA,cAC3CrH,IAAA,CAACV,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZF,OAAO,CAAE9B,sBAAuB,CAAAK,QAAA,cAEhCrH,IAAA,SAAM0J,uBAAuB,CAAE,CAAEC,MAAM,CAAEhK,YAAa,CAAE,CAAE,CAAC,CAChD,CAAC,CACL,CAAC,CACT,CAAC4B,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,gBAC/ExB,IAAA,CAACT,OAAO,EAAC6K,KAAK,CAAE7H,SAAS,CAAC,kBAAkB,CAAE,CAAC8H,KAAK,MAAAhD,QAAA,cACnDrH,IAAA,CAACV,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZF,OAAO,CAAE7D,0BAA2B,CAAAoC,QAAA,cAEpCrH,IAAA,SACC2G,KAAK,CAAE,CACNtB,eAAe,CAAEnC,aAAa,CAC9BuF,YAAY,CAAE,MAAM,CACpBd,KAAK,CAAE,MAAM,CACbD,MAAM,CAAE,MAAM,CACdE,OAAO,CAAE,cAAc,CACvBuC,SAAS,CAAE,MACZ,CAAE,CACF,CAAC,CACS,CAAC,CACL,CACT,CACA,CAAC5I,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,gBAC/ExB,IAAA,CAACT,OAAO,EAAC6K,KAAK,CAAExJ,eAAe,CAAG2B,SAAS,CAAC,4CAA4C,CAAC,CAAGA,SAAS,CAAC,cAAc,CAAE,CAAC8H,KAAK,MAAAhD,QAAA,cAC3HrH,IAAA,CAACV,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZF,OAAO,CAAEA,CAAA,GAAM,CACd9H,oBAAoB,CAAC4C,kBAAkB,CAAC,CACxC,GAAI9C,OAAO,CAAE,CACZA,OAAO,CAAC,CAAC,CACV,CACD,CAAE,CACFwJ,QAAQ,CAAE1J,eAAgB,CAAAyG,QAAA,cAE1BrH,IAAA,SACC0J,uBAAuB,CAAE,CAAEC,MAAM,CAAEjK,QAAS,CAAE,CAC9CiH,KAAK,CAAE,CAAE0B,OAAO,CAAEzH,eAAe,CAAG,GAAG,CAAG,CAAE,CAAE,CAC9C,CAAC,CACS,CAAC,CACL,CACT,CACA,CAACW,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,gBAC/ExB,IAAA,CAACT,OAAO,EAAC6K,KAAK,CAAE7H,SAAS,CAAC,eAAe,CAAE,CAAC8H,KAAK,MAAAhD,QAAA,cAChDrH,IAAA,CAACV,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZF,OAAO,CAAE/B,oBAAqB,CAAAM,QAAA,cAE9BrH,IAAA,SACC0J,uBAAuB,CAAE,CAAEC,MAAM,CAAElK,UAAW,CAAE,CAChDkH,KAAK,CAAE,CAAEwD,SAAS,CAAE,MAAO,CAAE,CAC7B,CAAC,CACS,CAAC,CACL,CACT,EACG,CAAC,CACE,CAAC,cAGVnK,IAAA,CAACZ,OAAO,EACPmG,IAAI,CAAEI,eAAgB,CACtB3C,QAAQ,CAAEI,mBAAoB,CAC9B0G,OAAO,CAAExE,sBAAuB,CAChCyE,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CAAA5C,QAAA,cAEFnH,KAAA,CAAChB,GAAG,EAAAmI,QAAA,eACHrH,IAAA,CAACR,YAAY,EACZ2F,KAAK,CAAEyB,qBAAsB,CAC7B2D,QAAQ,CAAErF,iBAAkB,CAC5B,CAAC,cACFlF,IAAA,UAAAqH,QAAA,CACE;AACP;AACA;AACA;AACA,KAAK,CACO,CAAC,EACJ,CAAC,CACE,CAAC,EACT,CAAC,CAEL,CAAC,CAED,cAAe,CAAAhH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}