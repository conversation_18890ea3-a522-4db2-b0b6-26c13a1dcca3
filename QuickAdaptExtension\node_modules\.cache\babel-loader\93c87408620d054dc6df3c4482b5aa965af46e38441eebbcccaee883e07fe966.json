{"ast": null, "code": "import React from'react';import{DotLottieReact}from'@lottiefiles/dotlottie-react';import'./LottieSpinner.css';import{jsx as _jsx}from\"react/jsx-runtime\";const LottieSpinner=_ref=>{let{size=60,className='',backgroundColor='transparent'}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:`lottie-spinner-container ${className}`,style:{width:size,height:size,backgroundColor:backgroundColor,borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(DotLottieReact,{src:\"https://lottie.host/1af42d2f-54f8-4b9f-b8fc-433ef9ab57cc/ZHCCOvYhBx.lottie\",loop:true,autoplay:true,className:\"lottie-spinner\"})});};export default LottieSpinner;", "map": {"version": 3, "names": ["React", "DotLottieReact", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "size", "className", "backgroundColor", "style", "width", "height", "borderRadius", "display", "alignItems", "justifyContent", "children", "src", "loop", "autoplay"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/LottieSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { DotLottieReact } from '@lottiefiles/dotlottie-react';\nimport './LottieSpinner.css';\n\ninterface LottieSpinnerProps {\n  size?: number;\n  className?: string;\n  backgroundColor?: string;\n}\n\nconst LottieSpinner: React.FC<LottieSpinnerProps> = ({\n  size = 60,\n  className = '',\n  backgroundColor = 'transparent'\n}) => {\n  return (\n    <div\n      className={`lottie-spinner-container ${className}`}\n      style={{\n        width: size,\n        height: size,\n        backgroundColor: backgroundColor,\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      }}\n    >\n      <DotLottieReact\n        src=\"https://lottie.host/1af42d2f-54f8-4b9f-b8fc-433ef9ab57cc/ZHCCOvYhBx.lottie\"\n        loop\n        autoplay\n        className=\"lottie-spinner\"\n      />\n    </div>\n  );\n};\n\nexport default LottieSpinner;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,8BAA8B,CAC7D,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAQ7B,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAI9C,IAJ+C,CACnDC,IAAI,CAAG,EAAE,CACTC,SAAS,CAAG,EAAE,CACdC,eAAe,CAAG,aACpB,CAAC,CAAAH,IAAA,CACC,mBACEF,IAAA,QACEI,SAAS,CAAE,4BAA4BA,SAAS,EAAG,CACnDE,KAAK,CAAE,CACLC,KAAK,CAAEJ,IAAI,CACXK,MAAM,CAAEL,IAAI,CACZE,eAAe,CAAEA,eAAe,CAChCI,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAC,QAAA,cAEFb,IAAA,CAACF,cAAc,EACbgB,GAAG,CAAC,4EAA4E,CAChFC,IAAI,MACJC,QAAQ,MACRZ,SAAS,CAAC,gBAAgB,CAC3B,CAAC,CACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}